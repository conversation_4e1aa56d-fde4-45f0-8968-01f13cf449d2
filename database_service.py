# database_service.py
import sqlite3
import logging
import time
import json
from datetime import datetime
from typing import List, Dict, Optional, Any

# 配置日志
logger = logging.getLogger(__name__)
# 基本日志配置，如果主程序中未配置
if not logger.handlers:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

DATABASE_NAME = "app_data.db" # 可以考虑放到 config.py

class DatabaseService:
    def __init__(self, db_name=DATABASE_NAME):
        self.db_name = db_name
        self._create_tables()

    def _get_connection(self):
        """Gets a database connection."""
        try:
            # check_same_thread=False: Use with caution.
            # Consider a dedicated DB thread or queue for concurrent access.
            conn = sqlite3.connect(self.db_name, check_same_thread=False)
            conn.row_factory = sqlite3.Row # Access columns by name
            return conn
        except sqlite3.Error as e:
            logger.error(f"Database connection failed: {e}", exc_info=True)
            raise

    def _create_tables(self):
        """Creates database tables if they do not exist."""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # Token metadata table
            sql_tokens_metadata = """
            CREATE TABLE IF NOT EXISTS tokens_metadata (
                address TEXT PRIMARY KEY,
                symbol TEXT,
                name TEXT,
                decimals INTEGER,
                source TEXT, -- e.g., 'trend', 'historical_api', 'birdeye_ohlcv', 'user_added'
                created_at_api TEXT, -- ISO format string from API
                logo_uri TEXT,
                description TEXT,
                last_fetched_metadata REAL, -- Unix timestamp
                other_metadata_json TEXT
            );
            """
            cursor.execute(sql_tokens_metadata)
            logger.info("Table 'tokens_metadata' checked/created.")

            # OHLCV K-line data table
            sql_ohlcv_data = """
            CREATE TABLE IF NOT EXISTS ohlcv_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token_address TEXT NOT NULL,
                timeframe TEXT NOT NULL, -- e.g., '1m', '5m', '1h', '1d'
                timestamp INTEGER NOT NULL, -- Unix timestamp (seconds) for the start of the candle
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume REAL NOT NULL,
                datetime_str TEXT, -- Human-readable datetime string (YYYY-MM-DD HH:MM:SS)
                fetched_at REAL, -- Unix timestamp when this record was fetched/updated
                FOREIGN KEY (token_address) REFERENCES tokens_metadata (address) ON DELETE CASCADE,
                UNIQUE (token_address, timeframe, timestamp) -- Ensures no duplicate candles
            );
            """
            cursor.execute(sql_ohlcv_data)
            logger.info("Table 'ohlcv_data' checked/created.")
            
            # Indices for ohlcv_data
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_ohlcv_token_timeframe_timestamp ON ohlcv_data (token_address, timeframe, timestamp DESC);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_ohlcv_token_address ON ohlcv_data (token_address);")
            logger.info("'ohlcv_data' table indices checked/created.")

            # OHLCV Cache table for raw API responses (full history for backtesting)
            sql_ohlcv_cache = """
            CREATE TABLE IF NOT EXISTS ohlcv_cache (
                token_address TEXT NOT NULL,
                timeframe TEXT NOT NULL, 
                source TEXT NOT NULL, -- e.g., 'historical_full_1d_1m' to signify 1 day of 1m data
                ohlcv_json_data TEXT NOT NULL,
                last_updated REAL NOT NULL, -- Unix timestamp
                PRIMARY KEY (token_address, timeframe, source)
            );
            """
            cursor.execute(sql_ohlcv_cache)
            logger.info("Table 'ohlcv_cache' checked/created.")

            conn.commit()
        except sqlite3.Error as e:
            logger.error(f"Error creating tables: {e}", exc_info=True)
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    # --- 后续会在这里添加数据存取方法 ---

    def save_token_metadata(self, token_data: Dict[str, Any], source: str):
        """Saves metadata for a single token. 
        Uses INSERT OR REPLACE to handle conflicts gracefully.
        'source' helps identify the origin for logging purposes.
        """
        if not token_data or not token_data.get('address'):
            logger.warning("save_token_metadata: Invalid token_data or missing address.")
            return

        address = token_data.get('address')
        # Provide default empty strings for symbol and name if not present in token_data, 
        # especially if it's a placeholder from OHLCV fetch.
        symbol = token_data.get('symbol', '') 
        name = token_data.get('name', '')     
        decimals = token_data.get('decimals')
        created_at_api = token_data.get('createdAt') or token_data.get('created_at_api')
        logo_uri = token_data.get('logoURI') or token_data.get('logo_uri')
        description = token_data.get('description')
        
        other_metadata = {}
        known_fields = {'address', 'symbol', 'name', 'decimals', 'createdAt', 'created_at_api', 
                        'logoURI', 'logo_uri', 'description', 'source', 
                        'tokenAddress', 'id', 'identifier'} 
        
        for key, value in token_data.items():
            if key not in known_fields:
                other_metadata[key] = value
        other_metadata_json = json.dumps(other_metadata) if other_metadata else None

        # 使用 INSERT OR REPLACE 来处理冲突，确保数据能够成功保存
        sql = """
        INSERT OR REPLACE INTO tokens_metadata 
            (address, symbol, name, decimals, source, created_at_api, logo_uri, description, 
             last_fetched_metadata, other_metadata_json)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            current_timestamp = time.time()
            cursor.execute(sql, (address, symbol, name, decimals, source, created_at_api, 
                                 logo_uri, description, current_timestamp, other_metadata_json))
            conn.commit()
            
            if cursor.rowcount > 0:
                logger.debug(f"Metadata saved/updated for {address} (Source: {source})")
            else:
                logger.debug(f"No changes for metadata of {address} (Source: {source})")
                
        except sqlite3.Error as e:
            logger.error(f"Failed to save metadata for {address} (Source: {source}): {e}", exc_info=True)
            if conn:
                conn.rollback()
            # 不再抛出异常，而是优雅地处理
        finally:
            if conn:
                conn.close()

    def save_ohlcv_batch(self, token_address: str, timeframe: str, ohlcv_list: List[Dict[str, Any]]):
        """Batch saves OHLCV data. Ignores duplicates due to UNIQUE constraint on (token_address, timeframe, timestamp)."""
        if not ohlcv_list:
            logger.info(f"save_ohlcv_batch: No OHLCV data provided for {token_address} ({timeframe}).")
            return

        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            current_fetch_time = time.time()
            
            data_to_insert = []
            for item in ohlcv_list:
                if not all(k in item for k in ['timestamp', 'open', 'high', 'low', 'close', 'volume']):
                    logger.warning(f"Skipping OHLCV item due to missing fields for {token_address}: {item}")
                    continue
                
                dt_str = item.get('datetime')
                if not dt_str and item.get('timestamp') is not None: # Ensure timestamp exists
                    try:
                        # Ensure timestamp is treated as number if it's string
                        ts = int(item['timestamp']) 
                        dt_str = datetime.fromtimestamp(ts).strftime('%Y-%m-%d %H:%M:%S')
                    except (ValueError, TypeError, OSError) as e_ts: # Catch more specific errors
                        logger.warning(f"Could not convert timestamp {item['timestamp']} to datetime string for {token_address}: {e_ts}")
                        dt_str = None 
                
                data_to_insert.append((
                    token_address, timeframe, 
                    item['timestamp'], item['open'], item['high'], 
                    item['low'], item['close'], item['volume'], 
                    dt_str, current_fetch_time
                ))

            if not data_to_insert:
                logger.info(f"No valid OHLCV data to insert for {token_address} ({timeframe}) after validation.")
                return

            sql_insert_ohlcv = """
            INSERT OR IGNORE INTO ohlcv_data 
            (token_address, timeframe, timestamp, open, high, low, close, volume, datetime_str, fetched_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            """
            
            cursor.executemany(sql_insert_ohlcv, data_to_insert)
            conn.commit()
            # cursor.rowcount might not be reliable for executemany with INSERT OR IGNORE across all sqlite versions for exact ignored count.
            # It typically returns the number of rows *affected* by the last executed statement.
            # For INSERT OR IGNORE, this means rows actually inserted.
            logger.info(f"Attempted to save {len(data_to_insert)} OHLCV records for {token_address} ({timeframe}). {cursor.rowcount} new records inserted.")
            
        except sqlite3.Error as e:
            logger.error(f"Batch save OHLCV data failed for {token_address} ({timeframe}): {e}", exc_info=True)
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def get_token_metadata(self, address: str) -> Optional[Dict[str, Any]]:
        """Retrieves metadata for a single token by its address.
        Returns a dictionary or None if not found.
        """
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tokens_metadata WHERE address = ?", (address,))
            row = cursor.fetchone()
            return dict(row) if row else None
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve metadata for {address}: {e}", exc_info=True)
            return None
        finally:
            if conn:
                conn.close()
    
    def get_ohlcv_data(self, token_address: str, timeframe: str, 
                       start_timestamp: Optional[int] = None, 
                       end_timestamp: Optional[int] = None, 
                       limit: Optional[int] = None,
                       ascending: bool = False) -> List[Dict[str, Any]]:
        """Retrieves OHLCV data for a given token and timeframe, ordered by timestamp.
        
        Args:
            token_address: The token's address.
            timeframe: The timeframe (e.g., '1m', '1h').
            start_timestamp: Optional Unix timestamp to filter data from.
            end_timestamp: Optional Unix timestamp to filter data up to.
            limit: Optional limit on the number of records returned.
            ascending: If True, data is ordered by timestamp ASC, otherwise DESC (default).
        Returns:
            A list of dictionaries, each representing an OHLCV candle.
        """
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            params = [token_address, timeframe]
            sql = "SELECT * FROM ohlcv_data WHERE token_address = ? AND timeframe = ?"
            
            if start_timestamp is not None:
                sql += " AND timestamp >= ?"
                params.append(start_timestamp)
            if end_timestamp is not None:
                sql += " AND timestamp <= ?"
                params.append(end_timestamp)
            
            sql += " ORDER BY timestamp " + ("ASC" if ascending else "DESC")
            
            if limit is not None:
                sql += " LIMIT ?"
                params.append(limit)
            
            cursor.execute(sql, tuple(params))
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve OHLCV data for {token_address} ({timeframe}): {e}", exc_info=True)
            return []
        finally:
            if conn:
                conn.close()

    def get_latest_ohlcv_timestamp(self, token_address: str, timeframe: str) -> Optional[int]:
        """Retrieves the latest (max) OHLCV data timestamp for a token and timeframe.
        Returns an integer timestamp or None if no data exists.
        """
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT MAX(timestamp) FROM ohlcv_data WHERE token_address = ? AND timeframe = ?", 
                           (token_address, timeframe))
            result = cursor.fetchone()
            return result[0] if result and result[0] is not None else None
        except sqlite3.Error as e:
            logger.error(f"Failed to get latest OHLCV timestamp for {token_address} ({timeframe}): {e}", exc_info=True)
            return None
        finally:
            if conn:
                conn.close()

    def get_all_tokens_metadata(self, source: Optional[str] = None) -> List[Dict[str, Any]]:
        """Retrieves all token metadata, optionally filtered by source.
        Reconstructs a more complete dictionary by merging 'other_metadata_json'.
        Ensures 'tokenAddress' field is present, aliasing 'address'.
        """
        conn = None
        tokens_list = []
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            sql = "SELECT * FROM tokens_metadata"
            params = []
            if source:
                sql += " WHERE source = ?"
                params.append(source)
            
            cursor.execute(sql, tuple(params))
            rows = cursor.fetchall()
            
            for row in rows:
                token_dict = dict(row) # Convert sqlite3.Row to dict
                
                # Merge other_metadata_json if it exists
                other_json = token_dict.get('other_metadata_json')
                if other_json:
                    try:
                        other_data = json.loads(other_json)
                        token_dict.update(other_data) # Merge
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to decode other_metadata_json for {token_dict.get('address')}: {e}")
                # del token_dict['other_metadata_json'] # Optionally remove after merging

                # Ensure 'tokenAddress' compatibility (used by TrendWidget etc.)
                if 'address' in token_dict and 'tokenAddress' not in token_dict:
                    token_dict['tokenAddress'] = token_dict['address']
                
                tokens_list.append(token_dict)
            
            return tokens_list
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve all token metadata (source: {source}): {e}", exc_info=True)
            return []
        finally:
            if conn:
                conn.close()

    def save_historical_tokens_list(self, tokens_list: List[Dict[str, Any]]):
        """保存历史代币列表到缓存
        
        Args:
            tokens_list: 历史代币列表数据
        """
        if not tokens_list:
            logger.warning("save_historical_tokens_list: 没有提供代币列表数据")
            return
        
        conn = None
        try:
            # 将列表转换为JSON字符串
            tokens_json = json.dumps(tokens_list)
            current_timestamp = time.time()
            
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 使用特殊的token_address作为历史代币列表的标识
            special_address = "__HISTORICAL_TOKENS_LIST__"
            timeframe = "list"  # 特殊的timeframe标识
            source = "historical_tokens_api"
            
            sql = """
            INSERT OR REPLACE INTO ohlcv_cache 
                (token_address, timeframe, source, ohlcv_json_data, last_updated)
            VALUES (?, ?, ?, ?, ?);
            """
            cursor.execute(sql, (special_address, timeframe, source, tokens_json, current_timestamp))
            conn.commit()
            logger.info(f"历史代币列表已保存到缓存，包含 {len(tokens_list)} 个代币")
            
        except sqlite3.Error as e:
            logger.error(f"保存历史代币列表到缓存失败: {e}", exc_info=True)
            if conn:
                conn.rollback()
        except json.JSONEncodeError as e:
            logger.error(f"序列化历史代币列表失败: {e}", exc_info=True)
        finally:
            if conn:
                conn.close()
    
    def get_historical_tokens_list(self, max_age_seconds: int = 3600) -> Optional[List[Dict[str, Any]]]:
        """从缓存获取历史代币列表
        
        Args:
            max_age_seconds: 缓存的最大有效期（秒），默认1小时
            
        Returns:
            历史代币列表，如果缓存不存在或过期则返回None
        """
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # 使用特殊的标识符查询
            special_address = "__HISTORICAL_TOKENS_LIST__"
            timeframe = "list"
            source = "historical_tokens_api"
            
            cursor.execute("""
                SELECT ohlcv_json_data, last_updated 
                FROM ohlcv_cache 
                WHERE token_address = ? AND timeframe = ? AND source = ?
            """, (special_address, timeframe, source))
            
            row = cursor.fetchone()
            if row:
                last_updated = row['last_updated']
                current_time = time.time()
                
                # 检查缓存是否过期
                if current_time - last_updated > max_age_seconds:
                    logger.info(f"历史代币列表缓存已过期 (年龄: {current_time - last_updated:.0f}秒)")
                    return None
                
                # 解析并返回数据
                tokens_list = json.loads(row['ohlcv_json_data'])
                logger.info(f"从缓存加载了 {len(tokens_list)} 个历史代币 (年龄: {current_time - last_updated:.0f}秒)")
                return tokens_list
            else:
                logger.debug("缓存中没有找到历史代币列表")
                return None
                
        except sqlite3.Error as e:
            logger.error(f"从缓存获取历史代币列表失败: {e}", exc_info=True)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析历史代币列表JSON失败: {e}", exc_info=True)
            return None
        finally:
            if conn:
                conn.close()
    
    def clear_historical_tokens_cache(self):
        """清除历史代币列表缓存"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            special_address = "__HISTORICAL_TOKENS_LIST__"
            timeframe = "list"
            source = "historical_tokens_api"
            
            cursor.execute("""
                DELETE FROM ohlcv_cache 
                WHERE token_address = ? AND timeframe = ? AND source = ?
            """, (special_address, timeframe, source))
            
            conn.commit()
            if cursor.rowcount > 0:
                logger.info("历史代币列表缓存已清除")
            else:
                logger.debug("没有找到需要清除的历史代币列表缓存")
                
        except sqlite3.Error as e:
            logger.error(f"清除历史代币列表缓存失败: {e}", exc_info=True)
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def save_ohlcv_to_cache(self, token_address: str, timeframe: str, source: str, ohlcv_list: List[Dict[str, Any]]):
        """Saves a list of OHLCV data points as a single JSON string to the cache.
        Overwrites if record with the same (token_address, timeframe, source) already exists.
        """
        if not ohlcv_list:
            logger.info(f"save_ohlcv_to_cache: No OHLCV data provided for {token_address} ({timeframe}, {source}).")
            return

        conn = None
        try:
            ohlcv_json_data = json.dumps(ohlcv_list)
            current_timestamp = time.time()
            
            conn = self._get_connection()
            cursor = conn.cursor()
            
            sql = """
            INSERT OR REPLACE INTO ohlcv_cache 
                (token_address, timeframe, source, ohlcv_json_data, last_updated)
            VALUES (?, ?, ?, ?, ?);
            """
            cursor.execute(sql, (token_address, timeframe, source, ohlcv_json_data, current_timestamp))
            conn.commit()
            logger.info(f"OHLCV data for {token_address} ({timeframe}, {source}) saved to cache. ({len(ohlcv_list)} records)")
        except sqlite3.Error as e:
            logger.error(f"Failed to save OHLCV to cache for {token_address} ({timeframe}, {source}): {e}", exc_info=True)
            if conn:
                conn.rollback()
        except json.JSONDecodeError as e_json: # Should not happen if ohlcv_list is well-formed
            logger.error(f"Failed to serialize OHLCV list to JSON for {token_address} ({timeframe}, {source}): {e_json}", exc_info=True)
        finally:
            if conn:
                conn.close()

    def get_ohlcv_from_cache(self, token_address: str, timeframe: str, source: str) -> Optional[List[Dict[str, Any]]]:
        """Retrieves OHLCV data list (from JSON string) from the cache.
        Returns a list of dictionaries or None if not found or error occurs.
        """
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT ohlcv_json_data FROM ohlcv_cache WHERE token_address = ? AND timeframe = ? AND source = ?", 
                           (token_address, timeframe, source))
            row = cursor.fetchone()
            if row and row['ohlcv_json_data']:
                logger.debug(f"OHLCV data found in cache for {token_address} ({timeframe}, {source}).")
                return json.loads(row['ohlcv_json_data'])
            else:
                logger.debug(f"No OHLCV data found in cache for {token_address} ({timeframe}, {source}).")
                return None
        except sqlite3.Error as e:
            logger.error(f"Failed to retrieve OHLCV from cache for {token_address} ({timeframe}, {source}): {e}", exc_info=True)
            return None
        except json.JSONDecodeError as e_json:
            logger.error(f"Failed to deserialize OHLCV JSON from cache for {token_address} ({timeframe}, {source}): {e_json}", exc_info=True)
            return None # Or consider deleting the corrupted cache entry
        finally:
            if conn:
                conn.close()

if __name__ == '__main__':
    # 测试数据库初始化
    db_service = DatabaseService(db_name="test_app_data.db") # 使用测试数据库名
    logger.info("Test DatabaseService initialized.")
    # 你可以手动检查文件是否生成，或使用DB Browser for SQLite等工具查看表结构
    # 清理测试数据库 (可选)
    # import os
    # if os.path.exists("test_app_data.db"):
    #     os.remove("test_app_data.db")
    #     logger.info("测试数据库已删除。") 
