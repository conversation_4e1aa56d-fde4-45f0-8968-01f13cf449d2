#!/usr/bin/env python3
"""
TrendTrader 客户端问题修复工具
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path

def create_client_package():
    """创建包含配置工具的客户端包"""
    print("🔧 创建客户端修复包...")
    
    # 创建客户端工具目录
    client_tools_dir = Path("客户端工具")
    client_tools_dir.mkdir(exist_ok=True)
    
    # 复制配置工具
    files_to_copy = [
        "配置API密钥.py",
        "客户端配置指南.md"
    ]
    
    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, client_tools_dir / file_name)
            print(f"✅ 复制: {file_name}")
    
    # 创建快速修复脚本
    quick_fix_script = client_tools_dir / "快速修复401错误.py"
    with open(quick_fix_script, 'w', encoding='utf-8') as f:
        f.write('''#!/usr/bin/env python3
"""
TrendTrader 401错误快速修复工具
"""

import os
import sys
from pathlib import Path

def main():
    print("🚨 TrendTrader 401错误修复工具")
    print("=" * 40)
    
    print("❌ 如果您看到以下错误:")
    print("   - 401 Client Error: Unauthorized")
    print("   - 数据获取失败")
    print("   - API来源: Birdeye API")
    print()
    print("✅ 这是因为缺少API密钥，按以下步骤解决:")
    print()
    
    print("📋 解决步骤:")
    print("1. 访问: https://docs.birdeye.so/")
    print("2. 注册免费账户并获取API密钥")
    print("3. 运行配置工具: python 配置API密钥.py")
    print("4. 重启TrendTrader应用程序")
    print()
    
    # 检查是否存在配置工具
    config_tool = Path("配置API密钥.py")
    if config_tool.exists():
        choice = input("🔧 是否现在运行配置工具? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            try:
                subprocess.run([sys.executable, str(config_tool)], check=True)
            except Exception as e:
                print(f"❌ 运行配置工具失败: {e}")
    else:
        print("⚠️  配置工具不存在，请手动创建.env文件")
        print("   文件内容: BIRDEYE_API_KEY=your_api_key_here")
    
    print("\\n🎯 完成后重启TrendTrader即可正常使用！")

if __name__ == "__main__":
    main()
''')
    
    # 创建README文件
    readme_file = client_tools_dir / "README.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write("""TrendTrader 客户端工具包

如果您遇到401错误或数据获取失败的问题，请按以下步骤操作：

1. 双击运行: 快速修复401错误.py
2. 按照提示获取并配置API密钥
3. 重启TrendTrader应用程序

详细说明请查看: 客户端配置指南.md

技术支持：如果问题仍然存在，请联系开发者
""")
    
    print(f"✅ 客户端工具包创建完成: {client_tools_dir}")
    return client_tools_dir

def update_build_script():
    """更新打包脚本，包含客户端工具"""
    print("🔄 更新打包脚本...")
    
    # 读取现有的simple_build.py
    build_script = Path("simple_build.py")
    if not build_script.exists():
        print("❌ 找不到simple_build.py")
        return False
    
    with open(build_script, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在add-data部分添加客户端工具
    if "--add-data=客户端工具:客户端工具" not in content:
        # 找到最后一个--add-data行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "--add-data=" in line and "okx_dex_client.py" in line:
                # 在下一行插入客户端工具
                lines.insert(i + 1, '        "--add-data=客户端工具:客户端工具",')
                break
        
        content = '\n'.join(lines)
        
        with open(build_script, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 打包脚本已更新")
    else:
        print("✅ 打包脚本已包含客户端工具")
    
    return True

def create_distribution_package():
    """创建完整的分发包"""
    print("📦 创建完整分发包...")
    
    # 1. 创建客户端工具包
    client_tools_dir = create_client_package()
    
    # 2. 更新打包脚本
    update_build_script()
    
    # 3. 重新打包
    print("🔨 重新打包应用程序...")
    try:
        result = subprocess.run([sys.executable, "simple_build.py"], 
                              check=True, capture_output=True, text=True)
        print("✅ 重新打包成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ 重新打包失败: {e}")
        return False
    
    # 4. 复制客户端工具到dist目录
    dist_dir = Path("dist")
    if dist_dir.exists():
        dist_tools_dir = dist_dir / "客户端工具"
        if dist_tools_dir.exists():
            shutil.rmtree(dist_tools_dir)
        shutil.copytree(client_tools_dir, dist_tools_dir)
        print(f"✅ 客户端工具已复制到: {dist_tools_dir}")
    
    return True

def main():
    print("🛠️  TrendTrader 客户端问题修复工具")
    print("=" * 50)
    
    print("📋 此工具将:")
    print("1. 创建API密钥配置工具")
    print("2. 更新打包脚本")
    print("3. 重新打包应用程序")
    print("4. 创建完整的客户端分发包")
    print()
    
    choice = input("🚀 是否继续? (y/n): ").strip().lower()
    if choice not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        return
    
    if create_distribution_package():
        print("\n🎉 修复包创建成功！")
        print("\n📋 给客户的说明:")
        print("1. 将整个dist目录发送给客户")
        print("2. 客户运行: dist/客户端工具/快速修复401错误.py")
        print("3. 按提示配置API密钥")
        print("4. 重启TrendTrader应用程序")
        print("\n✅ 问题应该得到解决！")
    else:
        print("❌ 修复包创建失败")

if __name__ == "__main__":
    main() 