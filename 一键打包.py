#!/usr/bin/env python3
"""
一键打包脚本 - 自动完成打包和测试流程
"""

import os
import sys
import subprocess
import platform

def main():
    """主函数"""
    print("🚀 TrendTrader 一键打包工具")
    print("="*50)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    print("📋 开始自动化打包流程...")
    
    # 步骤1: 运行快速打包
    print("\n🔨 步骤 1/2: 执行打包...")
    try:
        result = subprocess.run([sys.executable, "quick_build.py"], 
                              check=True, capture_output=True, text=True)
        print("✅ 打包完成")
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败")
        print("错误信息:")
        print(e.stderr)
        return 1
    except FileNotFoundError:
        print("❌ 找不到 quick_build.py 文件")
        return 1
    
    # 步骤2: 运行测试
    print("\n🧪 步骤 2/2: 执行测试...")
    try:
        result = subprocess.run([sys.executable, "test_package.py"], 
                              check=True, capture_output=True, text=True)
        print("✅ 测试完成")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print("⚠️  测试发现问题")
        print(e.stdout)
        return 1
    except FileNotFoundError:
        print("❌ 找不到 test_package.py 文件")
        return 1
    
    # 显示最终结果
    print("\n🎉 一键打包完成！")
    print("="*50)
    
    # 显示文件位置
    system = platform.system()
    if system == "Windows":
        exe_file = "TrendTrader.exe"
    elif system == "Darwin":
        exe_file = "TrendTrader.app"
    else:
        exe_file = "TrendTrader"
    
    print(f"📁 可执行文件位置: dist/{exe_file}")
    print("\n📋 分发说明:")
    print("1. 将 dist/ 目录中的文件复制到目标电脑")
    print("2. 确保目标电脑有网络连接")
    print("3. 双击运行应用程序")
    
    if system == "Darwin":
        print("\n🍎 macOS 特别说明:")
        print("- 首次运行时右键点击选择'打开'")
        print("- 或在系统偏好设置中允许运行")
    elif system == "Windows":
        print("\n🪟 Windows 特别说明:")
        print("- 如果出现安全警告，选择'仍要运行'")
        print("- 确保防病毒软件不会阻止运行")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 