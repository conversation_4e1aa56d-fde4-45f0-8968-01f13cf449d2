#!/usr/bin/env python3
"""
OKX DEX 本地API Python客户端
用于量化交易程序
"""

import requests
import json
from typing import Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlencode
import logging

logger = logging.getLogger(__name__)


@dataclass
class QuoteRequest:
    chain_id: str
    from_token_address: str
    to_token_address: str
    amount: str
    slippage: str


@dataclass
class SwapRequest:
    chain_id: str
    from_token_address: str
    to_token_address: str
    amount: str
    slippage: str
    user_wallet_address: Optional[str] = None


@dataclass 
class TotalValueRequest:
    address: str
    chains: Optional[str] = None


@dataclass
class TokenBalancesRequest:
    address: str
    token_contract_addresses: Optional[list] = None  # 现在是对象数组格式
    exclude_risk_token: Optional[str] = None


@dataclass
class AllTokenBalancesRequest:
    address: str
    chains: str
    exclude_risk_token: Optional[str] = None


class OKXDexClient:
    """OKX DEX API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:9527"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=10)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=10)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            logger.error(f"OKX DEX API请求失败: {str(e)}")
            return {
                "success": False,
                "error": f"请求失败: {str(e)}",
                "timestamp": ""
            }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._make_request('GET', '/api/dex/health')
    
    def get_wallet_info(self) -> Dict[str, Any]:
        """获取钱包信息"""
        return self._make_request('GET', '/api/dex/wallet-info')
    
    def get_quote(self, quote_request: QuoteRequest) -> Dict[str, Any]:
        """获取交换报价"""
        data = {
            "chainId": quote_request.chain_id,
            "fromTokenAddress": quote_request.from_token_address,
            "toTokenAddress": quote_request.to_token_address,
            "amount": quote_request.amount,
            "slippage": quote_request.slippage
        }
        return self._make_request('POST', '/api/dex/quote', data)
    
    def execute_swap(self, swap_request: SwapRequest) -> Dict[str, Any]:
        """执行代币交换"""
        data = {
            "chainId": swap_request.chain_id,
            "fromTokenAddress": swap_request.from_token_address,
            "toTokenAddress": swap_request.to_token_address,
            "amount": swap_request.amount,
            "slippage": swap_request.slippage
        }

        if swap_request.user_wallet_address:
            data["userWalletAddress"] = swap_request.user_wallet_address
            
        return self._make_request('POST', '/api/dex/swap', data)
    
    def get_token_info(self, chain_id: str, token_address: str) -> Dict[str, Any]:
        """获取代币信息"""
        endpoint = f'/api/dex/token-info/{chain_id}/{token_address}'
        return self._make_request('GET', endpoint)
    
    def get_total_value(self, total_value_request: TotalValueRequest) -> Dict[str, Any]:
        """获取钱包总估值"""
        params = {"address": total_value_request.address}
        if total_value_request.chains:
            params["chains"] = total_value_request.chains
        
        endpoint = f'/api/dex/balance/total-value?{urlencode(params)}'
        return self._make_request('GET', endpoint)
    
    def get_supported_chains(self) -> Dict[str, Any]:
        """获取支持的链列表"""
        return self._make_request('GET', '/api/dex/balance/supported-chains')
    
    def get_token_balances(self, token_balances_request: TokenBalancesRequest) -> Dict[str, Any]:
        """获取特定代币余额"""
        data = {
            "address": token_balances_request.address
        }
        
        if token_balances_request.token_contract_addresses:
            data["tokenContractAddresses"] = token_balances_request.token_contract_addresses
        
        if token_balances_request.exclude_risk_token is not None:
            data["excludeRiskToken"] = token_balances_request.exclude_risk_token
        else:
            data["excludeRiskToken"] = "0"  # 默认过滤风险代币
        
        return self._make_request('POST', '/api/dex/balance/token-balances', data)
    
    def get_all_token_balances(self, all_token_balances_request: AllTokenBalancesRequest) -> Dict[str, Any]:
        """获取所有代币余额明细"""
        params = {
            "address": all_token_balances_request.address,
            "chains": all_token_balances_request.chains
        }
        
        if all_token_balances_request.exclude_risk_token is not None:
            params["excludeRiskToken"] = all_token_balances_request.exclude_risk_token
        
        endpoint = f'/api/dex/balance/all-token-balances?{urlencode(params)}'
        return self._make_request('GET', endpoint)
    
    def get_solana_balance(self, address: str, exclude_risk_token: bool = True) -> Dict[str, Any]:
        """获取Solana钱包余额 (便捷方法)"""
        params = {"excludeRiskToken": "0" if exclude_risk_token else "1"}
        endpoint = f'/api/dex/balance/solana/{address}?{urlencode(params)}'
        return self._make_request('GET', endpoint)
    
    def get_solana_token_balance(self, address: str, token_address: str, exclude_risk_token: bool = True) -> Dict[str, Any]:
        """获取Solana特定代币余额 (便捷方法)"""
        request = TokenBalancesRequest(
            address=address,
            token_contract_addresses=[{
                "chainIndex": "501",  # Solana链ID
                "tokenContractAddress": token_address
            }],
            exclude_risk_token="0" if exclude_risk_token else "1"
        )
        return self.get_token_balances(request) 