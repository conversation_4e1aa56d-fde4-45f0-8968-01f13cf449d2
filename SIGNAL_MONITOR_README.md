# 策略信号监控系统

基于QLocalSocket/QLocalServer的分布式策略信号聚合和实盘交易执行系统。

## 系统架构

```
┌─────────────────┐    QLocalSocket    ┌──────────────────┐
│ trending_window │ ───────────────────▶│                  │
└─────────────────┘                     │                  │
                                        │  signal_monitor  │ ────▶ 实盘交易执行
┌─────────────────┐    QLocalSocket    │   (监控服务器)    │
│holdings_panel   │ ───────────────────▶│                  │
└─────────────────┘                     └──────────────────┘
```

## 核心功能

### 1. 信号聚合
- **去重处理**：基于 `token_address_timestamp_signal_type` 的唯一标识
- **时间排序**：自动选择最新的信号执行
- **信号缓冲**：5秒缓冲区，批量处理信号
- **多源合并**：来自trending和holdings的信号统一处理

### 2. 执行控制
- **时间间隔控制**：每个代币最小60秒执行间隔
- **信号类型过滤**：只执行buy/sell信号，忽略hold/wait
- **重复执行防护**：记录每个代币的最后执行时间
- **执行历史记录**：完整的交易执行日志

### 3. 通信机制
- **持久连接**：客户端自动重连机制
- **数据可靠性**：未连接时信号缓存到队列
- **错误处理**：完善的异常处理和日志记录

## 文件说明

### 核心文件
- `signal_monitor.py` - 监控服务器主程序
- `signal_client.py` - 客户端通信模块
- `start_signal_monitor.py` - 服务器启动脚本

### 修改的文件
- `trending_window.py` - 添加信号客户端功能
- `holdings_panel_window.py` - 添加信号客户端功能

## 使用方法

### 1. 启动监控服务器
```bash
# 方法1：直接运行启动脚本
python start_signal_monitor.py

# 方法2：直接运行主程序
python signal_monitor.py
```

### 2. 启动客户端窗口
```bash
# 启动趋势窗口（会自动连接到监控服务器）
python trending_window.py

# 启动持仓窗口（会自动连接到监控服务器）
python holdings_panel_window.py
```

### 3. 监控界面功能
- **实时信号日志**：显示接收到的所有策略信号
- **执行历史表格**：显示已执行的交易记录
- **统计信息**：连接客户端数、信号数、执行统计
- **服务器控制**：重启服务器、清空日志

## 信号格式

### 信号数据结构
```python
@dataclass
class TradingSignal:
    token_address: str      # 代币地址
    symbol: str            # 代币符号
    signal_type: str       # 信号类型：buy, sell, hold, wait
    price: float           # 当前价格
    timestamp: int         # 信号时间戳
    strategy_name: str     # 策略名称
    source: str           # 来源：trending, holdings
    confidence: float     # 置信度 0-1
    metadata: Dict        # 额外元数据
```

### 置信度设置
- **trending窗口**：0.8（市场趋势信号）
- **holdings窗口**：0.9（持仓分析信号，优先级更高）

## 执行逻辑

### 信号处理流程
1. **接收信号** → 验证格式和数据有效性
2. **去重检查** → 基于唯一标识避免重复处理
3. **添加缓冲区** → 5秒批量处理窗口
4. **信号聚合** → 按代币分组，选择最新信号
5. **执行检查** → 验证时间间隔和信号类型
6. **交易执行** → 调用实盘交易接口（当前为模拟）
7. **记录历史** → 保存执行记录和统计

### 重复执行防护
```python
# 每个代币记录最后执行时间
last_execution_times = {
    "token_address_1": 1703123456,  # 上次执行时间戳
    "token_address_2": 1703123789,
}

# 新信号时间必须大于最后执行时间 + 最小间隔（60秒）
if signal.timestamp <= last_time + 60:
    return False  # 跳过执行
```

## 配置选项

### 服务器配置
```python
# signal_monitor.py 中的配置
SERVER_NAME = "trading_signal_monitor"
AGGREGATION_INTERVAL = 5000      # 5秒信号聚合间隔
MIN_EXECUTION_INTERVAL = 60      # 60秒最小执行间隔
MAX_BUFFER_SIZE = 1000          # 信号缓冲区大小
```

### 客户端配置
```python
# signal_client.py 中的配置
RECONNECT_INTERVAL = 5000        # 5秒重连间隔
MIN_SIGNAL_INTERVAL = 5          # 5秒最小信号发送间隔
MAX_PENDING_SIGNALS = 100        # 待发送信号队列大小
```

## 实盘交易集成

当前系统使用模拟交易执行。要集成真实交易，需要修改 `TradingExecutor._simulate_trade_execution()` 方法：

```python
def _simulate_trade_execution(self, signal: TradingSignal) -> bool:
    """替换为真实交易逻辑"""
    try:
        if signal.signal_type == 'buy':
            # 调用买入API
            result = your_trading_api.buy(
                token_address=signal.token_address,
                amount=calculate_buy_amount(),
                price=signal.price
            )
        elif signal.signal_type == 'sell':
            # 调用卖出API
            result = your_trading_api.sell(
                token_address=signal.token_address,
                amount=calculate_sell_amount(),
                price=signal.price
            )
        
        return result.success
        
    except Exception as e:
        logger.error(f"真实交易执行失败: {e}")
        return False
```

## 日志和监控

### 日志文件
- `signal_monitor.log` - 服务器运行日志
- 控制台输出 - 实时日志显示

### 监控指标
- 连接客户端数量
- 接收信号总数
- 执行成功/失败统计
- 客户端连接状态和信号数

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用
   - 确认QLocalServer权限
   - 查看错误日志

2. **客户端连接失败**
   - 确认服务器已启动
   - 检查socket权限
   - 查看重连日志

3. **信号未执行**
   - 检查信号类型（只执行buy/sell）
   - 验证时间间隔限制
   - 查看执行条件检查日志

### 调试模式
```python
# 启用详细日志
logging.getLogger('signal_monitor').setLevel(logging.DEBUG)
logging.getLogger('signal_client').setLevel(logging.DEBUG)
```

## 扩展功能

### 可能的增强
1. **Web监控界面** - 基于Web的监控面板
2. **策略权重** - 根据策略历史表现调整权重
3. **风险控制** - 集成止损、仓位管理
4. **多交易所支持** - 支持多个交易所的并行执行
5. **回测模式** - 历史数据回测功能

### 性能优化
1. **批量执行** - 同时执行多个代币的交易
2. **智能去重** - 更精细的信号去重算法
3. **缓存优化** - 代币信息和价格缓存
4. **异步执行** - 非阻塞的交易执行

## 安全考虑

1. **权限控制** - 限制本地socket访问权限
2. **数据验证** - 严格的输入数据验证
3. **错误隔离** - 单个交易失败不影响其他交易
4. **日志审计** - 完整的操作日志记录 