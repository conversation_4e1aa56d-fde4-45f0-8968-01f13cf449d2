#!/usr/bin/env python3
"""
快速启动智能持仓面板 - 优化版本
修复了颜色对比度和OKX API数据格式问题
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication
from holdings_panel_window import HoldingsWindow
from api_service import APIService

# 设置简洁的日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(name)s - %(message)s'
)

def main():
    """主函数"""
    print("🚀 启动智能持仓面板 (优化版)")
    print("✅ 修复了界面颜色对比度问题")
    print("✅ 修复了OKX API数据格式适配问题")
    print("✅ 支持自动钱包连接和策略分析")
    print("-" * 50)
    
    try:
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("智能持仓面板")
        
        # 创建API服务
        api_service = APIService()
        
        # 创建持仓面板
        window = HoldingsWindow(api_service=api_service)
        window.show()
        
        print("🎉 持仓面板已启动！")
        print("\n📋 界面说明:")
        print("• 白色背景，深色文字 - 高对比度")
        print("• 绿色 = 买入信号/正向变化")
        print("• 红色 = 卖出信号/负向变化")
        print("• 橙色 = 持有信号")
        print("• 灰色 = 观察信号")
        
        print("\n🎯 使用步骤:")
        print("1. 程序自动获取OKX钱包地址")
        print("2. 自动加载Solana持仓数据")
        print("3. 按价值排序显示前30个代币")
        print("4. 每30秒自动策略分析")
        print("5. 双击代币行查看K线图表")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保安装了所有依赖: pip install PyQt5 requests pandas")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == '__main__':
    main() 