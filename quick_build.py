#!/usr/bin/env python3
"""
快速打包脚本 - 简化版本
"""

import os
import sys
import subprocess
import platform

def install_pyinstaller():
    """安装 PyInstaller"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装")
    except ImportError:
        print("📦 正在安装 PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller 安装完成")

def build_app():
    """构建应用程序"""
    print("🔨 开始构建应用程序...")
    
    # 根据操作系统调整打包参数
    if platform.system() == "Darwin":  # macOS
        # macOS 使用 onedir 模式创建 .app bundle
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onedir",  # macOS 使用目录模式
            "--windowed",  # 创建 .app bundle
            "--name=TrendTrader",
            "--add-data=ui:ui",
            "--add-data=strategies:strategies",
            "--hidden-import=PyQt5.sip",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=pandas",
            "--hidden-import=numpy",
            "--hidden-import=requests",
            "--hidden-import=ta",
            "--hidden-import=matplotlib",
            "--hidden-import=pyqtgraph",
            "--hidden-import=sqlite3",
            "--hidden-import=multiprocessing",
            "--hidden-import=concurrent.futures",
            "--hidden-import=typing",
            "--hidden-import=datetime",
            "--hidden-import=logging",
            "--osx-bundle-identifier=com.trendtrader.app",
            "main.py"
        ]
    else:
        # Windows 和 Linux 使用 onefile 模式
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 打包成单个文件
            "--windowed",  # 隐藏控制台窗口
            "--name=TrendTrader",
            "--add-data=ui:ui",
            "--add-data=strategies:strategies",
            "--hidden-import=PyQt5.sip",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=pandas",
            "--hidden-import=numpy",
            "--hidden-import=requests",
            "--hidden-import=ta",
            "--hidden-import=matplotlib",
            "--hidden-import=pyqtgraph",
            "--hidden-import=sqlite3",
            "--hidden-import=multiprocessing",
            "--hidden-import=concurrent.futures",
            "--hidden-import=typing",
            "--hidden-import=datetime",
            "--hidden-import=logging",
            "main.py"
        ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        
        # 显示输出文件位置
        if platform.system() == "Windows":
            print("📁 可执行文件位置: dist/TrendTrader.exe")
        elif platform.system() == "Darwin":
            print("📁 应用程序位置: dist/TrendTrader.app")
        else:
            print("📁 可执行文件位置: dist/TrendTrader")
            
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print("错误信息:")
        print(e.stderr)
        return False

def main():
    """主函数"""
    print("🚀 TrendTrader 快速打包工具")
    print("=" * 40)
    
    # 1. 安装 PyInstaller
    install_pyinstaller()
    
    # 2. 构建应用
    if build_app():
        print("\n🎉 打包完成！")
        print("您可以在 dist/ 目录中找到可执行文件")
        print("\n📋 分发说明:")
        print("1. 将 dist/ 目录中的文件复制到目标电脑")
        print("2. 确保目标电脑有网络连接（用于 API 调用）")
        print("3. 首次运行时会创建配置文件")
    else:
        print("❌ 打包失败")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 