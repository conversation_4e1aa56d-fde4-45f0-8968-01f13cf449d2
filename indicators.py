"""
技术指标模块 - 计算各种技术指标
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Optional, Union

class TechnicalIndicators:
    """技术指标类，用于计算各种技术分析指标"""
    
    @staticmethod
    def add_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        向DataFrame添加所有技术指标
        
        参数:
            df (pd.DataFrame): 包含OHLCV数据的DataFrame
            
        返回:
            pd.DataFrame: 添加了技术指标的DataFrame
        """
        if df.empty:
            return df
            
        # 确保DataFrame有正确的列名
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"DataFrame必须包含以下列: {required_columns}")
            
        # 添加各种指标
        df = TechnicalIndicators.add_macd(df)
        df = TechnicalIndicators.add_rsi(df)
        df = TechnicalIndicators.add_sar(df)
        df = TechnicalIndicators.add_bollinger_bands(df)
        df = TechnicalIndicators.add_volume_indicators(df)
        df = TechnicalIndicators.add_vwap(df)
        
        return df
    
    @staticmethod
    def add_essential_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        向DataFrame添加核心必需的技术指标（性能优化版本）
        只计算策略最常用的指标，提高性能
        
        参数:
            df (pd.DataFrame): 包含OHLCV数据的DataFrame
            
        返回:
            pd.DataFrame: 添加了核心技术指标的DataFrame
        """
        if df.empty:
            return df
            
        # 确保DataFrame有正确的列名
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            raise ValueError(f"DataFrame必须包含以下列: {required_columns}")
            
        # 只添加最常用的核心指标
        df = TechnicalIndicators.add_macd(df)
        df = TechnicalIndicators.add_rsi(df)
        df = TechnicalIndicators.add_vwap(df)
        # 跳过其他较少使用的指标以提高性能
        
        return df
    
    @staticmethod
    def add_macd(
        df: pd.DataFrame, 
        fast_period: int = 12, 
        slow_period: int = 26, 
        signal_period: int = 9
    ) -> pd.DataFrame:
        """
        添加MACD指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame
            fast_period (int): 快线周期
            slow_period (int): 慢线周期
            signal_period (int): 信号线周期
            
        返回:
            pd.DataFrame: 添加了MACD指标的DataFrame
        """
        if 'close' not in df.columns or df.empty:
            return df
            
        # 计算MACD
        macd = ta.trend.MACD(
            close=df['close'],
            window_fast=fast_period,
            window_slow=slow_period,
            window_sign=signal_period
        )
        
        # 添加MACD相关列
        df['macd'] = macd.macd()
        df['macd_signal'] = macd.macd_signal()
        df['macd_diff'] = macd.macd_diff()
        
        return df
    
    @staticmethod
    def add_rsi(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """
        添加RSI指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame
            period (int): RSI周期
            
        返回:
            pd.DataFrame: 添加了RSI指标的DataFrame
        """
        if 'close' not in df.columns or df.empty:
            return df
            
        # 计算RSI
        df['rsi'] = ta.momentum.RSIIndicator(
            close=df['close'], 
            window=period
        ).rsi()
        
        # 添加RSI超买超卖标记
        df['rsi_overbought'] = df['rsi'] > 70
        df['rsi_oversold'] = df['rsi'] < 30
        
        return df
    
    @staticmethod
    def add_sar(
        df: pd.DataFrame, 
        acceleration: float = 0.02, 
        maximum: float = 0.2
    ) -> pd.DataFrame:
        """
        添加抛物线SAR指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame
            acceleration (float): 加速因子
            maximum (float): 最大加速因子
            
        返回:
            pd.DataFrame: 添加了SAR指标的DataFrame
        """
        if not all(col in df.columns for col in ['high', 'low']) or df.empty:
            return df
            
        # 计算SAR
        df['sar'] = ta.trend.PSARIndicator(
            high=df['high'],
            low=df['low'],
            close=df['close'],
            step=acceleration,
            max_step=maximum
        ).psar()
        
        # 添加SAR信号
        df['sar_above'] = df['close'] < df['sar']  # SAR在价格上方，看跌信号
        df['sar_below'] = df['close'] > df['sar']  # SAR在价格下方，看涨信号
        
        return df
    
    @staticmethod
    def add_bollinger_bands(df: pd.DataFrame, window: int = 20, window_dev: int = 2) -> pd.DataFrame:
        """
        添加布林带指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame
            window (int): 移动平均窗口
            window_dev (int): 标准差倍数
            
        返回:
            pd.DataFrame: 添加了布林带指标的DataFrame
        """
        if 'close' not in df.columns or df.empty:
            return df
            
        # 计算布林带
        bollinger = ta.volatility.BollingerBands(
            close=df['close'],
            window=window,
            window_dev=window_dev
        )
        
        df['bb_mavg'] = bollinger.bollinger_mavg()
        df['bb_high'] = bollinger.bollinger_hband()
        df['bb_low'] = bollinger.bollinger_lband()
        
        # 添加布林带信号
        df['bb_above'] = bollinger.bollinger_hband_indicator()  # 价格在上轨上方
        df['bb_below'] = bollinger.bollinger_lband_indicator()  # 价格在下轨下方
        
        return df
    
    @staticmethod
    def add_volume_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        添加交易量相关指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame
            
        返回:
            pd.DataFrame: 添加了交易量指标的DataFrame
        """
        if not all(col in df.columns for col in ['close', 'volume']) or df.empty:
            return df
            
        # 添加成交量移动平均
        df['volume_ma5'] = df['volume'].rolling(window=5).mean()
        df['volume_ma10'] = df['volume'].rolling(window=10).mean()
        
        # 添加OBV (On-Balance Volume)
        df['obv'] = ta.volume.OnBalanceVolumeIndicator(
            close=df['close'],
            volume=df['volume']
        ).on_balance_volume()
        
        # 添加CMF (Chaikin Money Flow)
        df['cmf'] = ta.volume.ChaikinMoneyFlowIndicator(
            high=df['high'],
            low=df['low'],
            close=df['close'],
            volume=df['volume'],
            window=20
        ).chaikin_money_flow()
        
        return df

    @staticmethod
    def add_vwap(df: pd.DataFrame) -> pd.DataFrame:
        """
        添加VWAP (Volume Weighted Average Price) 指标
        
        参数:
            df (pd.DataFrame): 原始DataFrame，必须包含 'high', 'low', 'close', 'volume' 列
            
        返回:
            pd.DataFrame: 添加了VWAP指标的DataFrame
        """
        if not all(col in df.columns for col in ['high', 'low', 'close', 'volume']):
            # 可以选择在此处记录日志或抛出更具体的错误
            # logger.warning("VWAP calculation requires 'high', 'low', 'close', 'volume' columns.")
            return df
        
        if df.empty:
            return df

        # 确保列是数值类型
        for col in ['high', 'low', 'close', 'volume']:
            if not pd.api.types.is_numeric_dtype(df[col]):
                # logger.error(f"VWAP 计算错误: 列 {col} 非数值类型.")
                # 实际应用中应有更健壮的错误处理或日志
                return df # 或者抛出异常

        # 计算典型价格
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['tp_x_volume'] = df['typical_price'] * df['volume']
        
        # 使用 cumsum 进行累积计算
        cumulative_tp_x_volume = df['tp_x_volume'].cumsum()
        cumulative_volume = df['volume'].cumsum()
        
        # 处理 cumulative_volume 可能为0的情况以避免除以零错误
        df['vwap'] = np.where(cumulative_volume == 0, np.nan, cumulative_tp_x_volume / cumulative_volume)
        
        # 删除中间列 (可选)
        # df.drop(columns=['typical_price', 'tp_x_volume'], inplace=True)
        
        return df
