#!/usr/bin/env python
"""
依赖安装脚本 - 用于安装和配置项目所需的依赖
"""

import os
import sys
import subprocess
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('install_dependencies')

def install_dependencies():
    """安装项目所需的依赖"""
    logger.info("开始安装项目依赖...")
    
    # 需要安装的依赖列表
    dependencies = [
        "numpy==1.24.3",  # 指定较低版本的NumPy，避免兼容性问题
        "ta",             # 技术分析库
        "pandas",         # 数据处理库
        "pyqt5",          # GUI库
        "requests",       # HTTP请求库
        "python-dotenv",  # 环境变量加载库
        "matplotlib",     # 绘图库
    ]
    
    # 使用pip安装依赖
    for dep in dependencies:
        logger.info(f"正在安装 {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"{dep} 安装成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"{dep} 安装失败: {str(e)}")
            return False
    
    logger.info("所有依赖安装完成")
    return True

if __name__ == "__main__":
    if install_dependencies():
        print("\n依赖安装成功！现在可以运行主程序了。")
        print("运行方式: python main.py")
    else:
        print("\n依赖安装过程中出现错误，请查看日志了解详情。")
