#!/usr/bin/env python3
"""
新版持仓面板启动器 - 直接启动智能持仓面板
"""

import sys
import logging
import os
from PyQt5.QtWidgets import QApplication

# 导入新版持仓面板
from holdings_panel_window import HoldingsWindow
from api_service import APIService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数 - 启动新版持仓面板"""
    print("🚀 启动智能持仓面板")
    print("="*50)
    
    # 检查环境
    print("检查运行环境...")
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("智能持仓面板")
        app.setApplicationVersion("1.0")
        
        print("✅ Qt应用程序初始化成功")
        
        # 创建API服务
        print("初始化API服务...")
        api_service = APIService()
        print("✅ API服务初始化成功")
        
        # 创建持仓面板窗口
        print("创建智能持仓面板...")
        holdings_window = HoldingsWindow(
            parent=None,
            api_service=api_service
        )
        
        print("✅ 智能持仓面板创建成功")
        
        # 显示窗口
        holdings_window.show()
        
        print("\n🎉 智能持仓面板已启动！")
        print("\n功能特性:")
        print("• 自动从OKX DEX API获取钱包地址")
        print("• 实时获取Solana钱包持仓数据")
        print("• 按价值排序，自动分析前30个代币")
        print("• 自动OHLCV数据下载和策略分析")
        print("• 策略信号实时显示（买入/卖出/持有/观察）")
        print("• 双击代币行打开K线图表")
        print("• 风险代币检测和标记")
        print("• 30秒自动刷新周期")
        
        print("\n使用说明:")
        print("1. 面板会自动获取钱包地址和持仓数据")
        print("2. 自动策略分析每30秒运行一次")
        print("3. 双击代币行或点击'图表'按钮查看K线")
        print("4. 绿色表示买入信号，红色表示卖出信号")
        
        # 运行应用
        logger.info("启动Qt事件循环...")
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("\n请确保以下模块已正确安装:")
        print("• PyQt5")
        print("• requests")
        print("• pandas")
        print("• python-dotenv")
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")
        
        print("\n故障排除:")
        print("1. 检查OKX DEX API服务是否运行")
        print("2. 检查网络连接")
        print("3. 检查配置文件")


if __name__ == '__main__':
    main() 