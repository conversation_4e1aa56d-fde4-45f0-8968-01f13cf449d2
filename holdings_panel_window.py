"""
新版持仓面板窗口 - 基于trending_window.py设计
集成OKX DEX API和自动策略分析功能
"""
import logging
# 🔥 使用INFO级别日志（移除debug日志提高性能）
logging.getLogger('holdings_panel_window').setLevel(logging.INFO)
import time
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta

# 忽略 Qt 图像相关警告
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.gui.icc.debug=false;qt.gui.imageio.debug=false'
# 静默图像处理库的警告
import warnings
warnings.filterwarnings("ignore", message=".*iCCP.*")
warnings.filterwarnings("ignore", message=".*libpng.*")
warnings.filterwarnings("ignore", message=".*fromIccProfile.*")

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
    QLabel, QCheckBox, QFrame, QProgressBar, QMessageBox, QLineEdit, QComboBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot, QUrl, QObject
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter, QCursor, QPixmapCache
from PyQt5.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
import pandas as pd

# 导入API服务和配置
from api_service import APIService
from config import PORTFOLIO_CONFIG

# 🔥 导入OKX DEX客户端
from okx_dex_client import OKXDexClient, AllTokenBalancesRequest, TotalValueRequest

# 🔥 导入多线程OHLCV管理器
from multi_thread_ohlcv_manager import MultiThreadOHLCVManager

# 🔥 导入图表组件
from ui.chart_widget import ChartWidget

# 🔥 新增：导入信号客户端
from signal_client import SignalClient, create_signal_from_strategy_signal


logger = logging.getLogger(__name__)


class HoldingsWindow(QDialog):
    """新版持仓面板窗口"""
    
    def __init__(self, parent=None, api_service=None):
        super().__init__(parent)
        self.setWindowTitle("智能持仓面板")
        self.setGeometry(150, 150, 1300, 800)

        # API服务和OKX客户端
        self.api_service = api_service if api_service else APIService()
        self.okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        
        # 持仓数据
        self.holdings_data = []
        self.filtered_holdings_data = []  # 按价值筛选后的前30个
        self.wallet_address = ""
        self.total_value = 0.0
        
        # 🔥 多线程OHLCV下载管理器
        self.multi_thread_manager = MultiThreadOHLCVManager(
            api_service=self.api_service,
            max_workers=5,  # 限制并发数避免API限制
            parent=self
        )
        
        # 🔥 管理独立图表窗口
        self.chart_windows = []
        
        # 分析状态跟踪
        self.is_analysis_running = False
        self.analysis_start_time = None
        
        # 🔥 新增：信号客户端 - 向监控服务器发送策略信号
        self.signal_client = SignalClient("holdings", self)
        
        self._init_ui()
        self._setup_timers()
        self._connect_signals()
        self._connect_signal_client()
        
        # 自动获取钱包信息
        self.auto_get_wallet_info()
        
        logger.info("HoldingsWindow: 初始化完成")

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 🔥 设置整体窗口样式 - 深色高对比度主题
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                font-size: 12px;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
            }
            QLineEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 2px solid #404040;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #61dafb;
            }
            QPushButton {
                background-color: #61dafb;
                color: #1e1e1e;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #4fa8c5;
            }
            QPushButton:pressed {
                background-color: #3d8baf;
            }
            QCheckBox {
                color: #ffffff;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #404040;
                background-color: #2d2d2d;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #61dafb;
                background-color: #61dafb;
                border-radius: 3px;
            }
            QComboBox {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 2px solid #404040;
                border-radius: 4px;
                padding: 5px;
                min-width: 50px;
            }
            QComboBox:hover {
                border-color: #61dafb;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #ffffff;
                margin: 0px 4px;
            }
            QComboBox QAbstractItemView {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #404040;
                selection-background-color: #404040;
            }
            QProgressBar {
                border: 2px solid #404040;
                border-radius: 5px;
                background-color: #2d2d2d;
                text-align: center;
                color: #ffffff;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #61dafb;
                border-radius: 3px;
            }
        """)

        # 🔥 钱包信息和控制面板
        self._create_wallet_control_panel(layout)
        
        # 🔥 策略控制面板
        self._create_strategy_control_panel(layout)

        # 🔥 持仓表格
        self._create_holdings_table(layout)

        self.setLayout(layout)
        logger.info("HoldingsWindow: UI初始化完成")

    def _create_wallet_control_panel(self, layout):
        """创建钱包信息和控制面板"""
        wallet_frame = QFrame()
        wallet_layout = QHBoxLayout(wallet_frame)
        
        # 钱包地址输入
        wallet_layout.addWidget(QLabel("钱包地址:"))
        self.wallet_input = QLineEdit()
        self.wallet_input.setPlaceholderText("将自动从OKX DEX API获取...")
        self.wallet_input.textChanged.connect(self.on_wallet_address_changed)
        wallet_layout.addWidget(self.wallet_input)
        
        # 获取钱包信息按钮
        self.get_wallet_btn = QPushButton("获取钱包信息")
        self.get_wallet_btn.clicked.connect(self.auto_get_wallet_info)
        wallet_layout.addWidget(self.get_wallet_btn)
        
        # 刷新持仓按钮
        self.refresh_holdings_btn = QPushButton("刷新持仓")
        self.refresh_holdings_btn.clicked.connect(self.refresh_holdings)
        wallet_layout.addWidget(self.refresh_holdings_btn)
        
        # 总价值显示
        self.total_value_label = QLabel("总价值: $0.00")
        self.total_value_label.setStyleSheet("font-weight: bold; color: #4ade80; font-size: 14px; background-color: #1f2937; padding: 5px; border-radius: 4px; border: 1px solid #374151;")
        wallet_layout.addWidget(self.total_value_label)
        
        wallet_layout.addStretch()
        layout.addWidget(wallet_frame)

    def _create_strategy_control_panel(self, layout):
        """创建策略控制面板"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        
        # 策略分析开关
        self.strategy_enabled_checkbox = QCheckBox("启用自动策略分析 (30秒周期)")
        self.strategy_enabled_checkbox.setChecked(True)
        self.strategy_enabled_checkbox.stateChanged.connect(self.on_strategy_enabled_changed)
        control_layout.addWidget(self.strategy_enabled_checkbox)
        
        # 分析代币数量选择
        control_layout.addWidget(QLabel("分析前"))
        self.analysis_count_combo = QComboBox()
        self.analysis_count_combo.addItems(["10", "20", "30"])
        self.analysis_count_combo.setCurrentText("30")
        control_layout.addWidget(self.analysis_count_combo)
        control_layout.addWidget(QLabel("个代币"))
        
        # 手动分析按钮
        self.manual_analysis_button = QPushButton("立即分析")
        self.manual_analysis_button.clicked.connect(self.trigger_manual_analysis)
        control_layout.addWidget(self.manual_analysis_button)
        
        # 状态标签
        self.status_label = QLabel("状态: 就绪")
        control_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        control_layout.addStretch()
        layout.addWidget(control_frame)

    def _create_holdings_table(self, layout):
        """创建持仓表格"""
        self.holdings_table = QTableWidget()
        self.holdings_table.setColumnCount(12)
        self.holdings_table.setHorizontalHeaderLabels([
            "编号", "代币", "数量", "价格", "价值", "占比", "链", "风险", "策略", "信号时间", "24h变化", "操作"
        ])
        
        # 设置列宽
        header = self.holdings_table.horizontalHeader()
        header.setStretchLastSection(False)
        self.holdings_table.setColumnWidth(0, 50)   # 编号
        self.holdings_table.setColumnWidth(1, 80)   # 代币
        self.holdings_table.setColumnWidth(2, 100)  # 数量
        self.holdings_table.setColumnWidth(3, 80)   # 价格
        self.holdings_table.setColumnWidth(4, 100)  # 价值
        self.holdings_table.setColumnWidth(5, 60)   # 占比
        self.holdings_table.setColumnWidth(6, 60)   # 链
        self.holdings_table.setColumnWidth(7, 50)   # 风险
        self.holdings_table.setColumnWidth(8, 60)   # 策略
        self.holdings_table.setColumnWidth(9, 80)   # 信号时间
        self.holdings_table.setColumnWidth(10, 80)  # 24h变化
        self.holdings_table.setColumnWidth(11, 80)  # 操作
        
        self.holdings_table.verticalHeader().setVisible(False)
        self.holdings_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.holdings_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 表格样式 - 深色高对比度主题
        self.holdings_table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 2px solid #404040;
                gridline-color: #404040;
                selection-background-color: #374151;
                selection-color: #61dafb;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
                border-right: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #374151;
                color: #61dafb;
            }
            QTableWidget::item:hover {
                background-color: #2d2d2d;
            }
            QHeaderView::section {
                background-color: #111827;
                color: #ffffff;
                padding: 10px;
                border: 1px solid #374151;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:hover {
                background-color: #1f2937;
            }
        """)
        
        # 双击事件监听器
        self.holdings_table.cellDoubleClicked.connect(self.on_token_double_clicked)
        layout.addWidget(self.holdings_table)

    def _setup_timers(self):
        """设置定时器"""
        # 持仓刷新定时器
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.auto_refresh_with_analysis)
        
        # 根据配置设置刷新间隔
        refresh_interval = PORTFOLIO_CONFIG.get("refresh_interval", 30000)
        self.refresh_timer.start(refresh_interval)
        
        logger.info(f"HoldingsWindow: 刷新定时器启动 ({refresh_interval/1000}秒间隔)")

    def _connect_signals(self):
        """连接信号"""
        # API信号连接
        # (这里不需要趋势榜单信号，因为我们处理的是持仓数据)
        
        # 多线程管理器信号
        self.multi_thread_manager.strategy_signal_generated.connect(self.on_strategy_signal_received)
        self.multi_thread_manager.download_completed.connect(self.on_single_download_completed)
        self.multi_thread_manager.download_failed.connect(self.on_single_download_failed)
        self.multi_thread_manager.all_downloads_completed.connect(self.on_batch_analysis_completed)
        
        logger.info("HoldingsWindow: 信号连接完成")

    def _connect_signal_client(self):
        """连接信号客户端"""
        try:
            # 连接信号客户端的信号
            self.signal_client.connected.connect(self.on_signal_client_connected)
            self.signal_client.disconnected.connect(self.on_signal_client_disconnected)
            self.signal_client.connection_error.connect(self.on_signal_client_error)
            self.signal_client.signal_sent.connect(self.on_signal_sent)
            
            # 启动连接
            self.signal_client.start_connection()
            
            logger.info("HoldingsWindow: 信号客户端连接已启动")
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 连接信号客户端失败: {e}")

    def on_signal_client_connected(self):
        """信号客户端连接成功"""
        logger.info("HoldingsWindow: 成功连接到信号监控服务器")

    def on_signal_client_disconnected(self):
        """信号客户端断开连接"""
        logger.warning("HoldingsWindow: 与信号监控服务器断开连接")

    def on_signal_client_error(self, error_message: str):
        """信号客户端连接错误"""
        logger.warning(f"HoldingsWindow: 信号客户端连接错误 - {error_message}")

    def on_signal_sent(self, symbol: str, signal_type: str):
        """信号发送成功"""
        logger.debug(f"HoldingsWindow: 信号发送成功 - {symbol} {signal_type}")

    def auto_get_wallet_info(self):
        """自动获取钱包信息"""
        try:
            self.status_label.setText("状态: 正在获取钱包信息...")
            
            # 从OKX DEX API获取钱包信息
            wallet_info = self.okx_client.get_wallet_info()
            
            if wallet_info.get("success"):
                wallet_data = wallet_info.get("data", {})
                # 🔥 修复：适配实际API响应格式
                solana_wallet = (
                    wallet_data.get("solanaWallet", "") or 
                    wallet_data.get("solanaWalletAddress", "") or
                    wallet_data.get("supportedChains", {}).get("solana", {}).get("walletAddress", "")
                )
                
                if solana_wallet:
                    self.wallet_address = solana_wallet
                    self.wallet_input.setText(solana_wallet)
                    self.status_label.setText(f"状态: 已获取钱包地址 {solana_wallet[:8]}...")
                    logger.info(f"HoldingsWindow: 自动获取到钱包地址: {solana_wallet}")
                    
                    # 自动刷新持仓
                    self.refresh_holdings()
                else:
                    self.status_label.setText("状态: 未找到Solana钱包地址")
                    logger.warning("HoldingsWindow: OKX API返回的钱包信息中没有Solana地址")
                    logger.debug(f"HoldingsWindow: 完整钱包数据: {wallet_data}")
                    
                    # 🔥 初始化空的持仓数据避免后续错误
                    self.update_holdings_table([])
            else:
                error_msg = wallet_info.get("error", "未知错误")
                self.status_label.setText(f"状态: 获取钱包信息失败 - {error_msg}")
                logger.error(f"HoldingsWindow: 获取钱包信息失败: {error_msg}")
                
                # 🔥 初始化空的持仓数据避免后续错误
                self.update_holdings_table([])
                
        except Exception as e:
            self.status_label.setText("状态: 获取钱包信息异常")
            logger.error(f"HoldingsWindow: 获取钱包信息异常: {e}")
            
            # 🔥 初始化空的持仓数据避免后续错误
            self.update_holdings_table([])

    def on_wallet_address_changed(self, address: str):
        """钱包地址改变事件"""
        self.wallet_address = address.strip()

    def refresh_holdings(self):
        """刷新持仓数据"""
        if not self.wallet_address:
            self.status_label.setText("状态: 请先输入钱包地址")
            return
        
        try:
            self.status_label.setText("状态: 正在刷新持仓数据...")
            
            # 获取所有代币余额
            request = AllTokenBalancesRequest(
                address=self.wallet_address,
                chains="501",  # Solana链ID
                exclude_risk_token="0"  # 包含风险代币，但会标记
            )
            
            result = self.okx_client.get_all_token_balances(request)
            
            if result.get("success"):
                # 🔥 修复：适配OKX API的实际响应格式
                data = result.get("data", {})
                
                                # 提取持仓数据 - 适配不同的响应格式
                holdings_data = []
                if isinstance(data, list):
                    # 如果data直接是列表
                    holdings_data = data
                elif isinstance(data, dict):
                    # 如果data是字典，尝试从不同字段提取
                    if "tokenAssets" in data:
                        holdings_data = data["tokenAssets"]  # 🔥 OKX API实际字段
                    elif "tokenBalances" in data:
                        holdings_data = data["tokenBalances"]
                    elif "balances" in data:
                        holdings_data = data["balances"]
                    elif "data" in data:
                        holdings_data = data["data"]
                    else:
                        # 如果没有找到预期字段，尝试将字典转换为列表项
                        logger.warning(f"HoldingsWindow: 未知的数据格式，尝试转换: {list(data.keys())}")
                        holdings_data = [data] if data else []
                
                logger.info(f"HoldingsWindow: 提取到 {len(holdings_data)} 个持仓项")
                
                # 获取总价值
                total_value_request = TotalValueRequest(
                    address=self.wallet_address,
                    chains="501"
                )
                total_value_result = self.okx_client.get_total_value(total_value_request)
                
                if total_value_result.get("success"):
                    total_value_data = total_value_result.get("data", {})
                    if isinstance(total_value_data, dict):
                        self.total_value = float(total_value_data.get("totalValue", 0.0))
                    else:
                        self.total_value = 0.0
                        logger.warning(f"HoldingsWindow: 总价值数据格式异常: {type(total_value_data)}")
                else:
                    self.total_value = 0.0
                
                self.update_holdings_table(holdings_data)
                self.total_value_label.setText(f"总价值: ${self.total_value:,.2f}")
                
                count = len(holdings_data)
                filtered_count = len(self.filtered_holdings_data)
                self.status_label.setText(f"状态: 已刷新 {count} 个代币 (前{filtered_count}个将被分析)")
                
                logger.info(f"HoldingsWindow: 刷新完成 - {count} 个代币，总价值 ${self.total_value:,.2f}")
                
            else:
                error_msg = result.get("error", "未知错误")
                self.status_label.setText(f"状态: 刷新失败 - {error_msg}")
                logger.error(f"HoldingsWindow: 刷新持仓失败: {error_msg}")
                
        except Exception as e:
            self.status_label.setText("状态: 刷新异常")
            logger.error(f"HoldingsWindow: 刷新持仓异常: {e}")

    def update_holdings_table(self, holdings_data: List[Dict]):
        """更新持仓表格"""
        try:
            # 🔥 数据验证
            logger.info(f"HoldingsWindow: 接收到持仓数据，类型: {type(holdings_data)}, 数量: {len(holdings_data) if isinstance(holdings_data, list) else 'N/A'}")
            
            # 验证数据格式
            if not isinstance(holdings_data, list):
                logger.error(f"HoldingsWindow: 持仓数据不是列表格式: {type(holdings_data)}")
                holdings_data = []
            
            # 过滤掉非字典对象
            valid_holdings = []
            for i, item in enumerate(holdings_data):
                if isinstance(item, dict):
                    valid_holdings.append(item)
                else:
                    logger.warning(f"HoldingsWindow: 第{i}项数据不是字典格式: {type(item)} - {item}")
            
            self.holdings_data = valid_holdings
            
            if not valid_holdings:
                logger.info("HoldingsWindow: 没有有效的持仓数据")
                self.holdings_table.setRowCount(0)
                return
            
            # 🔥 按价值排序并筛选前N个 - 适配OKX API
            sorted_holdings = sorted(
                valid_holdings, 
                key=lambda x: float(x.get('balance', 0) or 0) * float(x.get('tokenPrice', 0) or 0), 
                reverse=True
            )
            
            # 排除特定的稳定币 (例如 USDC, USDT等) by their symbol
            # 这些代币通常不进行趋势策略分析
            excluded_symbols = ["USDC", "USDT"] # 您可以根据需要在此列表中添加更多稳定币的交易符号

            # 屏蔽的代币地址
            excluded_addresses = [
                "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", # USDC
            ]


            holdings_for_analysis = []
            for h_item in sorted_holdings:
                # 尝试获取代币地址，优先使用 'tokenContractAddress' 键 (OKX API中常见)
                token_address_from_data = h_item.get('tokenContractAddress', '')
                # 检查获取到的代币地址是否存在于我们的排除列表中
                if token_address_from_data and token_address_from_data in excluded_addresses:
                    # 如果是需要排除的稳定币，则记录日志并跳过，不将其加入待分析列表
                    logger.info(f"HoldingsWindow: 正在从策略分析中排除稳定币: {token_address_from_data}")
                    continue 
                
                # 如果不是需要排除的代币，则将其加入待分析列表
                holdings_for_analysis.append(h_item)
            
            # 获取用户选择的分析数量
            max_count = int(self.analysis_count_combo.currentText())
            self.filtered_holdings_data = holdings_for_analysis[:max_count]
            
            # 更新表格
            self.holdings_table.setRowCount(len(sorted_holdings))
            
            for row, holding in enumerate(sorted_holdings):
                self._populate_holding_row(row, holding)
            
            logger.info(f"HoldingsWindow: 更新表格完成 - {len(sorted_holdings)} 行，{len(self.filtered_holdings_data)} 行将被分析")
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 更新表格失败: {e}")
            import traceback
            logger.error(f"HoldingsWindow: 错误堆栈: {traceback.format_exc()}")

    def _populate_holding_row(self, row: int, holding: Dict):
        """填充持仓行数据"""
        try:
            # 🔥 数据验证
            if not isinstance(holding, dict):
                logger.error(f"HoldingsWindow: 第{row}行数据不是字典格式: {type(holding)} - {holding}")
                return
            
            # 🔥 提取数据 - 适配OKX API字段名
            symbol = holding.get('symbol', 'Unknown')
            token_address = holding.get('tokenContractAddress', '')
            quantity = float(holding.get('balance', 0) or 0)  # OKX使用balance字段
            price_usd = float(holding.get('tokenPrice', 0) or 0)  # OKX使用tokenPrice字段
            is_risk_token = holding.get('isRiskToken', False)
            
            # 计算价值 (OKX API可能不直接提供value字段)
            value_usd = quantity * price_usd
            
            # 链名称 - 从chainIndex推断
            chain_index = holding.get('chainIndex', '501')
            if chain_index == '501':
                chain_name = 'Solana'
            else:
                chain_name = f'Chain-{chain_index}'
            
            # 24h变化 (可能不可用)
            price_change_24h = holding.get('priceChange24h')
            
            # 计算占比
            percentage = (value_usd / self.total_value * 100) if self.total_value > 0 else 0
            
            # 编号列
            number_item = QTableWidgetItem(str(row + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            self.holdings_table.setItem(row, 0, number_item)
            
            # 代币符号
            self.holdings_table.setItem(row, 1, QTableWidgetItem(symbol))
            
            # 数量
            quantity_text = f"{quantity:,.4f}" if quantity < 1000 else f"{quantity:,.0f}"
            self.holdings_table.setItem(row, 2, QTableWidgetItem(quantity_text))
            
            # 价格
            price_item = QTableWidgetItem(f"${price_usd:.6f}" if price_usd < 1 else f"${price_usd:.2f}")
            self.holdings_table.setItem(row, 3, price_item)
            
            # 价值
            value_item = QTableWidgetItem(f"${value_usd:,.2f}")
            value_item.setForeground(QColor("#4ade80"))  # 亮绿色，深色主题
            self.holdings_table.setItem(row, 4, value_item)
            
            # 占比
            percentage_item = QTableWidgetItem(f"{percentage:.2f}%")
            self.holdings_table.setItem(row, 5, percentage_item)
            
            # 链
            self.holdings_table.setItem(row, 6, QTableWidgetItem(chain_name))
            
            # 风险标记
            risk_item = QTableWidgetItem("⚠️" if is_risk_token else "✅")
            risk_item.setForeground(QColor("#f87171") if is_risk_token else QColor("#4ade80"))  # 亮红色和亮绿色，深色主题
            self.holdings_table.setItem(row, 7, risk_item)
            
            # 策略列 (初始为空)
            self.holdings_table.setItem(row, 8, QTableWidgetItem("--"))
            
            # 信号时间列 (初始为空)
            self.holdings_table.setItem(row, 9, QTableWidgetItem("--"))
            
            # 24h变化
            if price_change_24h is not None:
                change_text = f"{price_change_24h:.2f}%"
                change_item = QTableWidgetItem(change_text)
                if price_change_24h > 0:
                    change_item.setForeground(QColor("#4ade80"))  # 亮绿色，深色主题
                elif price_change_24h < 0:
                    change_item.setForeground(QColor("#f87171"))  # 亮红色，深色主题
                self.holdings_table.setItem(row, 10, change_item)
            else:
                self.holdings_table.setItem(row, 10, QTableWidgetItem("N/A"))
            
            # 操作按钮 (图表)
            chart_btn = QPushButton("图表")
            chart_btn.setMaximumWidth(60)
            chart_btn.clicked.connect(lambda checked, addr=token_address: self.open_chart_for_token(addr))
            self.holdings_table.setCellWidget(row, 11, chart_btn)
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 填充第{row}行失败: {e}")

    def auto_refresh_with_analysis(self):
        """自动刷新并进行策略分析"""
        # 先刷新持仓
        self.refresh_holdings()
        
        # 如果启用了自动策略分析，则开始分析
        if self.strategy_enabled_checkbox.isChecked() and self.filtered_holdings_data:
            self.start_batch_strategy_analysis(is_auto=True)

    def on_strategy_enabled_changed(self, state):
        """策略分析开关状态改变"""
        if state == 2:  # 启用
            logger.info("HoldingsWindow: 自动策略分析已启用")
            self.status_label.setText("状态: 自动分析已启用")
        else:  # 禁用
            logger.info("HoldingsWindow: 自动策略分析已禁用")
            if self.is_analysis_running:
                self.force_stop_current_analysis()
            self.status_label.setText("状态: 自动分析已禁用")

    def trigger_manual_analysis(self):
        """手动触发策略分析"""
        try:
            if not self.filtered_holdings_data:
                self.status_label.setText("状态: 没有持仓数据可分析")
                return
            
            # 强制停止当前分析（如果有）
            if self.is_analysis_running:
                self.force_stop_current_analysis()
            
            # 开始手动分析
            self.start_batch_strategy_analysis(is_auto=False)
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 手动策略分析失败: {e}")

    def force_stop_current_analysis(self):
        """强制停止当前正在进行的分析"""
        try:
            if self.is_analysis_running:
                logger.info("HoldingsWindow: 强制停止当前策略分析...")
                
                # 强制停止多线程管理器的所有任务
                self.multi_thread_manager.force_stop_all_tasks()
                
                # 重置统计信息
                self.multi_thread_manager.reset_statistics()
                
                # 更新状态
                self.is_analysis_running = False
                self.analysis_start_time = None
                self.status_label.setText("状态: 分析已强制停止")
                self.progress_bar.setVisible(False)
                
                logger.info("HoldingsWindow: 当前分析已强制停止")
                
        except Exception as e:
            logger.error(f"HoldingsWindow: 强制停止分析时出错: {e}")

    def start_batch_strategy_analysis(self, is_auto: bool = False):
        """开始批量策略分析"""
        if not self.filtered_holdings_data:
            status_msg = "状态: 没有持仓数据进行分析"
            self.status_label.setText(status_msg)
            logger.warning("HoldingsWindow: 没有持仓数据可分析")
            return
        
        # 转换持仓数据为代币分析格式
        tokens_to_analyze = []
        for holding in self.filtered_holdings_data:
            token_data = {
                'tokenAddress': holding.get('tokenContractAddress', ''),
                'address': holding.get('tokenContractAddress', ''),
                'symbol': holding.get('symbol', 'Unknown'),
                'name': holding.get('name', holding.get('symbol', 'Unknown')),
                'network': 'solana',
                'source': 'holdings'  # 标识数据来源
            }
            tokens_to_analyze.append(token_data)
        
        analysis_type = "自动" if is_auto else "手动"
        logger.info(f"HoldingsWindow: 开始{analysis_type}批量策略分析 ({len(tokens_to_analyze)} 个代币)")
        
        # 更新分析状态
        self.is_analysis_running = True
        self.analysis_start_time = time.time()
        
        # 更新状态UI
        self.status_label.setText(f"状态: {analysis_type}分析中... (0/{len(tokens_to_analyze)})")
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(tokens_to_analyze))
        self.progress_bar.setValue(0)
        
        # 使用配置中的默认策略
        default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
        
        submitted_count = self.multi_thread_manager.submit_batch_download(
            token_list=tokens_to_analyze,
            strategy_name=default_strategy,
            timeframe='1m',
            days=1
        )
        
        if submitted_count > 0:
            logger.info(f"HoldingsWindow: 成功提交 {submitted_count} 个{analysis_type}分析任务")
        else:
            self.status_label.setText(f"状态: {analysis_type}分析提交失败")
            self.progress_bar.setVisible(False)
            self.is_analysis_running = False
            self.analysis_start_time = None

    @pyqtSlot(object)
    def on_strategy_signal_received(self, signal_data):
        """处理策略信号 (接收SignalData对象)"""
        try:
            # 从SignalData对象中提取信息
            token_address = signal_data.token_address
            symbol = signal_data.symbol
            signal_type = signal_data.signal_type
            price = signal_data.price
            timestamp = signal_data.timestamp
            strategy_name = signal_data.strategy_name

            # 信号类型中文映射
            signal_type_mapping = {
                'buy': '买入',
                'sell': '卖出',
                'hold': '持有',
                'wait': '观察'
            }
            signal_type_zh = signal_type_mapping.get(signal_type, signal_type)

            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")

            logger.info(f"HoldingsWindow: 接收到策略信号 (SignalData) - {symbol} {signal_type_zh} @ ${price:.6f} ({time_str})")
            
            # 🔥 新增：发送信号到监控服务器
            try:
                signal_data = create_signal_from_strategy_signal(
                    token_address=token_address,
                    symbol=symbol,
                    signal_type=signal_type,  # 使用原始英文信号类型
                    price=price,
                    timestamp=timestamp,
                    strategy_name=strategy_name,
                    confidence=0.9,  # 持仓窗口的信号置信度设为0.9（比趋势窗口更高）
                    metadata={
                        'source_window': 'holdings',
                        'signal_source': 'holdings_analysis'
                    }
                )
                
                success = self.signal_client.send_signal(signal_data)
                if success:
                    logger.info(f"HoldingsWindow: 成功发送信号到监控服务器 - {symbol} {signal_type}")
                else:
                    logger.warning(f"HoldingsWindow: 发送信号到监控服务器失败 - {symbol} {signal_type}")
                    
            except Exception as e:
                logger.error(f"HoldingsWindow: 发送信号到监控服务器异常: {e}")
            
            # 更新表格中的策略列
            self.update_strategy_column(token_address, signal_type_zh, time_str)
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 处理策略信号失败: {e}")

    @pyqtSlot(str, str, list, dict)
    def on_single_download_completed(self, token_address: str, symbol: str, ohlcv_data: list, token_data: dict):
        """单个下载完成"""
        try:
            # 更新进度条
            if self.progress_bar.isVisible():
                current_value = self.progress_bar.value()
                new_value = current_value + 1
                self.progress_bar.setValue(new_value)
                
                max_value = self.progress_bar.maximum()
                analysis_type = "自动" if self.strategy_enabled_checkbox.isChecked() else "手动"
                self.status_label.setText(f"状态: {analysis_type}分析中... ({new_value}/{max_value})")
                
            logger.debug(f"HoldingsWindow: {symbol} 下载完成，{len(ohlcv_data)} 条数据")
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 处理下载完成事件失败: {e}")

    @pyqtSlot(str, str, str)
    def on_single_download_failed(self, token_address: str, symbol: str, error_message: str):
        """单个下载失败"""
        try:
            # 更新进度条
            if self.progress_bar.isVisible():
                current_value = self.progress_bar.value()
                new_value = current_value + 1
                self.progress_bar.setValue(new_value)
                
                max_value = self.progress_bar.maximum()
                analysis_type = "自动" if self.strategy_enabled_checkbox.isChecked() else "手动"
                self.status_label.setText(f"状态: {analysis_type}分析中... ({new_value}/{max_value})")
                
            logger.debug(f"HoldingsWindow: {symbol} 下载失败: {error_message}")
        except Exception as e:
            logger.error(f"HoldingsWindow: 处理下载失败事件失败: {e}")

    @pyqtSlot(int, int)
    def on_batch_analysis_completed(self, success_count: int, total_count: int):
        """批量分析完成"""
        try:
            # 计算分析耗时
            analysis_duration = ""
            if self.analysis_start_time:
                duration = time.time() - self.analysis_start_time
                analysis_duration = f" ({duration:.1f}秒)"
            
            logger.info(f"HoldingsWindow: 批量分析完成 - 成功: {success_count}/{total_count}{analysis_duration}")
            
            completion_rate = (success_count / total_count * 100) if total_count > 0 else 0
            
            # 根据成功率设置不同的状态消息
            analysis_status = "自动分析" if self.strategy_enabled_checkbox.isChecked() else "手动分析"
            if completion_rate >= 80:
                status_msg = f"状态: {analysis_status}完成 ✅ (成功率: {completion_rate:.1f}%{analysis_duration})"
            elif completion_rate >= 50:
                status_msg = f"状态: {analysis_status}完成 ⚠️ (成功率: {completion_rate:.1f}%{analysis_duration})"
            else:
                status_msg = f"状态: {analysis_status}完成 ❌ (成功率: {completion_rate:.1f}%{analysis_duration})"
                
            self.status_label.setText(status_msg)
            self.progress_bar.setVisible(False)
            
            # 重置分析状态
            self.is_analysis_running = False
            self.analysis_start_time = None
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 处理分析完成事件时出错: {e}")
            self.is_analysis_running = False
            self.analysis_start_time = None
            self.progress_bar.setVisible(False)

    def update_strategy_column(self, token_address: str, signal: str, time_str: str):
        """更新表格中的策略列"""
        try:
            # 在表格中查找对应的代币行
            for row in range(self.holdings_table.rowCount()):
                if row < len(self.holdings_data):
                    holding_data = self.holdings_data[row]
                    holding_address = holding_data.get('tokenContractAddress', '')
                    
                    if holding_address == token_address:
                        # 更新策略列
                        strategy_item = QTableWidgetItem(signal)
                        if signal == "买入":
                            strategy_item.setForeground(QColor("#4ade80"))  # 亮绿色，深色主题
                        elif signal == "卖出":
                            strategy_item.setForeground(QColor("#f87171"))  # 亮红色，深色主题
                        elif signal == "持有":
                            strategy_item.setForeground(QColor("#fbbf24"))  # 亮黄色，深色主题
                        elif signal == "观察":
                            strategy_item.setForeground(QColor("#9ca3af"))  # 亮灰色，深色主题
                        
                        self.holdings_table.setItem(row, 8, strategy_item)
                        
                        # 更新时间列
                        time_item = QTableWidgetItem(time_str)
                        time_item.setForeground(QColor("#9ca3af"))  # 亮灰色，深色主题
                        self.holdings_table.setItem(row, 9, time_item)
                        
                        logger.debug(f"HoldingsWindow: 更新策略列成功 - {holding_data.get('symbol')} -> {signal}")
                        break
                        
        except Exception as e:
            logger.error(f"HoldingsWindow: 更新策略列失败: {e}")

    def on_token_double_clicked(self, row: int, column: int):
        """处理代币双击事件 - 打开K线图表"""
        try:
            if row >= len(self.holdings_data):
                logger.warning(f"HoldingsWindow: 双击行索引超出范围: {row}")
                return
            
            holding_data = self.holdings_data[row]
            token_address = holding_data.get('tokenContractAddress', '')
            symbol = holding_data.get('symbol', 'Unknown')
            
            if token_address:
                self.open_chart_for_token(token_address)
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 处理双击事件失败: {e}")

    def open_chart_for_token(self, token_address: str):
        """为指定代币打开图表"""
        try:
            # 找到对应的持仓数据
            holding_data = None
            for holding in self.holdings_data:
                if holding.get('tokenContractAddress') == token_address:
                    holding_data = holding
                    break
            
            if not holding_data:
                self.status_label.setText("状态: 未找到对应的代币数据")
                return
            
            symbol = holding_data.get('symbol', 'Unknown')
            name = holding_data.get('name', symbol)
            
            self.status_label.setText(f"状态: 正在打开 {symbol} K线图表...")
            
            # 尝试从缓存获取数据
            cached_ohlcv_data = self.get_cached_ohlcv_data(token_address)
            
            if cached_ohlcv_data:
                # 构造token_data格式
                token_data = {
                    'tokenAddress': token_address,
                    'address': token_address,
                    'symbol': symbol,
                    'name': name,
                    'network': 'solana',
                    'source': 'holdings'
                }
                
                self.open_chart_with_cached_data(token_data, cached_ohlcv_data)
            else:
                # 询问用户是否开始获取数据
                reply = QMessageBox.question(
                    self, 
                    '数据不可用', 
                    f'{symbol} 暂无K线数据。\n\n是否现在开始获取数据？',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    self.start_single_token_analysis(holding_data)
                
        except Exception as e:
            logger.error(f"HoldingsWindow: 打开图表失败: {e}")

    def start_single_token_analysis(self, holding_data: Dict):
        """为单个代币启动数据分析"""
        try:
            symbol = holding_data.get('symbol', 'Unknown')
            token_address = holding_data.get('tokenContractAddress', '')
            
            self.status_label.setText(f"状态: 正在为 {symbol} 获取K线数据...")
            
            # 记录等待自动打开图表的代币
            if not hasattr(self, 'pending_chart_opens'):
                self.pending_chart_opens = set()
            self.pending_chart_opens.add(token_address)
            
            # 转换为分析格式
            token_data = {
                'tokenAddress': token_address,
                'address': token_address,
                'symbol': symbol,
                'name': holding_data.get('name', symbol),
                'network': 'solana',
                'source': 'holdings'
            }
            
            # 使用多线程管理器获取数据
            from multi_thread_ohlcv_manager import OHLCVDownloadTask
            default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
            
            task = OHLCVDownloadTask(
                token_data=token_data,
                strategy_name=default_strategy,
                timeframe='1m',
                days=1
            )
            
            self.multi_thread_manager.submit_task(task)
            logger.info(f"HoldingsWindow: 已为 {symbol} 提交数据获取任务")
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 启动单个代币分析失败: {e}")

    def get_cached_ohlcv_data(self, token_address: str) -> Optional[List]:
        """从缓存获取OHLCV数据"""
        try:
            # 优先从MultiThreadOHLCVManager获取
            chart_widget = self.multi_thread_manager.get_chart_widget(token_address)
            if chart_widget and hasattr(chart_widget, 'ohlcv_data') and chart_widget.ohlcv_data:
                return chart_widget.ohlcv_data
            
            # 从API服务缓存获取
            if hasattr(self.api_service, 'get_cached_ohlcv_data'):
                cached_data = self.api_service.get_cached_ohlcv_data(token_address, '1m')
                if cached_data:
                    return cached_data
            
            return None
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 获取缓存数据失败: {e}")
            return None

    def open_chart_with_cached_data(self, token_data: Dict, ohlcv_data: List):
        """使用缓存数据打开图表窗口"""
        try:
            symbol = token_data.get('symbol', 'Unknown')
            name = token_data.get('name', symbol)
            token_address = token_data.get('tokenAddress', 'Unknown')
            
            # 创建图表窗口
            chart_window = ChartWidget(
                api_service=self.api_service,
                parent=None
            )
            
            chart_window.setWindowTitle(f"持仓K线图表 - {name} ({symbol}) | 地址: {token_address[:8]}...")
            chart_window.resize(1200, 800)
            
            # 设置代币信息
            enhanced_token_data = token_data.copy()
            enhanced_token_data.update({
                'source': 'holdings',
                'timeframe': '1m',
                'strategy_name': PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
            })
            
            # 临时禁用自动刷新
            chart_window.auto_refresh = False
            chart_window.set_token(enhanced_token_data)
            
            # 延迟提供数据
            def delayed_display_data():
                try:
                    chart_window.display_provided_ohlcv(
                        ohlcv_data=ohlcv_data,
                        timeframe='1m',
                        source_description=f"持仓缓存 ({len(ohlcv_data)} 条)"
                    )
                    chart_window.auto_refresh_check.setChecked(False)
                except Exception as e:
                    logger.error(f"HoldingsWindow: 延迟显示数据失败: {e}")
            
            QTimer.singleShot(100, delayed_display_data)
            
            chart_window.show()
            self.chart_windows.append(chart_window)
            
            self.status_label.setText(f"状态: 已打开 {symbol} 图表 (缓存数据: {len(ohlcv_data)} 条)")
            logger.info(f"HoldingsWindow: 成功打开 {symbol} 图表窗口")
            
        except Exception as e:
            logger.error(f"HoldingsWindow: 打开图表窗口失败: {e}")
            self.status_label.setText(f"状态: 打开图表失败")

    def closeEvent(self, event):
        """窗口关闭事件"""
        logger.info("HoldingsWindow: 正在关闭...")
        
        # 停止定时器
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        
        # 强制停止当前分析
        if hasattr(self, 'is_analysis_running') and self.is_analysis_running:
            self.force_stop_current_analysis()
        
        # 关闭多线程管理器
        if hasattr(self, 'multi_thread_manager'):
            self.multi_thread_manager.shutdown()
            
        # 🔥 新增：断开信号客户端连接
        if hasattr(self, 'signal_client'):
            self.signal_client.disconnect_from_server()
        
        # 清理图表窗口
        if hasattr(self, 'chart_windows'):
            for window in self.chart_windows:
                if window:
                    window.close()
        
        super().closeEvent(event)


# 测试用例
if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication

    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    
    app = QApplication(sys.argv)
    
    # 创建API服务
    from api_service import APIService
    main_api_service = APIService()
    
    # 创建持仓窗口
    window = HoldingsWindow(api_service=main_api_service)
    window.show()
    
    sys.exit(app.exec_()) 