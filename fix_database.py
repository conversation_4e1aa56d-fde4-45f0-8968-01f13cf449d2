#!/usr/bin/env python3
"""
数据库修复脚本 - 解决UNIQUE约束冲突问题
"""

import sqlite3
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

DATABASE_NAME = "app_data.db"

def backup_database():
    """备份原数据库"""
    if not os.path.exists(DATABASE_NAME):
        logger.info("数据库文件不存在，无需备份")
        return None
    
    backup_name = f"trading_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    
    try:
        # 简单文件复制
        with open(DATABASE_NAME, 'rb') as src, open(backup_name, 'wb') as dst:
            dst.write(src.read())
        logger.info(f"数据库已备份到: {backup_name}")
        return backup_name
    except Exception as e:
        logger.error(f"备份数据库失败: {e}")
        return None

def fix_database_schema():
    """修复数据库架构"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()
        
        # 检查是否存在问题约束
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='tokens_metadata';")
        result = cursor.fetchone()
        
        if result and 'UQ_Symbol_Source UNIQUE (symbol, source)' in result[0]:
            logger.info("发现有问题的UNIQUE约束，开始修复...")
            
            # 1. 创建临时表（无问题约束）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tokens_metadata_new (
                    address TEXT PRIMARY KEY,
                    symbol TEXT,
                    name TEXT,
                    decimals INTEGER,
                    source TEXT,
                    created_at_api TEXT,
                    logo_uri TEXT,
                    description TEXT,
                    last_fetched_metadata REAL,
                    other_metadata_json TEXT
                );
            """)
            
            # 2. 复制数据到新表（处理重复的symbol+source组合）
            cursor.execute("""
                INSERT OR REPLACE INTO tokens_metadata_new 
                SELECT address, symbol, name, decimals, source, created_at_api, 
                       logo_uri, description, last_fetched_metadata, other_metadata_json
                FROM tokens_metadata;
            """)
            
            # 3. 删除旧表
            cursor.execute("DROP TABLE tokens_metadata;")
            
            # 4. 重命名新表
            cursor.execute("ALTER TABLE tokens_metadata_new RENAME TO tokens_metadata;")
            
            conn.commit()
            logger.info("数据库架构修复完成")
            
        else:
            logger.info("数据库架构无需修复")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"修复数据库架构失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """主函数"""
    logger.info("开始数据库修复过程...")
    
    # 1. 备份数据库
    backup_file = backup_database()
    if not backup_file and os.path.exists(DATABASE_NAME):
        logger.warning("备份失败，但将继续修复（请确保您有数据备份）")
    
    # 2. 修复数据库
    if fix_database_schema():
        logger.info("✅ 数据库修复成功！")
        logger.info("现在可以重新启动应用程序")
    else:
        logger.error("❌ 数据库修复失败！")
        if backup_file:
            logger.info(f"可以从备份文件恢复: {backup_file}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 