# Meme币量化分析软件

这是一个使用Python和Qt开发的meme币量化分析软件，专注于Solana链上的meme币分析和策略回测。

## 功能特点

1. **趋势榜单**：每分钟自动更新一次meme币趋势榜单
2. **K线图表**：查看选定meme币的K线蜡烛图
3. **技术指标**：支持MACD、RSI、SAR等常用技术指标
4. **策略回测**：对选定的meme币进行策略回测

## 安装方法

### 环境要求
- Python 3.8+
- 依赖包：见`requirements.txt`

### 安装步骤

1. 克隆仓库
```bash
git clone <仓库地址>
cd bt-gui
```

2. 安装依赖
```bash
pnpm install
pip install -r requirements.txt
```

3. 运行程序
```bash
python main.py
```

## 使用说明

### 趋势榜单
- 软件启动后自动显示meme币趋势榜单
- 榜单每分钟自动更新一次
- 点击榜单中的币种可查看详细信息

### K线图表
- 在趋势榜单中选择币种后，点击"查看K线"按钮
- 可选择不同的时间周期（1分钟、5分钟、15分钟等）
- 支持缩放和平移图表

### 技术指标
- 在K线图表下方可选择添加技术指标
- 支持的指标包括：MACD、RSI、SAR等
- 可自定义指标参数

### 策略回测
- 在"策略回测"选项卡中设置回测参数
- 选择回测时间范围
- 选择回测策略
- 查看回测结果和性能指标

## API说明

### 趋势榜单API
- 端点：`https://token-news-roan.vercel.app/api/tokens/aggregated-data`
- 返回格式：JSON
- 包含信息：代币地址、符号、名称、图片、市值、价格、价格变化等

### 价格数据API (OHLCV)
- 端点：`https://v0-api-caching-proxy.vercel.app/defi/ohlcv`
- 请求方式：GET
- 请求头：
  ```
  'X-API-KEY': BIRDEYE_API_KEY,
  'accept': 'application/json',
  'x-chain': 'solana'
  ```
- 请求参数：
  ```
  'address': token_address, // 代币合约地址
  'type': '1m', // 时间间隔，例如 "1m" 表示 1 分钟
  'time_from': start_timestamp, // 开始时间戳
  'time_to': end_timestamp, // 结束时间戳
  'limit': 1000 // 每页记录数，最大 1000
  ```

## 配置说明

在`config.py`文件中可以配置：
- API密钥
- 默认显示的时间周期
- 默认技术指标参数
- UI主题设置

## 常见问题

**Q: 为什么趋势榜单没有更新？**
A: 请检查网络连接和API是否可用。

**Q: 如何添加自定义策略？**
A: 在`strategies`目录下创建新的策略类，继承自`BaseStrategy`类。

## 许可证

MIT

## 问题修复与更新日志

### 2024-07-29
- **修复“信号监控”中“执行历史”信号快速消失的问题**：
    - **问题描述**：在“信号监控”窗口的“执行历史”表格中，显示的交易信号（如 'buy', 'sell'）在出现后很短时间就会消失。
    - **诊断过程**：
        1. 追踪发现“执行历史”表格的数据来源于 `SignalMonitorWindow` 类中的 `update_execution_table` 方法。
        2. 此方法每10秒由定时器调用，数据来自 `TradingExecutor` 类的 `get_execution_statistics` 方法返回的 `recent_executions` 列表。
        3. 关键在于 `TradingExecutor.get_execution_statistics` 方法内部硬编码只返回其完整执行历史 (`self.execution_history`) 中的最后10条记录。
        4. 同时，`TradingExecutor` 内部的 `self.execution_history` 列表本身也有大小限制（当超过1000条时，会截断保留最新的500条）。
        5. `SignalMonitorWindow.update_execution_table` 每次刷新时，会清空表格并用这最新的（最多）10条记录重新填充，导致旧信号被移除。
    - **解决方案**：
        - 修改了 `signal_monitor.py` 文件中 `TradingExecutor` 类的 `get_execution_statistics` 方法。
        - 将 `recent_executions = self.execution_history[-10:]` 更改为 `recent_executions = list(self.execution_history)`。
        - 这使得 `TradingExecutor` 返回其内部存储的所有执行历史（当前上限为500条），而不是仅仅最后10条。
    - **效果**：“执行历史”表格现在能够显示更完整的交易信号记录，信号不再因为之前只取最后10条的限制而迅速消失。
- **改进“信号监控”中“执行历史”的排序方式**：
    - **需求**：用户希望“执行历史”表格中的条目按时间倒序排列，最新的记录显示在最顶部。
    - **实现**：在 `SignalMonitorWindow.update_execution_table` 方法中，对从 `TradingExecutor` 获取并筛选后的执行记录列表，在填充到表格前，增加了按时间戳 (`timestamp`) 降序排序的逻辑。
    - **效果**：“执行历史”表格现在会优先展示最新的交易信号，方便用户快速查看最近的活动。
