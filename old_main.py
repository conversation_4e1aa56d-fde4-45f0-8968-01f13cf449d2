#!/usr/bin/env python
"""
趋势榜单组件测试脚本 - 用于测试趋势榜单组件的显示功能
"""

import sys
import os
import logging
from typing import List, Dict, Optional # Added typing
import random
import time
from datetime import datetime, timedelta
import multiprocessing
import concurrent.futures
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QTabWidget, QMessageBox, QHBoxLayout
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QTimer, pyqtSlot # Added QObject
import pandas as pd
import numpy as np

from simple_api_service import SimpleAPIService  # 用于多进程
from api_service import APIService  # 用于主线程GUI
from ui.trend_widget import TrendWidget
from ui.chart_widget import ChartWidget
from ui.backtest_widget import BacktestWidget
from ui.historical_tokens_widget import HistoricalTokensWidget
from ui.portfolio_widget import PortfolioWidget
from ui.live_trading_widget import LiveTradingWidget
from strategies import StrategyFactory, BaseStrategy
from indicators import TechnicalIndicators

# 🔥 日志开关：通过环境变量控制
# 设置 ENABLE_LOGGING=1 来开启日志，否则默认关闭
ENABLE_LOGGING = os.getenv('ENABLE_LOGGING', '0') == '1'
LOG_LEVEL = logging.DEBUG if ENABLE_LOGGING else logging.CRITICAL

logging.basicConfig(
    level=LOG_LEVEL,  # 🔥 使用动态日志级别
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('trend_widget_test')
logger.setLevel(LOG_LEVEL)  # 🔥 使用动态日志级别

# 🔥 日志开关提示
if ENABLE_LOGGING:
    print("📝 日志已开启 (ENABLE_LOGGING=1)")
else:
    print("🔇 日志已关闭 (设置 ENABLE_LOGGING=1 来开启日志)")

# 独立的回测函数，用于多进程执行
def run_single_backtest_process(token_info: dict, strategy_name: str, initial_capital: float) -> tuple:
    """
    独立进程中执行单个代币回测
    返回: (token_address, result_summary_dict, error_message)
    """
    try:
        # 只导入纯Python模块，避免PyQt5相关导入
        import sys
        import os
        
        # 添加项目路径到sys.path（确保在子进程中能找到模块）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        
        # 导入必要的模块（避免QtCore等PyQt5模块）
        from simple_api_service import SimpleAPIService  # 使用不依赖PyQt5的API服务
        from strategies import StrategyFactory
        from indicators import TechnicalIndicators
        import pandas as pd
        import numpy as np
        
        token_address = token_info.get("address")
        if not token_address:
            return token_address, None, "无效的代币地址"
        
        # 创建简化的API服务实例（不依赖PyQt5）
        api_service = SimpleAPIService()
        
        # 获取策略实例
        strategy = StrategyFactory.get_strategy_by_name(strategy_name)
        if not strategy:
            return token_address, None, f"策略 '{strategy_name}' 未找到"
        
        # 获取策略所需的时间周期
        primary_timeframe = strategy.get_primary_timeframe()
        aux_requirements = strategy.get_auxiliary_timeframes_and_indicators()
        
        # 获取主K线数据
        ohlcv_data_primary = api_service.get_ohlcv_data(
            token_address=token_address,
            timeframe=primary_timeframe, 
            days=1,
            source='historical' 
        )
        
        # 如果主周期数据获取失败，尝试重采样
        if not ohlcv_data_primary and primary_timeframe != '1m':
            ohlcv_data_1m = api_service.get_ohlcv_data(
                token_address=token_address, timeframe='1m', days=3, source='historical'
            )
            if ohlcv_data_1m:
                df_1m = pd.DataFrame(ohlcv_data_1m)
                if not df_1m.empty:
                    df_1m['datetime_pd'] = pd.to_datetime(df_1m['timestamp'], unit='s')
                    df_1m = df_1m.set_index('datetime_pd').sort_index()
                    
                    # 重采样逻辑
                    pd_resample_rule = primary_timeframe.replace('m', 'T').replace('h', 'H').replace('d', 'D')
                    ohlc_dict = {'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'}
                    cols_to_resample = {col: agg for col, agg in ohlc_dict.items() if col in df_1m.columns}
                    
                    if all(key in cols_to_resample for key in ['open', 'high', 'low', 'close']):
                        df_resampled = df_1m.resample(pd_resample_rule).agg(cols_to_resample)
                        df_resampled.dropna(subset=['open', 'high', 'low', 'close'], how='all', inplace=True)
                        
                        if not df_resampled.empty:
                            df_resampled['timestamp'] = df_resampled.index.astype(np.int64) // 10**9 
                            df_resampled['datetime'] = df_resampled.index.strftime('%Y-%m-%d %H:%M:%S')
                            ohlcv_data_primary = df_resampled.reset_index().to_dict(orient='records')
        
        if not ohlcv_data_primary:
            return token_address, None, f"获取主周期 ({primary_timeframe}) K线数据失败"
        
        # 处理数据
        df_primary = pd.DataFrame(ohlcv_data_primary)
        if 'datetime_pd' not in df_primary.columns:
            df_primary['datetime_pd'] = pd.to_datetime(df_primary['timestamp'], unit='s')
        if 'datetime' not in df_primary.columns:
            df_primary['datetime'] = df_primary['datetime_pd'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        df_primary = df_primary.set_index('datetime_pd').sort_index()
        df_primary = TechnicalIndicators.add_all_indicators(df_primary.copy())
        
        # 处理辅助周期数据
        merged_df = df_primary.copy()
        if aux_requirements:
            for aux_tf, aux_indicators_needed in aux_requirements.items():
                ohlcv_data_aux = api_service.get_ohlcv_data(
                    token_address=token_address, timeframe=aux_tf, days=3, source='historical'
                )
                if ohlcv_data_aux:
                    df_aux = pd.DataFrame(ohlcv_data_aux)
                    df_aux['datetime_pd'] = pd.to_datetime(df_aux['timestamp'], unit='s')
                    df_aux = df_aux.set_index('datetime_pd').sort_index()
                    df_aux = TechnicalIndicators.add_all_indicators(df_aux.copy())
                    
                    for indicator_name in aux_indicators_needed:
                        if indicator_name in df_aux.columns:
                            new_col_name = f"{indicator_name}_{aux_tf}"
                            merged_df[new_col_name] = df_aux[indicator_name].reindex(merged_df.index, method='ffill')
        
        # 生成信号并执行回测
        final_df_for_strategy = strategy.generate_signals(merged_df)
        if 'signal' not in final_df_for_strategy.columns:
            return token_address, None, "策略未能生成信号列"
        
        result_summary = strategy.backtest(final_df_for_strategy, initial_capital)
        return token_address, result_summary, None
        
    except Exception as e:
        import traceback
        error_details = f"{str(e)}\n{traceback.format_exc()}"
        return token_info.get("address", "unknown"), None, error_details

# Removed MockData and commented out mock ChartWidget/BacktestWidget as they are no longer primary

# --- SingleBacktestThread class ---
class SingleBacktestThread(QThread):
    # Signal to emit results: token_address, result_summary_dict
    backtest_result_ready = pyqtSignal(str, dict)
    # Signal to emit errors: token_address, error_message_str
    backtest_error = pyqtSignal(str, str)

    def __init__(self, api_service: APIService, token_info: dict, strategy_name: str, initial_capital: float, parent=None):
        super().__init__(parent)
        self.api_service = api_service
        self.token_info = token_info
        self.strategy_name = strategy_name
        self.initial_capital = initial_capital
        self.token_address = token_info.get("address", "Unknown")

    def run(self):
        logger.info(f"SingleBacktestThread: Starting for {self.token_address}, Strategy: {self.strategy_name}")
        try:
            if not self.token_address:
                self.backtest_error.emit(self.token_address or "UnknownAddress", "无效的代币地址。")
                return

            # 3. 获取策略实例 (移到获取数据之前，以便知道需要哪个timeframe)
            strategy: Optional[BaseStrategy] = StrategyFactory.get_strategy_by_name(self.strategy_name)
            if not strategy:
                self.backtest_error.emit(self.token_address, f"策略 '{self.strategy_name}' 未找到。")
                return
            
            # 获取策略所需的时间周期
            primary_timeframe = strategy.get_primary_timeframe()
            aux_requirements = strategy.get_auxiliary_timeframes_and_indicators()
            logger.info(f"SingleBacktestThread: Strategy '{self.strategy_name}' requires primary timeframe '{primary_timeframe}' and auxiliary: {aux_requirements}")

            # 1. 获取主K线数据
            ohlcv_data_primary = self.api_service.get_ohlcv_data(
                token_address=self.token_address,
                timeframe=primary_timeframe, 
                days=1, # 对自定义API影响不大，对Birdeye可能需要调整天数以获取足够辅助周期数据
                source='historical' 
            )

            # 如果直接获取主周期数据失败或为空，并且主周期不是1m，则尝试获取1m数据并重采样
            if not ohlcv_data_primary and primary_timeframe != '1m':
                logger.warning(f"SingleBacktestThread: Failed to get primary timeframe {primary_timeframe} data directly. Attempting to fetch 1m data for resampling.")
                ohlcv_data_1m = self.api_service.get_ohlcv_data(
                    token_address=self.token_address,
                    timeframe='1m',
                    days=3, # 获取更长时间范围的1m数据以确保覆盖
                    source='historical'
                )
                if ohlcv_data_1m:
                    logger.info(f"SingleBacktestThread: Successfully fetched {len(ohlcv_data_1m)} 1m records for resampling to {primary_timeframe}.")
                    df_1m = pd.DataFrame(ohlcv_data_1m)
                    if not df_1m.empty:
                        df_1m['datetime_pd'] = pd.to_datetime(df_1m['timestamp'], unit='s')
                        df_1m = df_1m.set_index('datetime_pd').sort_index()
                        
                        # 执行重采样
                        # resample_rule = primary_timeframe # e.g., '5m', '15m', '1h' # Original

                        # 将策略中的时间周期字符串转换为Pandas兼容的offset alias
                        pd_resample_rule = primary_timeframe
                        if pd_resample_rule.endswith('m') and not pd_resample_rule.startswith('M'): # 确保不是 'ME' (Month End) 之类的
                            pd_resample_rule = pd_resample_rule.replace('m', 'T')  # e.g., "5m" -> "5T", "15m" -> "15T"
                        elif pd_resample_rule.endswith('h'):
                            pd_resample_rule = pd_resample_rule.replace('h', 'H')  # e.g., "1h" -> "1H", "4h" -> "4H"
                        elif pd_resample_rule.endswith('d'):
                            pd_resample_rule = pd_resample_rule.replace('d', 'D')  # e.g., "1d" -> "1D"
                        # 可以根据需要添加更多转换规则，例如 'w' -> 'W' for week
                        
                        logger.info(f"SingleBacktestThread: Original timeframe '{primary_timeframe}', Pandas resample rule: '{pd_resample_rule}'")

                        ohlc_dict = {
                            'open': 'first',
                            'high': 'max',
                            'low': 'min',
                            'close': 'last',
                            'volume': 'sum'
                        }
                        # 保留原始的 timestamp (取第一个) 和其他可能需要的列 (取第一个)
                        # 注意：如果原始数据有其他列，需要决定如何聚合它们
                        # 这里我们简单地只聚合OHLCV，并重新生成timestamp列
                        
                        # 筛选出必要的列进行重采样，避免不存在的列导致错误
                        cols_to_resample = {col: agg for col, agg in ohlc_dict.items() if col in df_1m.columns}
                        if not all(key in cols_to_resample for key in ['open', 'high', 'low', 'close']):
                            logger.error(f"SingleBacktestThread: 1m data is missing one or more OHLC columns for resampling. Available: {df_1m.columns.tolist()}")
                            # 如果缺少关键OHLC列，则重采样无意义，恢复 ohlcv_data_primary 为空，让后续逻辑处理错误
                            ohlcv_data_primary = None 
                        else:
                            df_resampled = df_1m.resample(pd_resample_rule).agg(cols_to_resample) # 使用修正后的 pd_resample_rule
                            df_resampled.dropna(subset=['open', 'high', 'low', 'close'], how='all', inplace=True) # 删除完全是NaN的行

                            if not df_resampled.empty:
                                # 重建 'timestamp' 和其他必要的列
                                # 使用每个周期的开始时间作为timestamp
                                df_resampled['timestamp'] = df_resampled.index.astype(np.int64) // 10**9 
                                # 添加 'datetime' 字符串列，与 base_strategy.py 中的期望一致
                                df_resampled['datetime'] = df_resampled.index.strftime('%Y-%m-%d %H:%M:%S')
                                
                                # 将重采样后的DataFrame转换回字典列表格式，以便后续流程使用
                                ohlcv_data_primary = df_resampled.reset_index().to_dict(orient='records')
                                logger.info(f"SingleBacktestThread: Resampled 1m data to {len(ohlcv_data_primary)} {primary_timeframe} records.")
                            else:
                                logger.warning(f"SingleBacktestThread: Resampling 1m data to {primary_timeframe} resulted in an empty DataFrame.")
                                ohlcv_data_primary = None # 如果重采样后为空，则视作获取失败
                    else:
                        logger.warning("SingleBacktestThread: Fetched 1m data for resampling, but it's an empty DataFrame.")
                        ohlcv_data_primary = None # 如果1m数据处理后为空
                else:
                    logger.warning(f"SingleBacktestThread: Failed to fetch 1m data for resampling as well.")
                    # ohlcv_data_primary 保持原样 (None or empty)

            if not ohlcv_data_primary:
                self.backtest_error.emit(self.token_address, f"获取主周期 ({primary_timeframe}) K线数据失败或无数据 (尝试重采样后仍失败)。")
                return
            
            df_primary = pd.DataFrame(ohlcv_data_primary)
            if df_primary.empty:
                self.backtest_error.emit(self.token_address, f"主周期 ({primary_timeframe}) K线数据处理后为空。")
                return
            
            # 确保主数据有 datetime_pd 索引并排序
            # 如果 ohlcv_data_primary 来自重采样，它已经有了 datetime_pd 索引
            # 如果是直接获取的数据，原始的 timestamp 列需要转换
            if 'datetime_pd' not in df_primary.columns:
                 df_primary['datetime_pd'] = pd.to_datetime(df_primary['timestamp'], unit='s')
            
            # 确保 'datetime' 列存在，如果不存在则从 datetime_pd 创建
            if 'datetime' not in df_primary.columns and 'datetime_pd' in df_primary.columns:
                df_primary['datetime'] = df_primary['datetime_pd'].dt.strftime('%Y-%m-%d %H:%M:%S')
            elif 'datetime' not in df_primary.columns and 'timestamp' in df_primary.columns:
                # 作为备选，如果连 datetime_pd 都没有但有 timestamp
                df_primary['datetime'] = pd.to_datetime(df_primary['timestamp'], unit='s').dt.strftime('%Y-%m-%d %H:%M:%S')

            df_primary = df_primary.set_index('datetime_pd').sort_index()
            
            logger.info(f"SingleBacktestThread: Fetched {len(df_primary)} OHLCV records for primary timeframe {primary_timeframe} for {self.token_address}.")
            df_primary = TechnicalIndicators.add_all_indicators(df_primary.copy()) # 计算主周期指标

            # 2. 获取并合并辅助周期指标
            merged_df = df_primary.copy()
            if aux_requirements:
                for aux_tf, aux_indicators_needed in aux_requirements.items():
                    logger.info(f"SingleBacktestThread: Fetching auxiliary data for timeframe {aux_tf}")
                    # 对于辅助数据，可能需要更多天数以确保覆盖主数据的时间范围
                    # 这里的 days 参数可能需要根据 primary_timeframe 和 aux_tf 的关系动态调整
                    # 简化处理：暂时仍用 days=1，但APIService的聚合应该能处理
                    ohlcv_data_aux = self.api_service.get_ohlcv_data(
                        token_address=self.token_address,
                        timeframe=aux_tf,
                        days=3, # 尝试获取更多天数以确保覆盖，APIService会负责聚合
                        source='historical'
                    )
                    if not ohlcv_data_aux:
                        logger.warning(f"SingleBacktestThread: 获取辅助周期 ({aux_tf}) K线数据失败或无数据。跳过此周期。")
                        continue
                    
                    df_aux = pd.DataFrame(ohlcv_data_aux)
                    if df_aux.empty:
                        logger.warning(f"SingleBacktestThread: 辅助周期 ({aux_tf}) K线数据处理后为空。跳过此周期。")
                        continue
                    
                    df_aux['datetime_pd'] = pd.to_datetime(df_aux['timestamp'], unit='s')
                    df_aux = df_aux.set_index('datetime_pd').sort_index()
                    df_aux = TechnicalIndicators.add_all_indicators(df_aux.copy())

                    for indicator_name in aux_indicators_needed:
                        if indicator_name in df_aux.columns:
                            # 生成新列名，例如 sar_5m, low_1h (确保与策略类中的期望一致)
                            timeframe_suffix = aux_tf # 使用原始的时间周期字符串作为后缀
                            new_col_name = f"{indicator_name}_{timeframe_suffix}" 
                            
                            merged_df[new_col_name] = df_aux[indicator_name].reindex(merged_df.index, method='ffill')
                            logger.info(f"SingleBacktestThread: Merged auxiliary indicator '{new_col_name}' from {aux_tf} to main df.")
                        else:
                            logger.warning(f"SingleBacktestThread: Indicator '{indicator_name}' not found in auxiliary df for {aux_tf}. Skipping.")
            
            logger.info(f"Merged DF for strategy head:\n{merged_df.head()}")
            if aux_requirements:
                for aux_tf, aux_indicators_needed in aux_requirements.items():
                    for indicator_name in aux_indicators_needed:
                        new_col_name = f"{indicator_name}_{aux_tf}"
                        if new_col_name in merged_df.columns:
                            logger.info(f"Sample of merged column {new_col_name}: {merged_df[new_col_name].dropna().head().tolist()}")
                        else:
                            logger.warning(f"Column {new_col_name} was expected but not found in merged_df after processing {aux_tf}")

            # 现在 merged_df 包含了主周期数据和所有映射过来的辅助周期指标
            # df = TechnicalIndicators.add_all_indicators(df.copy()) # 主周期的指标已在df_primary计算
            # merged_df 已经是我们最终要给策略的df

            # 4. 生成交易信号 (必须在调用 backtest 之前)
            logger.info(f"SingleBacktestThread: Generating signals for {self.token_address} using {self.strategy_name} on merged multi-timeframe data.")
            final_df_for_strategy = strategy.generate_signals(merged_df) # 传递合并后的df
            if 'signal' not in final_df_for_strategy.columns:
                logger.error(f"SingleBacktestThread: 'signal' column not found after strategy.generate_signals() for {self.token_address}.")
                self.backtest_error.emit(self.token_address, "策略未能生成信号列。")
                return
            logger.info(f"SingleBacktestThread: Signals generated for {self.token_address}. Example signals: {final_df_for_strategy['signal'].value_counts().to_dict()}")

            # 5. 执行回测
            result_summary = strategy.backtest(final_df_for_strategy, self.initial_capital)
            logger.info(f"SingleBacktestThread: Backtest completed for {self.token_address}. Profit %: {result_summary.get('profit_percentage', 0):.2f}")
            
            self.backtest_result_ready.emit(self.token_address, result_summary)

        except Exception as e:
            logger.error(f"SingleBacktestThread: Error during backtest for {self.token_address}: {e}", exc_info=True)
            self.backtest_error.emit(self.token_address, str(e))
# --- End of SingleBacktestThread class ---

# --- BatchBacktestExecutionManager class (多进程版本) ---
class BatchBacktestExecutionManager(QObject):
    # Signals to forward to MainWindow for UI updates
    token_processing_started = pyqtSignal(str)  # token_address
    token_result_ready = pyqtSignal(str, dict)  # token_address, result_summary
    token_error_occurred = pyqtSignal(str, str) # token_address, error_message
    all_tokens_processed = pyqtSignal()

    def __init__(self, api_service: APIService, tokens_info_list: List[Dict], 
                 strategy_name: str, initial_capital: float, max_workers: int = None, parent=None):
        super().__init__(parent)
        self.tokens_info_list = list(tokens_info_list)  # Make a copy
        self.strategy_name = strategy_name
        self.initial_capital = initial_capital
        
        # 设置进程数：默认为CPU核心数，但不超过代币数量
        if max_workers is None:
            max_workers = min(multiprocessing.cpu_count(), len(tokens_info_list), 4)  # 最多4个进程
        self.max_workers = max_workers
        
        self.total_tokens = len(tokens_info_list)
        self.completed_count = 0
        self.active_futures = {}  # future -> token_address mapping
        self.executor = None
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._check_completed_futures)

    def start_batch(self):
        logger.info(f"BatchBacktestManager: Starting MULTIPROCESSING batch processing for {len(self.tokens_info_list)} tokens with max_workers={self.max_workers}.")
        
        try:
            # 创建进程池
            self.executor = concurrent.futures.ProcessPoolExecutor(max_workers=self.max_workers)
            
            # 提交所有任务到进程池
            for token_info in self.tokens_info_list:
                token_address = token_info.get("address")
                if token_address:
                    self.token_processing_started.emit(token_address)
                    
                    # 提交任务到进程池
                    future = self.executor.submit(
                        run_single_backtest_process, 
                        token_info, 
                        self.strategy_name, 
                        self.initial_capital
                    )
                    self.active_futures[future] = token_address
                    logger.info(f"BatchBacktestManager: Submitted process for {token_address}")
            
            # 启动监控定时器
            self.monitor_timer.start(500)  # 每500ms检查一次完成状态
            
        except Exception as e:
            logger.error(f"BatchBacktestManager: Error starting multiprocessing: {e}")
            self.all_tokens_processed.emit()

    def _check_completed_futures(self):
        """检查已完成的future"""
        completed_futures = []
        
        for future, token_address in list(self.active_futures.items()):
            if future.done():
                completed_futures.append((future, token_address))
        
        # 处理完成的任务
        for future, token_address in completed_futures:
            try:
                # 获取结果
                result_token_address, result_summary, error_message = future.result(timeout=1)
                
                if error_message:
                    self.token_error_occurred.emit(result_token_address or token_address, error_message)
                else:
                    self.token_result_ready.emit(result_token_address, result_summary)
                
                self.completed_count += 1
                logger.info(f"BatchBacktestManager: Process completed for {token_address} ({self.completed_count}/{self.total_tokens})")
                
            except concurrent.futures.TimeoutError:
                logger.warning(f"BatchBacktestManager: Timeout getting result for {token_address}")
                self.token_error_occurred.emit(token_address, "处理超时")
                self.completed_count += 1
                
            except Exception as e:
                logger.error(f"BatchBacktestManager: Error processing result for {token_address}: {e}")
                self.token_error_occurred.emit(token_address, f"处理错误: {str(e)}")
                self.completed_count += 1
            
            # 从活跃列表中移除
            del self.active_futures[future]
        
        # 检查是否所有任务都完成
        if self.completed_count >= self.total_tokens:
            self.monitor_timer.stop()
            self._cleanup()
            logger.info(f"BatchBacktestManager: All multiprocessing completed! Total processed: {self.completed_count}/{self.total_tokens}")
            self.all_tokens_processed.emit()

    def _cleanup(self):
        """清理资源"""
        try:
            if self.executor:
                logger.info("BatchBacktestManager: 开始强制清理ProcessPoolExecutor...")
                
                # 1. 尝试取消所有未完成的任务
                for future in list(self.active_futures.keys()):
                    try:
                        if not future.done():
                            future.cancel()
                            logger.info(f"BatchBacktestManager: 已取消future任务")
                    except Exception as e:
                        logger.warning(f"BatchBacktestManager: 取消future任务失败: {e}")
                
                # 2. 强制关闭进程池（不等待）
                self.executor.shutdown(wait=False)
                logger.info("BatchBacktestManager: ProcessPoolExecutor.shutdown(wait=False) 完成")
                
                # 3. 清理引用
                self.executor = None
                self.active_futures.clear()
                
                logger.info("BatchBacktestManager: ProcessPoolExecutor 强制清理完成")
                
        except Exception as e:
            logger.error(f"BatchBacktestManager: 清理过程中出错: {e}")
        
        # 4. 停止监控定时器
        try:
            if hasattr(self, 'monitor_timer') and self.monitor_timer:
                self.monitor_timer.stop()
                logger.info("BatchBacktestManager: 监控定时器已停止")
        except Exception as e:
            logger.error(f"BatchBacktestManager: 停止监控定时器时出错: {e}")

    def force_cleanup(self):
        """强制清理所有资源 - 用于程序关闭时"""
        logger.info("BatchBacktestManager: 执行强制清理...")
        
        # 停止定时器
        try:
            if hasattr(self, 'monitor_timer') and self.monitor_timer:
                self.monitor_timer.stop()
        except Exception:
            pass
        
        # 强制终止进程池
        try:
            if self.executor:
                logger.info("BatchBacktestManager: 强制终止所有子进程...")
                
                # 尝试使用psutil精确管理进程（如果可用）
                try:
                    import psutil
                    # 尝试终止所有运行中的进程
                    if hasattr(self.executor, '_processes'):
                        for process in self.executor._processes:
                            try:
                                if process.is_alive():
                                    process.terminate()  # 发送SIGTERM
                                    logger.info(f"BatchBacktestManager: 已发送终止信号到进程 {process.pid}")
                            except Exception as e:
                                logger.warning(f"BatchBacktestManager: 终止进程失败: {e}")
                    
                    # 等待一小段时间让进程自然退出
                    import time
                    time.sleep(0.2)
                    
                    # 如果还有存活的进程，强制杀死
                    if hasattr(self.executor, '_processes'):
                        for process in self.executor._processes:
                            try:
                                if process.is_alive():
                                    process.kill()  # 发送SIGKILL
                                    logger.info(f"BatchBacktestManager: 已强制杀死进程 {process.pid}")
                            except Exception as e:
                                logger.warning(f"BatchBacktestManager: 强制杀死进程失败: {e}")
                                
                except ImportError:
                    logger.info("BatchBacktestManager: psutil 未安装，使用基础进程清理")
                    # 基础清理：等待一下让进程自然退出
                    import time
                    time.sleep(0.5)
                
                # 关闭进程池
                self.executor.shutdown(wait=False)
                self.executor = None
                
        except Exception as e:
            logger.error(f"BatchBacktestManager: 强制清理进程池时出错: {e}")
        
        # 清理其他资源
        self.active_futures.clear()
        self.completed_count = self.total_tokens  # 标记为已完成，避免继续处理

    def get_progress_info(self) -> dict:
        """获取当前进度信息，供UI显示"""
        active = len(self.active_futures)
        remaining = self.total_tokens - self.completed_count - active
        return {
            'completed': self.completed_count,
            'active': active, 
            'remaining': max(0, remaining),
            'total': self.total_tokens,
            'progress_percentage': (self.completed_count / self.total_tokens * 100) if self.total_tokens > 0 else 0
        }

# --- End of BatchBacktestExecutionManager class ---

class MainWindow(QMainWindow):
    """测试窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("综合交易分析工具")
        self.resize(1200, 800)
        self.api_service = APIService(self) 
        self.chart_windows = []
        self.backtest_windows = []
        self.single_backtest_threads = [] # To keep references to running single backtest threads
        self.batch_manager: Optional[BatchBacktestExecutionManager] = None # For the batch process
        self.active_batch_threads_managed_by_mainwindow = [] # If MainWindow directly manages SingleBacktestThreads for batch (alternative to manager)

        main_container = QWidget()
        self.setCentralWidget(main_container)
        main_layout = QVBoxLayout(main_container)
        self.central_widget = QTabWidget()
        main_layout.addWidget(self.central_widget)

        self.trend_tab = QWidget()
        self.trend_layout = QVBoxLayout(self.trend_tab)
        self.trend_widget = TrendWidget(self.trend_tab) 
        if hasattr(self.trend_widget, 'api_service') and self.trend_widget.api_service is None:
            self.trend_widget.api_service = self.api_service
        self.trend_layout.addWidget(self.trend_widget)
        self.central_widget.addTab(self.trend_tab, "趋势榜单")

        self.historical_tokens_tab = QWidget()
        self.historical_tokens_layout = QVBoxLayout(self.historical_tokens_tab)
        self.historical_tokens_widget = HistoricalTokensWidget(api_service=self.api_service, parent=self.historical_tokens_tab)
        self.historical_tokens_layout.addWidget(self.historical_tokens_widget)
        self.central_widget.addTab(self.historical_tokens_tab, "历史代币")
        
        # Portfolio 标签页
        self.portfolio_tab = QWidget()
        self.portfolio_layout = QVBoxLayout(self.portfolio_tab)
        self.portfolio_widget = PortfolioWidget(api_service=self.api_service, parent=self.portfolio_tab)
        self.portfolio_layout.addWidget(self.portfolio_widget)
        self.central_widget.addTab(self.portfolio_tab, "Portfolio")
        
        # 实盘交易标签页
        self.live_trading_tab = QWidget()
        self.live_trading_layout = QVBoxLayout(self.live_trading_tab)
        self.live_trading_widget = LiveTradingWidget(parent=self.live_trading_tab)
        self.live_trading_layout.addWidget(self.live_trading_widget)
        self.central_widget.addTab(self.live_trading_tab, "实盘交易")
        
        # 🔥 设置"实盘交易"为默认页面
        self.central_widget.setCurrentWidget(self.live_trading_tab)

        bottom_button_widget = QWidget()
        bottom_buttons_layout = QHBoxLayout(bottom_button_widget)
        bottom_buttons_layout.setContentsMargins(10, 5, 10, 5)
        self.view_chart_button = QPushButton("查看K线")
        self.view_chart_button.setFixedWidth(150)
        self.view_chart_button.setEnabled(False)
        self.view_chart_button.clicked.connect(self.on_view_chart_clicked)
        bottom_buttons_layout.addWidget(self.view_chart_button)
        self.backtest_button = QPushButton("策略回测")
        self.backtest_button.setFixedWidth(150)
        self.backtest_button.setEnabled(False)
        self.backtest_button.clicked.connect(self.on_backtest_clicked)
        bottom_buttons_layout.addWidget(self.backtest_button)
        bottom_buttons_layout.addStretch()
        main_layout.addWidget(bottom_button_widget)

        self.current_selected_token = None

        if self.trend_widget:
            self.trend_widget.token_selected.connect(self.on_token_selected_from_any_tab)
        if self.historical_tokens_widget:
            self.historical_tokens_widget.token_selected.connect(self.on_token_selected_from_any_tab)
            self.historical_tokens_widget.individual_backtest_requested.connect(self.on_individual_backtest_request_received)
            self.historical_tokens_widget.batch_backtest_requested.connect(self.on_batch_backtest_request_received)
        
        self.central_widget.currentChanged.connect(self.on_tab_changed)
        self.on_tab_changed(self.central_widget.currentIndex()) 

    def closeEvent(self, event):
        """窗口关闭事件处理 - 确保清理所有进程资源"""
        logger.info("MainWindow: 正在关闭窗口，清理资源...")
        
        # 1. 清理批量回测管理器和其进程池
        if self.batch_manager:
            logger.info("MainWindow: 强制停止批量回测进程池...")
            try:
                # 使用新的强制清理方法
                self.batch_manager.force_cleanup()
                self.batch_manager = None
                logger.info("MainWindow: 批量回测管理器已强制清理")
                
            except Exception as e:
                logger.error(f"MainWindow: 清理批量回测管理器时出错: {e}")
        
        # 额外的进程清理 - 确保没有遗留的python子进程
        try:
            import psutil
            import os
            current_pid = os.getpid()
            current_process = psutil.Process(current_pid)
            
            # 查找并终止所有子进程
            for child in current_process.children(recursive=True):
                try:
                    logger.info(f"MainWindow: 发现子进程 {child.pid}，正在终止...")
                    child.terminate()
                except Exception as e:
                    logger.warning(f"MainWindow: 终止子进程 {child.pid} 失败: {e}")
            
            # 等待子进程退出
            psutil.wait_procs(current_process.children(), timeout=1)
            
            # 强制杀死仍存活的子进程
            for child in current_process.children(recursive=True):
                try:
                    if child.is_running():
                        logger.info(f"MainWindow: 强制杀死子进程 {child.pid}")
                        child.kill()
                except Exception as e:
                    logger.warning(f"MainWindow: 强制杀死子进程失败: {e}")
                    
        except ImportError:
            logger.info("MainWindow: psutil 未安装，使用基础清理方法")
            # 基础清理：只能关闭executor，无法精确管理子进程
            try:
                import time
                time.sleep(0.5)  # 给进程一些时间自然退出
            except Exception:
                pass
        except Exception as e:
            logger.error(f"MainWindow: 额外进程清理时出错: {e}")
        
        # 2. 清理单个回测线程
        for thread in self.single_backtest_threads:
            if thread.isRunning():
                logger.info(f"MainWindow: 停止单个回测线程...")
                thread.quit()
                thread.wait(1000)  # 等待最多1秒
        
        # 3. 清理图表和回测窗口
        for window in self.chart_windows:
            try:
                window.close()
            except Exception:
                pass
        
        for window in self.backtest_windows:
            try:
                window.close()
            except Exception:
                pass
        
        # 4. 清理Portfolio组件
        if hasattr(self, 'portfolio_widget') and self.portfolio_widget:
            try:
                self.portfolio_widget.close()
                logger.info("MainWindow: Portfolio组件已清理")
            except Exception as e:
                logger.error(f"MainWindow: 清理Portfolio组件时出错: {e}")
        
        # 5. 清理实盘交易组件
        if hasattr(self, 'live_trading_widget') and self.live_trading_widget:
            try:
                self.live_trading_widget.close()
                logger.info("MainWindow: 实盘交易组件已清理")
            except Exception as e:
                logger.error(f"MainWindow: 清理实盘交易组件时出错: {e}")
        
        # 6. 清理API服务
        if hasattr(self, 'api_service') and self.api_service:
            try:
                # 如果APIService有清理方法，调用它
                if hasattr(self.api_service, '__del__'):
                    self.api_service.__del__()
            except Exception as e:
                logger.error(f"MainWindow: 清理API服务时出错: {e}")
        
        logger.info("MainWindow: 资源清理完成，接受关闭事件")
        event.accept()  # 接受关闭事件
        
        # 7. 强制退出应用程序
        from PyQt5.QtWidgets import QApplication
        QApplication.instance().quit()

    @pyqtSlot(int)
    def on_tab_changed(self, index):
        logger.info(f"MainWindow: Tab changed to index {index}")
        self.current_selected_token = None
        self.view_chart_button.setEnabled(False)
        self.backtest_button.setEnabled(False)

        # 获取当前活动的标签页
        current_tab_widget = self.central_widget.currentWidget()
        
        # 在实盘交易和Portfolio页面隐藏查看K线和策略回测按钮
        if current_tab_widget in [self.live_trading_tab, self.portfolio_tab]:
            self.view_chart_button.hide()
            self.backtest_button.hide()
        else:
            self.view_chart_button.show()
            self.backtest_button.show()

    @pyqtSlot(dict)
    def on_token_selected_from_any_tab(self, token: dict):
        current_tab_widget_object = self.central_widget.currentWidget()
        active_widget = None
        if current_tab_widget_object == self.trend_tab:
            active_widget = self.trend_widget
        elif current_tab_widget_object == self.historical_tokens_tab:
            active_widget = self.historical_tokens_widget
        
        if not token or not token.get('address'): 
            self.current_selected_token = None
            self.view_chart_button.setEnabled(False)
            self.backtest_button.setEnabled(False)
        else:
            expected_source_for_active_tab = None
            if active_widget == self.trend_widget:
                expected_source_for_active_tab = "trend"
            elif active_widget == self.historical_tokens_widget:
                expected_source_for_active_tab = "historical"
            actual_token_source = token.get("source")
            if actual_token_source == expected_source_for_active_tab:
                self.current_selected_token = token
                self.view_chart_button.setEnabled(True)
                if actual_token_source == 'historical':
                    self.backtest_button.setEnabled(True)
                else:
                    self.backtest_button.setEnabled(False)
            else:
                if not self.current_selected_token or self.current_selected_token.get("source") != expected_source_for_active_tab:
                    self.view_chart_button.setEnabled(False)
                    self.backtest_button.setEnabled(False)

    @pyqtSlot()
    def on_view_chart_clicked(self):
        logger.info("MainWindow: 'View Chart' button clicked.")
        if self.current_selected_token:
            self.open_chart_window(self.current_selected_token)
        else:
            QMessageBox.warning(self, "未选择代币", "请先在列表中选择一个代币。")
    
    @pyqtSlot()
    def on_backtest_clicked(self):
        logger.info("MainWindow: 'Backtest' button clicked.")
        if self.current_selected_token:
            if self.current_selected_token.get('source') == 'historical':
                self.open_backtest_window(self.current_selected_token)
            else:
                QMessageBox.information(self, "操作限制", "策略回测功能仅适用于从'历史代币'列表选择的代币。")
        else:
            QMessageBox.warning(self, "未选择代币", "请先在列表中选择一个代币。")

    @pyqtSlot(dict, str, float) 
    def on_individual_backtest_request_received(self, token_info: dict, strategy_name: str, initial_capital: float):
        logger.info(f"MainWindow: Received individual backtest request for {token_info.get('symbol')} with strategy {strategy_name}")
        if token_info and token_info.get('address') and token_info.get('source') == 'historical':
            thread = SingleBacktestThread(self.api_service, token_info, strategy_name, initial_capital, self) # Parent to MainWindow
            thread.backtest_result_ready.connect(self.handle_single_backtest_result)
            thread.backtest_error.connect(self.handle_single_backtest_error)
            # No need to add to self.single_backtest_threads if parented, Qt handles cleanup
            # thread.finished.connect(lambda t=thread: self.single_backtest_threads.remove(t) if t in self.single_backtest_threads else None) 
            # self.single_backtest_threads.append(thread)
            thread.start()
        else:
            logger.warning(f"MainWindow: Invalid token_info for individual backtest: {token_info}")
            if self.historical_tokens_widget and token_info.get('address'):
                 self.historical_tokens_widget.update_token_backtest_status(token_info.get('address'), "无效请求")

    @pyqtSlot(str, dict)
    def handle_single_backtest_result(self, token_address: str, result_summary: dict):
        logger.info(f"MainWindow: Single backtest result ready for {token_address}. Profit %: {result_summary.get('profit_percentage', 0):.2f}")
        if self.historical_tokens_widget:
            self.historical_tokens_widget.update_token_backtest_status(token_address, "完成", result_summary)

    @pyqtSlot(str, str)
    def handle_single_backtest_error(self, token_address: str, error_message: str):
        logger.error(f"MainWindow: Single backtest error for {token_address}: {error_message}")
        if self.historical_tokens_widget:
            self.historical_tokens_widget.update_token_backtest_status(token_address, f"失败: {error_message[:30]}...")

    @pyqtSlot(list, str, float) 
    def on_batch_backtest_request_received(self, tokens: List[Dict], strategy_name: str, initial_capital: float):
        logger.info(f"MainWindow: Received batch backtest request for {len(tokens)} tokens, Strategy: {strategy_name}, Capital: {initial_capital}")
        # 检查是否有批量回测正在进行（多进程处理）
        if self.batch_manager and (self.batch_manager.active_futures or self.batch_manager.completed_count < self.batch_manager.total_tokens):
            QMessageBox.warning(self, "批量回测进行中", "当前已有批量回测任务在运行。请等待其完成后再开始新的任务。")
            return

        # 获取CPU核心数信息
        cpu_cores = multiprocessing.cpu_count()
        actual_workers = min(cpu_cores, len(tokens), 32)

        self.batch_manager = BatchBacktestExecutionManager(
            self.api_service, tokens, strategy_name, initial_capital, max_workers=actual_workers, parent=self
        )
        self.batch_manager.token_processing_started.connect(self.handle_batch_token_processing_started)
        self.batch_manager.token_result_ready.connect(self.handle_batch_token_result_ready)
        self.batch_manager.token_error_occurred.connect(self.handle_batch_token_error_occurred)
        self.batch_manager.all_tokens_processed.connect(self.handle_batch_all_tokens_processed)
        self.batch_manager.start_batch()
        
        # 更新历史代币界面的状态
        if self.historical_tokens_widget:
            self.historical_tokens_widget.status_label.setText(f"🚀 正在多进程回测 {len(tokens)} 个代币（策略: {strategy_name}，进程数: {actual_workers}/{cpu_cores}核）...")
        
        QMessageBox.information(self, "多进程批量回测", f"开始对 {len(tokens)} 个代币进行多进程批量回测！\n💻 使用 {actual_workers} 个进程（总共 {cpu_cores} 个CPU核心）\n🚀 I/O密集型任务，高并发处理！\n⚡ 充分利用网络等待时间，大幅提升速度！")

    @pyqtSlot(str)
    def handle_batch_token_processing_started(self, token_address: str):
        logger.info(f"MainWindow: Batch - Processing started for {token_address}")
        if self.historical_tokens_widget:
            self.historical_tokens_widget.update_token_backtest_status(token_address, "多进程测试中...")

    @pyqtSlot(str, dict)
    def handle_batch_token_result_ready(self, token_address: str, result_summary: dict):
        logger.info(f"MainWindow: Batch - Result for {token_address}. Profit %: {result_summary.get('profit_percentage', 0):.2f}")
        if self.historical_tokens_widget:
            self.historical_tokens_widget.update_token_backtest_status(token_address, "多进程完成", result_summary)

    @pyqtSlot(str, str)
    def handle_batch_token_error_occurred(self, token_address: str, error_message: str):
        logger.error(f"MainWindow: Batch - Error for {token_address}: {error_message}")
        if self.historical_tokens_widget:
            self.historical_tokens_widget.update_token_backtest_status(token_address, f"多进程失败: {error_message[:20]}...")

    @pyqtSlot()
    def handle_batch_all_tokens_processed(self):
        logger.info("MainWindow: Batch - All tokens processed.")
        if self.batch_manager:
            progress_info = self.batch_manager.get_progress_info()
            completed_count = progress_info['completed']
            total_count = progress_info['total']
            cpu_cores = multiprocessing.cpu_count()
            
            # 更新历史代币界面的状态
            if self.historical_tokens_widget:
                self.historical_tokens_widget.status_label.setText(f"✅ 多进程批量回测完成！处理了 {completed_count}/{total_count} 个代币")
            
            QMessageBox.information(self, "多进程批量回测完成", f"所有 {total_count} 个代币的多进程批量回测已处理完毕！\n✅ 成功完成: {completed_count} 个\n💻 利用了 {self.batch_manager.max_workers}/{cpu_cores} 个CPU核心\n⚡ 高并发I/O处理，充分利用网络等待时间！")
            # self.batch_manager.deleteLater() # Schedule for deletion if parented correctly
            self.batch_manager = None # Allow new batch requests

    def open_chart_window(self, token_data: dict):
        chart_window = ChartWidget(api_service=self.api_service)
        
        token_name = token_data.get('name', 'N/A')
        token_symbol = token_data.get('symbol', 'N/A')
        token_source = token_data.get('source', 'unknown')
        
        source_description = "未知来源"
        if token_source == 'historical':
            source_description = "自定义API"
        elif token_source == 'trend':
            source_description = "Birdeye"
            
        chart_window.setWindowTitle(f"K线图 - {token_name} ({token_symbol}) (来源: {source_description})")
        chart_window.resize(1000, 700)

        context_list = []
        current_index = -1
        active_tab_widget = self.central_widget.currentWidget()

        if active_tab_widget == self.trend_tab and hasattr(self.trend_widget, 'tokens_data') and self.trend_widget.tokens_data:
            context_list = self.trend_widget.tokens_data
            key_to_compare = 'tokenAddress' # TrendWidget uses tokenAddress in its raw data
            current_token_addr = token_data.get('tokenAddress')
            if not current_token_addr:
                 current_token_addr = token_data.get('address') # Fallback if tokenAddress is missing in selected token_data

            for idx, t in enumerate(context_list):
                if t.get(key_to_compare) == current_token_addr:
                    current_index = idx
                    break
        elif active_tab_widget == self.historical_tokens_tab and hasattr(self.historical_tokens_widget, 'historical_tokens_data') and self.historical_tokens_widget.historical_tokens_data:
            context_list = self.historical_tokens_widget.historical_tokens_data
            key_to_compare = 'address' # HistoricalTokensWidget uses address
            current_token_addr = token_data.get('address')

            for idx, t in enumerate(context_list):
                if t.get(key_to_compare) == current_token_addr:
                    current_index = idx
                    break
        
        if context_list and current_index != -1:
            # 为context_list中的每个token确保有source字段，以便ChartWidget.set_token可以正确工作
            # 这个source应该与打开ChartWidget时这个列表的来源一致
            list_source = 'trend' if active_tab_widget == self.trend_tab else 'historical'
            processed_context_list = []
            for t_data in context_list:
                new_t_data = t_data.copy() # 操作副本
                if 'source' not in new_t_data:
                    new_t_data['source'] = list_source
                # 确保地址字段统一，方便ChartWidget内部的比较
                if list_source == 'trend' and 'tokenAddress' in new_t_data and 'address' not in new_t_data:
                    new_t_data['address'] = new_t_data['tokenAddress']
                elif list_source == 'historical' and 'address' in new_t_data and 'tokenAddress' not in new_t_data:
                     new_t_data['tokenAddress'] = new_t_data['address']
                processed_context_list.append(new_t_data)
            
            chart_window.set_context_data(processed_context_list, current_index)
            logger.info(f"Chart context set: {len(processed_context_list)} tokens, current index: {current_index}, source type: {list_source}")
        else:
            logger.warning(f"Could not determine context list or index for chart navigation. Token: {token_data.get(key_to_compare)}")

        chart_window.set_token(token_data) 
        chart_window.show()
        self.chart_windows.append(chart_window)

    def open_backtest_window(self, token: dict):
        backtest_window = BacktestWidget(api_service=self.api_service) 
        backtest_window.setWindowTitle(f"策略回测 - {token['name']} ({token['symbol']})")
        backtest_window.resize(1200, 800)
        backtest_window.set_token(token)
        backtest_window.show()
        self.backtest_windows.append(backtest_window)

def main():
    # 设置多进程启动方法（对于macOS很重要）
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 如果已经设置过了就忽略
    
    app = QApplication(sys.argv)
    
    # 设置应用程序退出时的行为
    app.setQuitOnLastWindowClosed(True)
    
    # 添加信号处理
    import signal
    
    def signal_handler(signum, frame):
        """处理系统信号，确保程序能够正常退出"""
        logger.info(f"接收到信号 {signum}，正在退出程序...")
        
        # 强制清理所有子进程
        try:
            import psutil
            import os
            current_pid = os.getpid()
            current_process = psutil.Process(current_pid)
            
            for child in current_process.children(recursive=True):
                try:
                    child.terminate()
                except Exception:
                    pass
            
            # 等待一小段时间
            import time
            time.sleep(0.2)
            
            # 强制杀死顽固的子进程
            for child in current_process.children(recursive=True):
                try:
                    if child.is_running():
                        child.kill()
                except Exception:
                    pass
                    
        except ImportError:
            logger.info("signal_handler: psutil 未安装，使用基础清理")
            # 基础清理：等待一下
            try:
                import time
                time.sleep(0.5)
            except Exception:
                pass
        except Exception:
            pass
        
        # 退出应用程序
        app.quit()
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
    
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    exit_code = app.exec_()
    
    # 应用程序退出后的额外清理
    logger.info("应用程序正在退出，执行最终清理...")
    
    try:
        # 确保所有子进程都被清理
        import psutil
        import os
        current_pid = os.getpid()
        current_process = psutil.Process(current_pid)
        
        for child in current_process.children(recursive=True):
            try:
                if child.is_running():
                    child.kill()
                    logger.info(f"最终清理：杀死子进程 {child.pid}")
            except Exception:
                pass
                
    except ImportError:
        logger.info("最终清理: psutil 未安装，跳过进程检查")
        # 基础清理：等待一下确保进程有时间退出
        try:
            import time
            time.sleep(0.3)
        except Exception:
            pass
    except Exception as e:
        logger.error(f"最终清理时出错: {e}")
    
    logger.info("程序退出完成")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()