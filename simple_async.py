#!/usr/bin/env python
"""
简化版异步工作线程模块 - 使用标准Python线程库
"""

import threading
import time
import queue
from typing import Callable, Any, Dict, List, Optional

# 定义日志函数
def log_info(message):
    print(f"INFO: {message}")
    
def log_error(message):
    print(f"ERROR: {message}")

class SimpleAsyncTask:
    """简化版异步任务类"""
    
    @staticmethod
    def run(func, on_result=None, on_error=None, *args, **kwargs):
        """
        运行异步任务
        
        参数:
            func (Callable): 要执行的函数
            on_result (Callable): 结果回调函数
            on_error (Callable): 错误回调函数
            *args, **kwargs: 传递给函数的参数
            
        返回:
            int: 任务ID
        """
        # 创建任务ID
        task_id = id(func)
        
        # 定义线程函数
        def thread_func():
            try:
                log_info(f"任务 {task_id} 开始执行...")
                # 执行函数
                result = func(*args, **kwargs)
                log_info(f"任务 {task_id} 执行完成")
                
                # 调用结果回调函数
                if on_result:
                    log_info(f"任务 {task_id} 调用结果回调函数")
                    on_result(result)
                else:
                    log_info(f"任务 {task_id} 没有提供结果回调函数")
            except Exception as e:
                log_error(f"任务 {task_id} 执行出错: {str(e)}")
                
                # 调用错误回调函数
                if on_error:
                    log_info(f"任务 {task_id} 调用错误回调函数")
                    on_error(str(e))
                else:
                    log_info(f"任务 {task_id} 没有提供错误回调函数")
        
        # 创建并启动线程
        thread = threading.Thread(target=thread_func)
        thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
        
        log_info(f"启动任务 {task_id}...")
        thread.start()
        
        return task_id
