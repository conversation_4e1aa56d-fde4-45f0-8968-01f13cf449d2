"""
策略信号客户端 - 基于QLocalSocket
用于trending_window和holdings_panel向signal_monitor发送策略信号
"""
import logging
import json
import time
from typing import Dict, Optional
from dataclasses import dataclass, asdict

from PyQt5.QtCore import QObject, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtNetwork import QLocalSocket, QAbstractSocket

logger = logging.getLogger(__name__)


@dataclass
class SignalData:
    """信号数据结构"""
    token_address: str
    symbol: str
    signal_type: str  # buy, sell, hold, wait
    price: float
    timestamp: int
    strategy_name: str
    source: str  # trending, holdings
    confidence: float = 0.0
    metadata: Dict = None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        if data['metadata'] is None:
            data['metadata'] = {}
        return data


class SignalClient(QObject):
    """策略信号客户端"""
    
    # 信号定义
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    connection_error = pyqtSignal(str)
    signal_sent = pyqtSignal(str, str)  # symbol, signal_type
    
    def __init__(self, source_name: str, parent=None):
        super().__init__(parent)
        self.source_name = source_name  # trending 或 holdings
        self.socket = QLocalSocket(self)
        self.server_name = "trading_signal_monitor"
        
        # 连接状态
        self.is_connected = False
        self.pending_signals = []  # 待发送信号队列
        self.max_pending_signals = 100
        
        # 重连机制
        self.reconnect_timer = QTimer(self)
        self.reconnect_timer.setSingleShot(True)
        self.reconnect_timer.timeout.connect(self.attempt_connection)
        self.reconnect_interval = 5000  # 5秒重连间隔
        
        # 信号发送限制
        self.last_signal_time = {}  # token_address -> timestamp
        self.min_signal_interval = 5  # 同一代币最小信号间隔（秒）
        
        # 连接socket信号
        self.socket.connected.connect(self.on_connected)
        self.socket.disconnected.connect(self.on_disconnected)
        self.socket.errorOccurred.connect(self.on_error_occurred)
        
        logger.info(f"SignalClient: 信号客户端初始化完成 (来源: {source_name})")
    
    def start_connection(self):
        """开始连接到监控服务器"""
        self.attempt_connection()
    
    def attempt_connection(self):
        """尝试连接到服务器"""
        try:
            if self.socket.state() == QAbstractSocket.ConnectedState:
                logger.debug(f"SignalClient[{self.source_name}]: 已经连接到服务器")
                return
            
            if self.socket.state() == QAbstractSocket.ConnectingState:
                logger.debug(f"SignalClient[{self.source_name}]: 正在连接中...")
                return
            
            logger.info(f"SignalClient[{self.source_name}]: 尝试连接到服务器...")
            self.socket.connectToServer(self.server_name)
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 连接尝试异常: {e}")
            self.schedule_reconnect()
    
    def schedule_reconnect(self):
        """安排重新连接"""
        if not self.reconnect_timer.isActive():
            logger.debug(f"SignalClient[{self.source_name}]: 将在{self.reconnect_interval/1000}秒后重新连接")
            self.reconnect_timer.start(self.reconnect_interval)
    
    @pyqtSlot()
    def on_connected(self):
        """连接成功回调"""
        self.is_connected = True
        self.reconnect_timer.stop()
        
        logger.info(f"SignalClient[{self.source_name}]: 成功连接到监控服务器")
        self.connected.emit()
        
        # 发送待发送的信号
        self.send_pending_signals()
    
    @pyqtSlot()
    def on_disconnected(self):
        """断开连接回调"""
        self.is_connected = False
        logger.warning(f"SignalClient[{self.source_name}]: 与监控服务器断开连接")
        self.disconnected.emit()
        
        # 安排重新连接
        self.schedule_reconnect()
    
    @pyqtSlot(QLocalSocket.LocalSocketError)
    def on_error_occurred(self, error):
        """连接错误回调"""
        error_string = self.socket.errorString()
        logger.error(f"SignalClient[{self.source_name}]: 连接错误 - {error}: {error_string}")
        self.connection_error.emit(error_string)
        
        # 如果是连接拒绝错误，说明服务器可能没启动
        if error == QAbstractSocket.ConnectionRefusedError:
            logger.warning(f"SignalClient[{self.source_name}]: 服务器拒绝连接，可能服务器未启动")
        
        # 安排重新连接
        self.schedule_reconnect()
    
    def send_signal(self, signal_data: SignalData) -> bool:
        """发送策略信号"""
        try:
            # 验证信号数据
            if not self.validate_signal(signal_data):
                return False
            
            # 检查发送频率限制
            if not self.check_signal_rate_limit(signal_data):
                logger.debug(f"SignalClient[{self.source_name}]: 信号发送过于频繁，跳过 - {signal_data.symbol}")
                return False
            
            # 设置来源
            signal_data.source = self.source_name
            
            # 如果已连接，直接发送
            if self.is_connected and self.socket.state() == QAbstractSocket.ConnectedState:
                return self.send_signal_immediately(signal_data)
            else:
                # 如果未连接，加入待发送队列
                self.add_to_pending_queue(signal_data)
                
                # 尝试连接
                if not self.reconnect_timer.isActive():
                    self.attempt_connection()
                
                return True
                
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 发送信号失败: {e}")
            return False
    
    def validate_signal(self, signal_data: SignalData) -> bool:
        """验证信号数据"""
        try:
            if not signal_data.token_address:
                logger.warning(f"SignalClient[{self.source_name}]: 代币地址为空")
                return False
            
            if not signal_data.symbol:
                logger.warning(f"SignalClient[{self.source_name}]: 代币符号为空")
                return False
            
            if signal_data.signal_type not in ['buy', 'sell', 'hold', 'wait']:
                logger.warning(f"SignalClient[{self.source_name}]: 无效的信号类型: {signal_data.signal_type}")
                return False
            
            if signal_data.price <= 0:
                logger.warning(f"SignalClient[{self.source_name}]: 无效的价格: {signal_data.price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 信号验证失败: {e}")
            return False
    
    def check_signal_rate_limit(self, signal_data: SignalData) -> bool:
        """检查信号发送频率限制"""
        try:
            # 🔥 修复：使用当前时间而不是信号的历史时间戳进行频率限制
            # 这样可以避免历史时间戳相近的信号被错误过滤
            current_time = int(time.time())  # 当前时间
            last_time = self.last_signal_time.get(signal_data.token_address, 0)

            if current_time - last_time < self.min_signal_interval:
                logger.debug(f"SignalClient[{self.source_name}]: 信号被频率限制过滤 - {signal_data.symbol} (间隔: {current_time - last_time}秒)")
                return False

            self.last_signal_time[signal_data.token_address] = current_time
            logger.debug(f"SignalClient[{self.source_name}]: 信号通过频率限制检查 - {signal_data.symbol}")
            return True

        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 检查发送频率失败: {e}")
            return True  # 出错时允许发送
    
    def send_signal_immediately(self, signal_data: SignalData) -> bool:
        """立即发送信号"""
        try:
            # 转换为JSON
            message = json.dumps(signal_data.to_dict(), ensure_ascii=False)
            # 🔥 修复：添加换行符作为消息分隔符，避免多个JSON消息连在一起
            message_with_separator = message + '\n'
            message_bytes = message_with_separator.encode('utf-8')

            # 发送数据
            bytes_written = self.socket.write(message_bytes)

            if bytes_written > 0:
                logger.info(f"SignalClient[{self.source_name}]: 发送信号 - {signal_data.symbol} {signal_data.signal_type}")
                self.signal_sent.emit(signal_data.symbol, signal_data.signal_type)
                return True
            else:
                logger.error(f"SignalClient[{self.source_name}]: 发送信号失败 - 写入字节数为0")
                return False

        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 立即发送信号失败: {e}")
            return False
    
    def add_to_pending_queue(self, signal_data: SignalData):
        """添加到待发送队列"""
        try:
            # 限制队列大小
            if len(self.pending_signals) >= self.max_pending_signals:
                # 移除最旧的信号
                self.pending_signals.pop(0)
                logger.debug(f"SignalClient[{self.source_name}]: 待发送队列已满，移除最旧信号")
            
            self.pending_signals.append(signal_data)
            logger.debug(f"SignalClient[{self.source_name}]: 信号加入待发送队列 - {signal_data.symbol} (队列: {len(self.pending_signals)})")
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 添加到待发送队列失败: {e}")
    
    def send_pending_signals(self):
        """发送待发送的信号"""
        try:
            if not self.pending_signals:
                return
            
            logger.info(f"SignalClient[{self.source_name}]: 发送待发送信号 ({len(self.pending_signals)} 个)")
            
            # 按时间排序，发送最新的
            sorted_signals = sorted(self.pending_signals, key=lambda x: x.timestamp, reverse=True)
            
            # 按代币分组，每个代币只发送最新信号
            latest_signals = {}
            for signal in sorted_signals:
                if signal.token_address not in latest_signals:
                    latest_signals[signal.token_address] = signal
            
            # 发送最新信号
            sent_count = 0
            for signal in latest_signals.values():
                if self.send_signal_immediately(signal):
                    sent_count += 1
                else:
                    break  # 如果发送失败，停止发送其他信号
            
            # 清空待发送队列
            self.pending_signals.clear()
            
            logger.info(f"SignalClient[{self.source_name}]: 成功发送 {sent_count} 个待发送信号")
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 发送待发送信号失败: {e}")
    
    def disconnect_from_server(self):
        """断开与服务器的连接"""
        try:
            self.reconnect_timer.stop()
            
            if self.socket.state() == QAbstractSocket.ConnectedState:
                self.socket.disconnectFromServer()
                # 等待断开连接
                if not self.socket.waitForDisconnected(3000):
                    logger.warning(f"SignalClient[{self.source_name}]: 断开连接超时")
            
            logger.info(f"SignalClient[{self.source_name}]: 已断开与服务器的连接")
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 断开连接失败: {e}")
    
    def get_connection_status(self) -> Dict:
        """获取连接状态信息"""
        try:
            return {
                'is_connected': self.is_connected,
                'socket_state': self.socket.state(),
                'pending_signals_count': len(self.pending_signals),
                'source_name': self.source_name,
                'server_name': self.server_name
            }
            
        except Exception as e:
            logger.error(f"SignalClient[{self.source_name}]: 获取连接状态失败: {e}")
            return {}


# 便捷函数
def create_signal_from_strategy_signal(token_address: str, symbol: str, signal_type: str, 
                                     price: float, timestamp: int, strategy_name: str, 
                                     confidence: float = 0.0, metadata: Dict = None, source: str = "") -> SignalData:
    """从策略信号参数创建SignalData对象"""
    return SignalData(
        token_address=token_address,
        symbol=symbol,
        signal_type=signal_type,
        price=price,
        timestamp=timestamp,
        strategy_name=strategy_name,
        source=source if source else "unknown",  # 使用传入的source，如果为空则默认为unknown
        confidence=confidence,
        metadata=metadata or {}
    ) 