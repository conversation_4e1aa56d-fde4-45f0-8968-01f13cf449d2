#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度性能分析工具 - 使用 cProfile 和 profile 模块分析性能瓶颈
"""

import sys
import os
import cProfile
import pstats
import profile
import io
import time
import threading
from contextlib import contextmanager
from typing import Dict, Any, Optional
import argparse

class DetailedPerformanceProfiler:
    """详细的性能分析器"""
    
    def __init__(self):
        self.profiler = None
        self.profile_data = {}
        self.start_time = None
        
    @contextmanager
    def profile_context(self, sort_by: str = 'cumulative'):
        """性能分析上下文管理器"""
        print("🎯 开始性能分析...")
        self.start_time = time.time()
        
        # 使用 cProfile
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            yield profiler
        finally:
            profiler.disable()
            end_time = time.time()
            
            print(f"⏱️ 分析完成，耗时: {end_time - self.start_time:.2f}秒")
            self._analyze_results(profiler, sort_by)
    
    def _analyze_results(self, profiler: cProfile.Profile, sort_by: str):
        """分析性能结果"""
        
        # 创建字符串缓冲区来捕获输出
        s = io.StringIO()
        stats = pstats.Stats(profiler, stream=s)
        
        print("\n" + "="*80)
        print("📊 详细性能分析报告")
        print("="*80)
        
        # 1. 总体统计
        stats.print_stats(0)  # 不打印详细信息，只要总计
        total_stats = s.getvalue()
        print("📈 总体统计:")
        for line in total_stats.split('\n')[:10]:  # 只显示前10行
            if line.strip():
                print(f"   {line}")
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 2. 按累积时间排序的前20个函数
        print(f"\n🔥 CPU时间占用最高的前20个函数 (按{sort_by}排序):")
        print("-" * 80)
        stats.sort_stats(sort_by)
        stats.print_stats(20)
        slow_functions = s.getvalue()
        print(slow_functions)
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 3. 按调用次数排序
        print(f"\n🔄 调用次数最多的前20个函数:")
        print("-" * 80)
        stats.sort_stats('calls')
        stats.print_stats(20)
        frequent_calls = s.getvalue()
        print(frequent_calls)
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 4. 按单次调用平均时间排序
        print(f"\n⏱️ 单次调用耗时最长的前20个函数:")
        print("-" * 80)
        stats.sort_stats('tottime')
        stats.print_stats(20)
        slow_single_calls = s.getvalue()
        print(slow_single_calls)
        
        # 5. 特别关注PyQt5相关函数
        print(f"\n🖥️ PyQt5相关函数性能:")
        print("-" * 80)
        stats.sort_stats('cumulative')
        stats.print_stats('PyQt5', 15)
        pyqt_stats = s.getvalue()
        if pyqt_stats.strip():
            print(pyqt_stats)
        else:
            print("   没有发现PyQt5相关的性能瓶颈")
        
        # 6. 特别关注定时器相关函数
        print(f"\n⏰ 定时器相关函数性能:")
        print("-" * 80)
        stats.print_stats('timer', 15)
        timer_stats = s.getvalue()
        if timer_stats.strip():
            print(timer_stats)
        else:
            print("   没有发现定时器相关的性能瓶颈")
        
        # 保存详细报告到文件
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"performance_report_{timestamp}.prof"
        profiler.dump_stats(filename)
        print(f"\n💾 详细性能数据已保存到: {filename}")
        print("   可使用以下命令查看:")
        print(f"   python -m pstats {filename}")
    
    def profile_trading_app_startup(self):
        """分析交易应用启动性能"""
        print("🚀 分析交易应用启动性能...")
        
        with self.profile_context('cumulative'):
            try:
                # 模拟应用启动过程
                print("📱 正在启动应用...")
                
                # 导入主要模块
                from PyQt5.QtWidgets import QApplication
                from ui.live_trading_widget import LiveTradingWidget
                
                # 创建应用
                app = QApplication(sys.argv)
                
                # 创建主窗口
                print("🏠 创建主窗口...")
                widget = LiveTradingWidget()
                
                # 初始化UI
                print("🎨 初始化UI...")
                widget.init_ui()
                
                # 显示窗口
                widget.show()
                
                print("✅ 启动完成")
                
                # 清理资源
                widget.close()
                app.quit()
                
            except Exception as e:
                print(f"❌ 启动分析失败: {e}")
                
    def profile_function_execution(self, func, *args, **kwargs):
        """分析特定函数的执行性能"""
        print(f"🎯 分析函数: {func.__name__}")
        
        with self.profile_context():
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                print(f"❌ 函数执行失败: {e}")
                return None

    def profile_with_line_profiler(self, script_path: str):
        """使用line_profiler进行行级性能分析"""
        try:
            import line_profiler
            print("📏 使用line_profiler进行行级分析...")
            
            # 这需要手动装饰要分析的函数
            print("💡 提示: 请在要分析的函数前添加 @profile 装饰器")
            print(f"   然后运行: kernprof -l -v {script_path}")
            
        except ImportError:
            print("❌ line_profiler未安装")
            print("   安装命令: pip install line_profiler")

    def memory_profiler_analysis(self, script_path: str):
        """使用memory_profiler进行内存分析"""
        try:
            import memory_profiler
            print("💾 使用memory_profiler进行内存分析...")
            
            print("💡 提示: 请在要分析的函数前添加 @profile 装饰器")
            print(f"   然后运行: python -m memory_profiler {script_path}")
            
        except ImportError:
            print("❌ memory_profiler未安装")
            print("   安装命令: pip install memory-profiler")

def analyze_specific_module():
    """分析特定模块的性能"""
    
    profiler = DetailedPerformanceProfiler()
    
    print("🔍 选择要分析的模块:")
    print("1. 交易应用启动过程")
    print("2. LiveTradingWidget初始化")
    print("3. API服务性能")
    print("4. 趋势数据刷新")
    print("5. 图表加载")
    
    choice = input("请选择 (1-5): ")
    
    if choice == "1":
        profiler.profile_trading_app_startup()
        
    elif choice == "2":
        print("🔧 分析LiveTradingWidget初始化...")
        with profiler.profile_context():
            try:
                from ui.live_trading_widget import LiveTradingWidget
                from PyQt5.QtWidgets import QApplication
                
                app = QApplication(sys.argv)
                widget = LiveTradingWidget()
                widget.init_ui()
                widget.close()
                app.quit()
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")
                
    elif choice == "3":
        print("🌐 分析API服务性能...")
        with profiler.profile_context():
            try:
                from api_service import APIService
                api = APIService()
                # 模拟API调用
                print("   正在测试API调用...")
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")
                
    elif choice == "4":
        print("📊 分析趋势数据刷新...")
        with profiler.profile_context():
            try:
                # 模拟趋势数据刷新
                print("   正在刷新趋势数据...")
                time.sleep(0.1)  # 模拟网络请求
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")
                
    elif choice == "5":
        print("📈 分析图表加载...")
        with profiler.profile_context():
            try:
                # 模拟图表加载
                print("   正在加载图表...")
                time.sleep(0.1)  # 模拟图表绘制
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")
    else:
        print("❌ 无效选择")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="交易应用性能分析工具")
    parser.add_argument('--module', choices=['startup', 'widget', 'api', 'trend', 'chart'], 
                       help='要分析的模块')
    parser.add_argument('--sort', default='cumulative', 
                       choices=['cumulative', 'time', 'calls'],
                       help='排序方式')
    parser.add_argument('--interactive', action='store_true', 
                       help='交互式选择模块')
    
    args = parser.parse_args()
    
    print("🔬 交易应用深度性能分析工具")
    print("=" * 60)
    
    if args.interactive or not args.module:
        analyze_specific_module()
    else:
        profiler = DetailedPerformanceProfiler()
        
        if args.module == 'startup':
            profiler.profile_trading_app_startup()
        # 其他模块的处理...
    
    print("\n✅ 性能分析完成!")
    print("\n💡 优化建议:")
    print("   1. 检查高CPU占用的函数")
    print("   2. 减少高频调用的函数")
    print("   3. 优化耗时的单次调用")
    print("   4. 检查PyQt5相关的性能瓶颈")
    print("   5. 优化定时器使用")

if __name__ == "__main__":
    main() 