#!/usr/bin/env python3
"""
修复持仓数据刷新逻辑的脚本
避免在获取空数据时清空界面
"""

import re

def fix_holdings_refresh():
    # 读取文件
    with open('ui/live_trading_widget.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定位refresh_holdings_data方法中需要修改的部分
    # 查找: holdings_list = self.get_wallet_holdings_data() 开始到 else: 结束的代码段
    lines = content.split('\n')
    
    start_line = -1
    end_line = -1
    
    for i, line in enumerate(lines):
        if 'holdings_list = self.get_wallet_holdings_data()' in line:
            start_line = i
        if start_line >= 0 and 'self.add_message_to_log(f"持仓数据已刷新，当前无持仓记录或无法获取数据。", "info")' in line:
            end_line = i
            break
    
    if start_line >= 0 and end_line >= 0:
        # 构建新的代码段
        new_lines = [
            '            holdings_list = self.get_wallet_holdings_data() ',
            '',
            '            # 🔥 修复：只有在获取到有效数据时才更新界面',
            '            if holdings_list:',
            '                resize_holdings_list = holdings_list[:50]',
            '                self.holdings_list_normalized = normalize_holdings_for_background_charts(resize_holdings_list)',
            '                ',
            '                # 更新界面显示',
            '                self.holdings_panel.update_holdings(holdings_list)',
            '                self.add_message_to_log(f"持仓数据刷新完成，共 {len(holdings_list)} 个代币。", "success")',
            '            else:',
            '                # 🔥 修复：没有数据时不清空界面，保留当前显示',
            '                self.add_message_to_log("获取持仓数据为空，保留当前界面显示。请检查钱包连接状态或稍后重试。", "warning")',
            '                # 不调用 self.holdings_panel.update_holdings([])，避免清空界面'
        ]
        
        # 替换旧代码
        new_content_lines = lines[:start_line] + new_lines + lines[end_line+1:]
        new_content = '\n'.join(new_content_lines)
        
        # 写回文件
        with open('ui/live_trading_widget.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f'✅ 成功修复持仓刷新逻辑 (行 {start_line+1}-{end_line+1})')
        print('🔥 修改内容：')
        print('  - 只有在获取到有效数据时才调用 update_holdings()')
        print('  - 数据为空时保留当前界面显示，不清空')
        return True
    else:
        print('❌ 未找到需要修改的代码段')
        return False

if __name__ == '__main__':
    fix_holdings_refresh() 