#!/usr/bin/env python3
"""
自动化打包脚本 - 将趋势交易应用打包成可执行文件
支持 Windows、macOS 和 Linux
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

def check_dependencies():
    """检查打包所需的依赖"""
    print("🔍 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller 安装完成")
    
    # 检查其他必要依赖
    required_packages = [
        "PyQt5", "requests", "pandas", "numpy", 
        "pyqtgraph", "ta", "python-dotenv", "matplotlib"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"正在安装缺失的包: {', '.join(missing_packages)}")
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + missing_packages)
        print("✅ 所有依赖安装完成")

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    print("📝 创建 PyInstaller 规格文件...")
    
    current_os = platform.system().lower()
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 收集策略模块
strategy_modules = collect_submodules('strategies')

# 收集UI模块
ui_modules = collect_submodules('ui')

# 收集所有子模块
hiddenimports = strategy_modules + ui_modules + [
    'PyQt5.QtCore',
    'PyQt5.QtWidgets', 
    'PyQt5.QtGui',
    'PyQt5.sip',
    'pandas',
    'numpy',
    'requests',
    'ta',
    'matplotlib',
    'pyqtgraph',
    'sqlite3',
    'json',
    'time',
    'datetime',
    'logging',
    'multiprocessing',
    'concurrent.futures',
    'typing',
]

# 排除不需要的模块以减小体积
excludes = [
    'tkinter',
    'unittest',
    'test',
    'distutils',
    'setuptools',
    'pip',
]

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TrendTrader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设为False隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{"icon_path"}',  # 如果有图标文件的话
)

# macOS 特定配置
{"macos_bundle_config" if current_os == "darwin" else "# Windows/Linux 不需要 app bundle"}
'''

    # 根据操作系统调整配置
    if current_os == "darwin":
        icon_path = "icon.icns"  # macOS 图标
        macos_bundle_config = '''
app = BUNDLE(
    exe,
    name='TrendTrader.app',
    icon='icon.icns',
    bundle_identifier='com.trendtrader.app',
    info_plist={
        'NSHighResolutionCapable': 'True',
        'NSRequiresAquaSystemAppearance': 'False',
    },
)'''
    elif current_os == "windows":
        icon_path = "icon.ico"  # Windows 图标
        macos_bundle_config = "# Windows 不需要 app bundle"
    else:
        icon_path = "icon.png"  # Linux 图标
        macos_bundle_config = "# Linux 不需要 app bundle"
    
    # 替换模板中的占位符
    spec_content = spec_content.format(
        icon_path=icon_path,
        macos_bundle_config=macos_bundle_config
    )
    
    with open("TrendTrader.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 规格文件创建完成: TrendTrader.spec")

def create_icon():
    """创建应用图标（如果不存在）"""
    print("🎨 检查应用图标...")
    
    current_os = platform.system().lower()
    
    if current_os == "darwin":
        icon_file = "icon.icns"
    elif current_os == "windows":
        icon_file = "icon.ico"
    else:
        icon_file = "icon.png"
    
    if not os.path.exists(icon_file):
        print(f"⚠️  图标文件 {icon_file} 不存在，将使用默认图标")
        # 这里可以添加创建默认图标的代码
        return False
    else:
        print(f"✅ 找到图标文件: {icon_file}")
        return True

def clean_build_dirs():
    """清理之前的构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已清理: {dir_name}")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 使用 PyInstaller 构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",  # 清理缓存
            "TrendTrader.spec"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ 构建成功！")
        print("构建输出:")
        print(result.stdout)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print("错误输出:")
        print(e.stderr)
        return False

def create_installer_script():
    """创建安装脚本"""
    print("📦 创建安装脚本...")
    
    current_os = platform.system().lower()
    
    if current_os == "windows":
        # Windows 批处理安装脚本
        installer_content = '''@echo off
echo 正在安装 TrendTrader...
echo.

REM 创建程序目录
if not exist "C:\\Program Files\\TrendTrader" mkdir "C:\\Program Files\\TrendTrader"

REM 复制文件
xcopy /E /I /Y "dist\\TrendTrader.exe" "C:\\Program Files\\TrendTrader\\"

REM 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\TrendTrader.lnk'); $Shortcut.TargetPath = 'C:\\Program Files\\TrendTrader\\TrendTrader.exe'; $Shortcut.Save()"

echo.
echo 安装完成！
echo 您可以在桌面找到 TrendTrader 快捷方式
pause
'''
        with open("install.bat", "w", encoding="utf-8") as f:
            f.write(installer_content)
        print("✅ Windows 安装脚本创建完成: install.bat")
        
    elif current_os == "darwin":
        # macOS 安装脚本
        installer_content = '''#!/bin/bash
echo "正在安装 TrendTrader..."

# 复制应用到 Applications 目录
if [ -d "dist/TrendTrader.app" ]; then
    echo "复制应用到 /Applications..."
    cp -R "dist/TrendTrader.app" "/Applications/"
    echo "安装完成！"
    echo "您可以在 Launchpad 或 Applications 文件夹中找到 TrendTrader"
else
    echo "错误：找不到 TrendTrader.app"
    exit 1
fi
'''
        with open("install.sh", "w", encoding="utf-8") as f:
            f.write(installer_content)
        os.chmod("install.sh", 0o755)
        print("✅ macOS 安装脚本创建完成: install.sh")
        
    else:
        # Linux 安装脚本
        installer_content = '''#!/bin/bash
echo "正在安装 TrendTrader..."

# 创建程序目录
sudo mkdir -p /opt/trendtrader

# 复制文件
sudo cp -R dist/TrendTrader /opt/trendtrader/

# 创建符号链接
sudo ln -sf /opt/trendtrader/TrendTrader /usr/local/bin/trendtrader

# 创建桌面文件
cat > ~/.local/share/applications/trendtrader.desktop << EOF
[Desktop Entry]
Name=TrendTrader
Comment=趋势交易应用
Exec=/opt/trendtrader/TrendTrader
Icon=/opt/trendtrader/icon.png
Terminal=false
Type=Application
Categories=Office;Finance;
EOF

echo "安装完成！"
echo "您可以在应用菜单中找到 TrendTrader，或在终端中运行 'trendtrader'"
'''
        with open("install.sh", "w", encoding="utf-8") as f:
            f.write(installer_content)
        os.chmod("install.sh", 0o755)
        print("✅ Linux 安装脚本创建完成: install.sh")

def create_readme():
    """创建分发说明文档"""
    print("📖 创建分发说明文档...")
    
    current_os = platform.system().lower()
    
    readme_content = f'''# TrendTrader 分发包

## 系统要求

- 操作系统: {platform.system()} {platform.release()}
- 架构: {platform.machine()}

## 安装说明

### 方法一：使用安装脚本（推荐）

{"Windows:" if current_os == "windows" else "macOS/Linux:"}
{"1. 右键点击 install.bat，选择 '以管理员身份运行'" if current_os == "windows" else "1. 在终端中运行: ./install.sh"}
2. 按照提示完成安装

### 方法二：手动安装

1. 将 dist 目录中的可执行文件复制到您希望的位置
2. 创建快捷方式（可选）

## 使用说明

1. 启动应用程序
2. 在趋势榜单中查看热门代币
3. 选择交易策略进行回测
4. 连接钱包进行实盘交易（需要配置 API）

## 配置说明

首次运行时，应用会创建配置文件。您可以在配置中设置：

- API 密钥
- 钱包地址
- 交易参数
- 风险设置

## 故障排除

### 常见问题

1. **应用无法启动**
   - 确保您的系统满足最低要求
   - 检查防病毒软件是否阻止了应用

2. **网络连接问题**
   - 检查网络连接
   - 确认防火墙设置

3. **API 错误**
   - 验证 API 密钥是否正确
   - 检查 API 配额是否充足

### 获取帮助

如果遇到问题，请：
1. 查看应用日志文件
2. 联系技术支持

## 版本信息

- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python 版本: {sys.version}
- 平台: {platform.platform()}

## 免责声明

本软件仅供学习和研究使用。使用本软件进行交易的所有风险由用户自行承担。
'''

    with open("README_DISTRIBUTION.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ 分发说明文档创建完成: README_DISTRIBUTION.md")

def create_distribution_package():
    """创建最终的分发包"""
    print("📦 创建分发包...")
    
    current_os = platform.system().lower()
    
    # 创建分发目录
    dist_dir = f"TrendTrader_Distribution_{current_os}"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)
    
    # 复制可执行文件
    if current_os == "darwin" and os.path.exists("dist/TrendTrader.app"):
        shutil.copytree("dist/TrendTrader.app", f"{dist_dir}/TrendTrader.app")
    elif os.path.exists("dist/TrendTrader.exe"):
        shutil.copy2("dist/TrendTrader.exe", dist_dir)
    elif os.path.exists("dist/TrendTrader"):
        shutil.copy2("dist/TrendTrader", dist_dir)
    
    # 复制安装脚本
    install_script = "install.bat" if current_os == "windows" else "install.sh"
    if os.path.exists(install_script):
        shutil.copy2(install_script, dist_dir)
    
    # 复制说明文档
    if os.path.exists("README_DISTRIBUTION.md"):
        shutil.copy2("README_DISTRIBUTION.md", dist_dir)
    
    # 复制图标文件（如果存在）
    icon_files = ["icon.ico", "icon.icns", "icon.png"]
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            shutil.copy2(icon_file, dist_dir)
    
    print(f"✅ 分发包创建完成: {dist_dir}")
    
    # 创建压缩包
    try:
        import zipfile
        zip_name = f"{dist_dir}.zip"
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(dist_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, dist_dir)
                    zipf.write(file_path, arcname)
        print(f"✅ 压缩包创建完成: {zip_name}")
    except Exception as e:
        print(f"⚠️  创建压缩包失败: {e}")

def main():
    """主函数"""
    print("🚀 TrendTrader 自动化打包工具")
    print("=" * 50)
    
    try:
        # 1. 检查依赖
        check_dependencies()
        
        # 2. 清理构建目录
        clean_build_dirs()
        
        # 3. 创建图标
        create_icon()
        
        # 4. 创建规格文件
        create_spec_file()
        
        # 5. 构建可执行文件
        if not build_executable():
            print("❌ 构建失败，退出")
            return 1
        
        # 6. 创建安装脚本
        create_installer_script()
        
        # 7. 创建说明文档
        create_readme()
        
        # 8. 创建分发包
        create_distribution_package()
        
        print("\n🎉 打包完成！")
        print("=" * 50)
        print("分发文件位置:")
        print(f"- 可执行文件: dist/")
        print(f"- 分发包: TrendTrader_Distribution_{platform.system().lower()}/")
        print(f"- 压缩包: TrendTrader_Distribution_{platform.system().lower()}.zip")
        
        return 0
        
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import datetime
    sys.exit(main()) 