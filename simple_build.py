#!/usr/bin/env python3
"""
简化的macOS打包脚本
"""

import os
import sys
import subprocess
import platform

def main():
    print("🚀 TrendTrader macOS 打包工具")
    print("=" * 40)
    
    # 清理之前的构建
    print("🧹 清理之前的构建...")
    os.system("rm -rf dist build *.spec")
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",  # 使用目录模式
        "--windowed",  # 创建 .app bundle
        "--name=TrendTrader",
        "--add-data=ui:ui",
        "--add-data=strategies:strategies", 
        "--add-data=config.py:.",
        "--add-data=indicators.py:.",
        "--add-data=api_service.py:.",
        "--add-data=simple_api_service.py:.",
        "--add-data=database_service.py:.",
        "--add-data=okx_dex_client.py:.",
        "--hidden-import=PyQt5.sip",
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtWidgets", 
        "--hidden-import=PyQt5.QtGui",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=requests",
        "--hidden-import=ta",
        "--hidden-import=matplotlib",
        "--hidden-import=pyqtgraph",
        "--hidden-import=sqlite3",
        "--hidden-import=multiprocessing",
        "--hidden-import=concurrent.futures",
        "--hidden-import=typing",
        "--hidden-import=datetime",
        "--hidden-import=logging",
        "--hidden-import=dotenv",
        "--hidden-import=pathlib",
        "--osx-bundle-identifier=com.trendtrader.app",
        "main.py"
    ]
    
    print("🔨 开始构建...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        print("📁 应用程序位置: dist/TrendTrader.app")
        
        # 测试启动
        print("\n🧪 测试应用启动...")
        test_cmd = ["open", "dist/TrendTrader.app"]
        subprocess.run(test_cmd, check=False)
        print("✅ 应用已启动，请检查是否正常运行")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 构建失败！")
        print("错误信息:")
        print(e.stderr)
        return False

if __name__ == "__main__":
    if platform.system() != "Darwin":
        print("❌ 此脚本仅适用于 macOS")
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1) 