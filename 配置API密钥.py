#!/usr/bin/env python3
"""
TrendTrader API密钥配置工具
"""

import os
import sys
from pathlib import Path

def main():
    print("🔑 TrendTrader API密钥配置工具")
    print("=" * 40)
    
    print("📋 此工具将帮助您配置Birdeye API密钥以解决401错误")
    print()
    
    # 检查当前是否已有API密钥
    current_key = os.environ.get("BIRDEYE_API_KEY", "")
    env_file = Path(".env")
    
    if env_file.exists():
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                if "BIRDEYE_API_KEY" in content:
                    print("✅ 发现现有的.env文件")
                    print("📄 当前配置:")
                    for line in content.split('\n'):
                        if line.strip() and not line.startswith('#'):
                            if "BIRDEYE_API_KEY" in line:
                                key_part = line.split('=')[1] if '=' in line else ""
                                masked_key = key_part[:8] + "*" * (len(key_part) - 8) if len(key_part) > 8 else "*" * len(key_part)
                                print(f"   BIRDEYE_API_KEY={masked_key}")
                    print()
        except Exception as e:
            print(f"⚠️  读取.env文件时出错: {e}")
    
    print("🌐 获取API密钥步骤:")
    print("1. 访问: https://docs.birdeye.so/")
    print("2. 点击 'Get API Key' 按钮")
    print("3. 注册免费账户")
    print("4. 复制您的API密钥")
    print()
    
    # 获取用户输入
    while True:
        api_key = input("🔑 请输入您的Birdeye API密钥 (或按Enter跳过): ").strip()
        
        if not api_key:
            print("⏭️  跳过配置，您可以稍后手动配置")
            break
            
        if len(api_key) < 10:
            print("❌ API密钥似乎太短，请检查是否完整复制")
            continue
            
        # 创建或更新.env文件
        try:
            env_content = []
            
            # 如果.env文件存在，读取现有内容
            if env_file.exists():
                with open(env_file, 'r') as f:
                    env_content = f.readlines()
            
            # 更新或添加API密钥
            key_found = False
            for i, line in enumerate(env_content):
                if line.strip().startswith('BIRDEYE_API_KEY'):
                    env_content[i] = f"BIRDEYE_API_KEY={api_key}\n"
                    key_found = True
                    break
            
            if not key_found:
                env_content.append(f"BIRDEYE_API_KEY={api_key}\n")
            
            # 写入文件
            with open(env_file, 'w') as f:
                f.writelines(env_content)
            
            print("✅ API密钥配置成功！")
            print(f"📁 配置文件位置: {env_file.absolute()}")
            print()
            break
            
        except Exception as e:
            print(f"❌ 配置失败: {e}")
            break
    
    print("📋 下一步:")
    print("1. 🔄 重新启动TrendTrader应用程序")
    print("2. 🎯 错误应该消失，图表数据正常显示")
    print("3. 🌐 确保网络连接正常")
    print()
    
    print("🆘 如果仍有问题:")
    print("- 检查API密钥是否正确")
    print("- 确认.env文件在正确位置")
    print("- 重启应用程序")
    print()
    
    # 验证配置
    if env_file.exists():
        print("🧪 验证配置...")
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                if "BIRDEYE_API_KEY" in content and len(content.split('=')[1].strip()) > 10:
                    print("✅ 配置验证通过")
                else:
                    print("⚠️  配置可能有问题，请检查.env文件")
        except:
            print("⚠️  无法验证配置")
    
    print("\n🎉 配置完成！请重启TrendTrader应用程序。")

if __name__ == "__main__":
    main() 