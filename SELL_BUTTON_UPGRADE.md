# 卖出按钮功能升级说明

## 概述
参考 `portfolio_widget.py` 中的卖出按钮实现，为 `live_trading_widget.py` 中的卖出按钮添加了卖出数量选项功能。

## 主要改进

### 1. 新增 SellConfirmDialog 类
- 从 `portfolio_widget.py` 移植了 `SellConfirmDialog` 确认对话框
- 支持显示详细的卖出信息：代币符号、余额、卖出比例、卖出数量、剩余数量
- 支持显示预估滑点信息
- 支持风险代币警告显示
- 支持SOL代币的特殊提醒（用作手续费）

### 2. 卖出按钮升级为下拉菜单
- 卖出按钮现在支持下拉菜单，提供多种卖出比例选项
- 默认提供：10%、25%、50%、75%、100% 五种卖出比例
- 添加了"🧠 智能卖出"选项，提供智能建议

### 3. 新增方法

#### `sell_token_percentage(percentage: int)`
- 按指定比例卖出代币
- 显示确认对话框，包含详细信息和风险提示
- 用户确认后执行实际卖出操作

#### `smart_sell_token()`
- 智能卖出功能，根据代币情况自动推荐最佳卖出策略
- 目前默认建议卖出50%（可根据实际需求扩展智能逻辑）
- 提供智能建议对话框，用户可选择采用建议或手动选择

### 4. 修改的方法

#### `sell_token()`
- 原来直接卖出所有代币，现在改为默认执行智能卖出
- 保持向后兼容性，点击按钮仍然可以卖出代币
- 通过下拉菜单提供更多选项

#### `update_sell_button_state()`
- 更新了工具提示文本，说明新的功能
- 提示用户可以点击智能卖出或使用下拉菜单选择比例

## 用户体验改进

### 1. 更灵活的卖出选项
- 用户不再只能卖出全部代币
- 可以根据风险偏好选择合适的卖出比例
- 支持部分卖出，保留部分仓位

### 2. 更详细的确认信息
- 卖出前显示详细的交易信息
- 包含预估滑点，帮助用户做出决策
- 风险代币有特殊警告提示

### 3. 智能建议功能
- 提供智能卖出建议，降低决策难度
- 可扩展为根据代币价值、流动性等因素提供个性化建议

## 技术实现细节

### 1. 菜单创建
```python
# 创建卖出菜单
self.sell_menu = QMenu(self.sell_button)

# 添加卖出比例选项
percentages = [10, 25, 50, 75, 100]
for percentage in percentages:
    action = QAction(f"卖出 {percentage}%", self.sell_button)
    action.triggered.connect(
        lambda checked, p=percentage: self.sell_token_percentage(p)
    )
    self.sell_menu.addAction(action)

# 设置按钮菜单
self.sell_button.setMenu(self.sell_menu)
```

### 2. 确认对话框使用
```python
dialog = SellConfirmDialog(
    token_symbol=token_symbol,
    token_balance=str(self.current_token_balance),
    sell_percentage=percentage,
    parent=self,
    estimated_slippage=estimated_slippage,
    is_risk_token=False,
    target_symbol="SOL"
)

if dialog.exec_() == QDialog.Accepted:
    # 用户确认，执行卖出
    sell_amount = self.current_token_balance * (percentage / 100.0)
    self.execute_real_sell(token_address, token_symbol, sell_amount)
```

## 测试
创建了 `test_sell_dialog.py` 测试脚本，可以验证：
- 普通代币卖出确认对话框
- 风险代币卖出确认对话框（带警告）
- SOL代币卖出确认对话框（带手续费提醒）

## 兼容性
- 保持了原有的卖出功能，现有代码无需修改
- 新功能为可选增强，不影响基本交易流程
- 所有必要的PyQt5组件都已正确导入

## 未来扩展建议
1. 可以根据代币价值、流动性等因素智能调整推荐的卖出比例
2. 可以添加自定义卖出比例输入功能
3. 可以集成更精确的滑点预估算法
4. 可以添加卖出历史记录和统计分析 