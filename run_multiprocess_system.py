#!/usr/bin/env python3
"""
多进程交易策略分析系统启动脚本
集成trending_window和holdings_panel_window的多进程版本
"""
import sys
import os
import logging
from multiprocessing import freeze_support

# 确保工作目录正确
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)
sys.path.insert(0, current_dir)

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('multiprocess_trading_system.log', encoding='utf-8')
        ]
    )
    
    # 减少某些模块的日志输出
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

def check_dependencies():
    """检查必要的依赖库"""
    required_modules = [
        'PyQt5',
        'pandas',
        'numpy',
        'requests',
        'mplfinance'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少必要的依赖库: {', '.join(missing_modules)}")
        print("请使用以下命令安装：")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    print("✅ 所有依赖库检查通过")
    return True

def check_config_files():
    """检查配置文件"""
    required_files = [
        'api_service.py',
        'config.py',
        'trending_window.py',
        'holdings_panel_window.py',
        'multi_thread_ohlcv_manager.py',
        'signal_aggregator.py',
        'multiprocess_window_manager.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要的文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件检查通过")
    return True

def main():
    """主函数"""
    print("🚀 启动多进程交易策略分析系统...")
    print("=" * 50)
    
    # 多进程支持 (Windows需要)
    freeze_support()
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 依赖检查
        if not check_dependencies():
            return 1
        
        # 文件检查
        if not check_config_files():
            return 1
        
        print("\n📋 系统功能:")
        print("• 趋势分析窗口 - 分析热门代币趋势")
        print("• 持仓分析窗口 - 分析钱包持仓策略")
        print("• 策略信号聚合 - 跨进程信号整合")
        print("• 多进程架构 - 提高稳定性和性能")
        
        print("\n🔧 启动多进程管理器...")
        
        # 导入并启动多进程管理器
        from multiprocess_window_manager import main as manager_main
        
        logger.info("🎯 多进程交易策略分析系统启动")
        return manager_main()
        
    except KeyboardInterrupt:
        print("\n⚠️  收到中断信号，正在关闭系统...")
        logger.info("用户中断程序")
        return 0
    
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        logger.error(f"系统启动失败: {e}", exc_info=True)
        return 1
    
    finally:
        print("\n🏁 系统已关闭")
        logger.info("多进程交易策略分析系统已关闭")

if __name__ == '__main__':
    print("""
╔══════════════════════════════════════════════════════════════╗
║                多进程交易策略分析系统                          ║
║                                                              ║
║  🔥 特性:                                                     ║
║  • 多进程架构 - 独立运行趋势和持仓分析                         ║
║  • 策略信号聚合 - 智能整合多源信号                             ║
║  • 实时K线图表 - 支持多种技术指标                              ║
║  • 自动策略分析 - VWAP、MACD、RSI等策略                        ║
║                                                              ║
║  📖 使用说明:                                                 ║
║  1. 点击"启动趋势分析窗口"分析热门代币                         ║
║  2. 点击"启动持仓分析窗口"分析钱包持仓                         ║
║  3. 查看主界面的聚合策略信号                                   ║
║  4. 双击代币可打开详细K线图表                                 ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    exit_code = main()
    sys.exit(exit_code) 