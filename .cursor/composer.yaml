name: "bt-gui-trading-system"
description: "PyQt5 cryptocurrency trading system with real-time analysis"

# 项目上下文文件
include:
  - "main.py"
  - "ui/**/*.py"
  - "strategies/**/*.py"
  - "services/**/*.py"
  - "config.py"
  - "api_service.py"
  - "indicators.py"
  - "README.md"
  - "requirements.txt"

# 排除的文件和目录
exclude:
  - "__pycache__/**"
  - "*.pyc"
  - "dist/**"
  - "build/**"
  - "*.db"
  - "*.log"
  - ".git/**"
  - "*.csv"
  - "*.prof"

# AI助手行为配置
ai_config:
  language: "zh-CN"
  coding_style: "pep8"
  comment_language: "chinese"
  
# 项目特定的代码模板
templates:
  pyqt_widget: |
    class {ClassName}(QWidget):
        """
        {description}
        """
        
        def __init__(self, parent=None):
            super().__init__(parent)
            self.init_ui()
        
        def init_ui(self):
            """初始化UI"""
            layout = QVBoxLayout()
            # TODO: 添加UI组件
            self.setLayout(layout)
  
  signal_handler: |
    @pyqtSlot({signal_types})
    def {handler_name}(self, {parameters}):
        """处理{signal_description}信号"""
        try:
            # TODO: 实现信号处理逻辑
            logger.info(f"处理信号: {signal_description}")
        except Exception as e:
            logger.error(f"处理信号失败: {e}", exc_info=True)

# 常用导入语句
common_imports:
  - "from PyQt5.QtWidgets import *"
  - "from PyQt5.QtCore import *"
  - "from PyQt5.QtGui import *"
  - "import logging"
  - "import pandas as pd"
  - "from typing import Dict, List, Optional" 