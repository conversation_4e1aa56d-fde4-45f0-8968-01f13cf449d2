# BT-GUI 交易系统上下文

## 项目架构

### 主要模块结构
```
bt-gui/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── api_service.py         # API服务层
├── indicators.py          # 技术指标计算
├── ui/                    # 用户界面模块
│   ├── live_trading_widget.py      # 实盘交易主组件
│   ├── headless_chart_widget.py    # 无GUI图表分析
│   ├── portfolio_widget.py         # 投资组合管理
│   ├── chart_widget.py             # GUI图表组件
│   └── styles.py                   # UI样式定义
├── strategies/            # 交易策略模块
│   ├── __init__.py
│   ├── base_strategy.py
│   └── various_strategies.py
├── services/             # 服务层
│   ├── wallet_service.py
│   └── database_service.py
└── utils/               # 工具函数
```

## 核心功能模块

### 1. LiveTradingWidget (实盘交易)
- **目的**: 主要的交易界面，整合所有交易功能
- **关键特性**:
  - 趋势币实时监控
  - 后台策略分析（使用HeadlessChartWidget池）
  - 交易信号收集和显示
  - 买卖操作执行
  - Webhook集成

### 2. HeadlessChartWidget (后台分析)
- **目的**: 无GUI的图表分析组件，专为性能优化
- **关键特性**:
  - 基于QObject，无界面开销
  - 自动化K线数据获取和分析
  - 策略信号生成
  - 与APIService集成

### 3. PortfolioWidget (投资组合)
- **目的**: 管理用户的加密货币投资组合
- **关键特性**:
  - 实时持仓查询
  - 收益统计
  - 风险分析

### 4. APIService (数据服务)
- **目的**: 统一管理所有外部API调用
- **支持的API**:
  - Birdeye API (主要数据源)
  - 自定义历史数据API
  - 钱包余额查询

### 5. 策略系统
- **目的**: 提供多种技术分析策略
- **包含策略**:
  - VWAP交叉策略
  - SAR抛物线策略
  - RSI策略
  - MACD策略
  - 等等...

## 数据流程

### 实时交易信号流程
1. **数据获取**: APIService → Birdeye API获取OHLCV数据
2. **后台分析**: HeadlessChartWidget池并行分析30个代币
3. **策略计算**: TechnicalIndicators计算指标 → Strategy生成信号
4. **信号传递**: PyQt信号槽机制传递到UI
5. **用户交互**: LiveTradingWidget显示信号，用户决策执行

### 投资组合数据流程
1. **钱包连接**: WalletService连接用户钱包
2. **持仓查询**: 获取代币余额和价值
3. **数据分析**: PortfolioWidget分析收益和风险
4. **界面更新**: 实时更新持仓信息

## 性能优化策略

### 1. 后台分析优化
- 使用HeadlessChartWidget替代ChartWidget减少GUI开销
- 30个实例的对象池复用，避免频繁创建销毁
- 分批处理，避免UI线程阻塞

### 2. 数据缓存
- OHLCV数据本地缓存
- API调用结果缓存
- 避免重复请求

### 3. 异步处理
- 所有网络请求异步执行
- 使用QThread进行耗时操作
- PyQt信号槽解耦UI和业务逻辑

## 关键配置

### API配置
```python
# config.py
PORTFOLIO_CONFIG = {
    "birdeye_api_key": "your_key",
    "api_base_url": "https://public-api.birdeye.so",
    "default_strategy": "VWAP 交叉策略",
    "okx_dex_api_url": "https://www.okx.com/api/v5/dex/"
}
```

### 信号处理配置
- 信号时间戳：使用图表事件时间，非当前系统时间
- 信号去重：防止重复信号干扰
- 信号冷却：控制信号频率

## 部署和运行

### 开发环境
```bash
pip install -r requirements.txt
python main.py
```

### 生产环境
```bash
python build_package.py  # 打包为可执行文件
```

## 常见问题和解决方案

### 1. API限流
- 实现请求频率控制
- 使用缓存减少API调用
- 错误重试机制

### 2. 内存管理
- 及时清理ChartWidget实例
- 定期清理历史数据
- 监控内存使用

### 3. 线程安全
- 使用PyQt信号槽进行线程间通信
- 避免在子线程中直接操作UI
- 合理使用锁机制

## 开发指南

### 添加新策略
1. 在strategies/目录创建新策略类
2. 继承BaseStrategy基类
3. 实现generate_signals方法
4. 在StrategyFactory中注册

### 添加新UI组件
1. 继承QWidget类
2. 实现init_ui方法
3. 使用PyQt信号槽与其他组件通信
4. 应用统一的样式

### 添加新API
1. 在APIService中添加新方法
2. 实现异步调用
3. 添加错误处理和重试
4. 更新相关配置 