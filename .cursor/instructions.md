# 项目指令 - BT-GUI 交易系统

## 项目概述
这是一个基于 PyQt5 的加密货币交易系统，专注于实时趋势分析和自动化交易策略执行。

## 核心组件
- **LiveTradingWidget**: 实盘交易主组件
- **HeadlessChartWidget**: 无GUI后台图表分析组件  
- **PortfolioWidget**: 投资组合管理组件
- **APIService**: 数据获取服务（Birdeye API）
- **StrategyFactory**: 策略工厂类
- **TechnicalIndicators**: 技术指标计算

## 编程规范

### 1. 代码风格
- 使用中文注释和日志信息
- 遵循 PEP 8 编码规范
- 函数名使用下划线命名法 (snake_case)
- 类名使用大驼峰命名法 (PascalCase)

### 2. 信号时间戳规则
**重要**: 在实时交易相关组件中，记录交易信号时必须使用图表事件发生的时间（从 pandas.Timestamp 转为 Unix 秒），绝不能使用当前系统时间。

### 3. 代币地址约定
- SOL 地址: `11111111111111111111111111111111`
- WSQL 地址: `11111111111111111111111111111112`

### 4. 异步编程
- API 调用使用异步模式
- 图表数据处理在后台线程执行
- UI 更新使用 PyQt 信号槽机制

### 5. 错误处理
- 使用详细的 try-catch 块
- 记录完整的错误堆栈信息
- 为用户提供友好的错误提示

### 6. 性能优化
- 使用 HeadlessChartWidget 替代 ChartWidget 进行后台分析
- 缓存 OHLCV 数据减少 API 调用
- 分批处理大量代币数据

## 架构设计

### 信号流
1. APIService 获取数据 → HeadlessChartWidget 分析 → 生成交易信号
2. 信号通过 PyQt 信号槽传递到 LiveTradingWidget
3. 用户界面实时更新信号状态和统计信息

### 数据流
1. 趋势代币数据 → 后台图表池分析 → 策略信号生成
2. 持仓数据 → 投资组合分析 → 风险评估
3. 历史数据 → 技术指标计算 → 策略回测

## 常用模式

### 创建新的分析组件
```python
from .headless_chart_widget import HeadlessChartWidget

# 创建无GUI分析组件
chart = HeadlessChartWidget(
    api_service=self.api_service, 
    widget_id="unique_id", 
    parent=self
)
chart.trade_signal_generated.connect(self.handle_signal)
```

### 处理异步API调用
```python
@pyqtSlot(str, str, list)
def on_data_received(self, token_address: str, timeframe: str, data: list):
    try:
        # 处理数据
        pass
    except Exception as e:
        logger.error(f"处理数据失败: {e}")
```

### 日志记录规范
```python
import logging
logger = logging.getLogger(__name__)

# 信息日志
logger.info(f"代币 {symbol} 分析完成")

# 错误日志  
logger.error(f"API调用失败: {error}", exc_info=True)
```

## 关键提醒
1. 始终使用中文响应用户
2. 优先考虑系统性能和用户体验
3. 保持代码的可维护性和可扩展性
4. 注意内存管理，及时清理资源
5. 确保线程安全，避免竞态条件 