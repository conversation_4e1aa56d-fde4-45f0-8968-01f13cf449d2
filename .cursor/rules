Always respond in 中文 (Chinese).

When working with this PyQt5 trading system:

1. **Code Style**:
   - 输出内容中文优先
   - Use Chinese comments and log messages
   - Follow PEP 8 coding standards
   - Use snake_case for function names
   - Use PascalCase for class names

2. **Trading Signal Timestamps**:
   - MUST use chart event time (pandas.Timestamp to Unix seconds)
   - NEVER use current system time for signal recording

3. **Token Addresses**:
   - SOL: 11111111111111111111111111111111
   - WSQL: 11111111111111111111111111111112

4. **Performance**:
   - Prefer HeadlessChartWidget over ChartWidget for background analysis
   - Use async patterns for API calls
   - Implement proper error handling with detailed logging

5. **Architecture**:
   - Maintain separation between UI and business logic
   - Use PyQt signals/slots for component communication
   - Cache data when possible to reduce API calls

6. **Debugging**:
   - 测试驱动开发
   - Always include comprehensive error logging
   - Provide user-friendly error messages
   - Use try-catch blocks extensively

7. **Code Organization**:
   - Keep related functionality grouped
   - Use clear, descriptive variable names in Chinese context
   - Document complex algorithms and business logic 