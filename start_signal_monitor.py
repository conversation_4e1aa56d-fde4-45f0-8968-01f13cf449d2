#!/usr/bin/env python3
"""
启动策略信号监控服务器
独立进程，接收来自trending_window和holdings_panel的策略信号并执行实盘交易
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('signal_monitor.log', encoding='utf-8')
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('signal_monitor').setLevel(logging.INFO)
    logging.getLogger('signal_client').setLevel(logging.INFO)
    
    logger = logging.getLogger(__name__)
    logger.info("=== 策略信号监控服务器启动 ===")
    return logger

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 检查必要的依赖
        logger.info("检查依赖模块...")
        
        # 导入并启动信号监控器
        from signal_monitor import main as run_signal_monitor
        
        logger.info("启动信号监控服务器...")
        run_signal_monitor()
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保所有依赖模块都已正确安装")
        sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"启动信号监控服务器失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == '__main__':
    main() 