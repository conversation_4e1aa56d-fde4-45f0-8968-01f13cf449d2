"""
Portfolio 组件 - 显示钱包余额和代币信息
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox, QFormLayout,
    QComboBox, QSplitter, QTextEdit, QProgressBar, QMessageBox, QFrame,
    QTabWidget, QScrollArea, QGridLayout, QSpacerItem, QSizePolicy,
    QMenu, QAction, QDialog, QDialogButtonBox, QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette

from config import PORTFOLIO_CONFIG
from okx_dex_client import OKXDexClient, AllTokenBalancesRequest, TotalValueRequest, QuoteRequest, SwapRequest
from ui.portfolio_monitor import PortfolioMonitor
from api_service import APIService

logger = logging.getLogger(__name__)


class NumericTableWidgetItem(QTableWidgetItem):
    """数值型表格项，支持正确的数值排序"""
    
    def __init__(self, text: str, numeric_value: float):
        super().__init__(text)
        self.numeric_value = numeric_value
    
    def __lt__(self, other):
        if isinstance(other, NumericTableWidgetItem):
            return self.numeric_value < other.numeric_value
        return super().__lt__(other)


class SellConfirmDialog(QDialog):
    """卖出确认对话框"""
    
    def __init__(self, token_symbol: str, token_balance: str, sell_percentage: int, parent=None, estimated_slippage: str = "", is_risk_token: bool = False, target_symbol: str = "USDC"):
        super().__init__(parent)
        self.token_symbol = token_symbol
        self.token_balance = float(token_balance)
        self.sell_percentage = sell_percentage
        self.sell_amount = self.token_balance * (sell_percentage / 100.0)
        self.estimated_slippage = estimated_slippage
        self.is_risk_token = is_risk_token
        self.target_symbol = target_symbol
        
        title_prefix = "⚠️ 风险代币 - " if is_risk_token else ""
        self.setWindowTitle(f"{title_prefix}确认卖出 {token_symbol}")
        self.setModal(True)
        self.resize(400, 300 if is_risk_token else 250)
        
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(f"卖出 {self.token_symbol} → {self.target_symbol}")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 详细信息
        info_layout = QFormLayout()
        info_layout.addRow("代币:", QLabel(self.token_symbol))
        info_layout.addRow("目标代币:", QLabel(f"🎯 {self.target_symbol}"))
        info_layout.addRow("当前余额:", QLabel(f"{self.token_balance:.6f}"))
        info_layout.addRow("卖出比例:", QLabel(f"{self.sell_percentage}%"))
        info_layout.addRow("卖出数量:", QLabel(f"{self.sell_amount:.6f}"))
        info_layout.addRow("剩余数量:", QLabel(f"{self.token_balance - self.sell_amount:.6f}"))
        
        # 显示滑点信息（如果有）
        if self.estimated_slippage:
            slippage_label = QLabel(f"{self.estimated_slippage}%")
            slippage_label.setStyleSheet("color: orange; font-weight: bold;")
            info_layout.addRow("预计滑点:", slippage_label)
        
        info_widget = QWidget()
        info_widget.setLayout(info_layout)
        layout.addWidget(info_widget)
        
        # 警告信息
        warning_text = "⚠️ 请确认卖出操作。此操作不可撤销！"
        
        if self.is_risk_token:
            warning_text = "🚨 风险代币警告！\n\n" + warning_text
            warning_text += "\n\n⚠️ 此代币被标记为风险代币，可能是：\n• 空投代币（价值可能为零）\n• 貔貅盘代币（可能无法正常交易）\n• 存在其他风险因素\n\n请谨慎考虑是否继续交易！"
        
        if self.token_symbol.upper() == 'SOL':
            warning_text += "\n\n💰 注意：SOL用作交易手续费，请确保保留足够的SOL用于后续交易！"
        
        warning_label = QLabel(warning_text)
        if self.is_risk_token:
            warning_label.setStyleSheet("color: #ff1744; font-weight: bold; padding: 10px; background-color: #ffebee; border: 2px solid #ff5252; border-radius: 5px;")
        else:
            warning_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
        warning_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning_label)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)


class PortfolioDataThread(QThread):
    """Portfolio数据获取线程"""
    
    # 信号定义
    total_value_updated = pyqtSignal(dict)
    token_balances_updated = pyqtSignal(dict)
    api_status_updated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, wallet_address: str, chain_id: str = "501"):
        super().__init__()
        self.wallet_address = wallet_address
        self.chain_id = chain_id
        self.okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        self.should_stop = False
    
    def run(self):
        """执行数据获取"""
        try:
            if not self.wallet_address:
                self.error_occurred.emit("钱包地址不能为空")
                return
            
            # 健康检查
            health_result = self.okx_client.health_check()
            self.api_status_updated.emit(health_result)
            
            if not health_result.get('success'):
                self.error_occurred.emit(f"OKX DEX API服务不可用: {health_result.get('error', '未知错误')}")
                return
            
            # 获取总估值
            total_value_request = TotalValueRequest(
                address=self.wallet_address,
                chains=self.chain_id
            )
            total_value_result = self.okx_client.get_total_value(total_value_request)
            self.total_value_updated.emit(total_value_result)
            
            # 检查总估值请求是否成功
            if not total_value_result.get('success'):
                error_msg = total_value_result.get('error', '未知错误')
                if 'address format' in error_msg.lower():
                    self.error_occurred.emit(f"钱包地址格式错误: {error_msg}")
                    return
                elif 'too many requests' in error_msg.lower():
                    self.error_occurred.emit("API请求频率限制，请稍后再试")
                    return
            
            # 获取代币余额
            token_balances_request = AllTokenBalancesRequest(
                address=self.wallet_address,
                chains=self.chain_id,
                exclude_risk_token="0"
            )
            token_balances_result = self.okx_client.get_all_token_balances(token_balances_request)
            self.token_balances_updated.emit(token_balances_result)
            
            # 检查代币余额请求是否成功
            if not token_balances_result.get('success'):
                error_msg = token_balances_result.get('error', '未知错误')
                if 'address format' in error_msg.lower():
                    self.error_occurred.emit(f"钱包地址格式错误: {error_msg}")
                    return
                elif 'too many requests' in error_msg.lower():
                    self.error_occurred.emit("API请求频率限制，请稍后再试")
                    return
            
        except Exception as e:
            logger.error(f"Portfolio数据获取线程错误: {e}", exc_info=True)
            self.error_occurred.emit(f"数据获取失败: {str(e)}")
    
    def stop(self):
        """停止线程"""
        self.should_stop = True


class TokenBalanceWidget(QWidget):
    """单个代币余额显示组件"""
    
    def __init__(self, token_data: Dict):
        super().__init__()
        self.token_data = token_data
        self.init_ui()
    
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 代币信息
        token_info_layout = QVBoxLayout()
        
        # 代币名称和符号
        name_label = QLabel(f"{self.token_data.get('symbol', 'Unknown')} ({self.token_data.get('tokenContractAddress', '')[:8]}...)")
        name_label.setFont(QFont("Arial", 10, QFont.Bold))
        token_info_layout.addWidget(name_label)
        
        # 余额
        balance_text = self.token_data.get('balanceAmount', '0')
        balance_label = QLabel(f"余额: {balance_text}")
        token_info_layout.addWidget(balance_label)
        
        layout.addLayout(token_info_layout)
        
        # 估值
        value_layout = QVBoxLayout()
        value_text = self.token_data.get('valueUsd', '0')
        value_label = QLabel(f"${value_text}")
        value_label.setFont(QFont("Arial", 10, QFont.Bold))
        value_label.setAlignment(Qt.AlignRight)
        value_layout.addWidget(value_label)
        
        # 价格
        price_text = self.token_data.get('priceUsd', '0')
        price_label = QLabel(f"${price_text}")
        price_label.setAlignment(Qt.AlignRight)
        value_layout.addWidget(price_label)
        
        layout.addLayout(value_layout)
        
        self.setLayout(layout)
        
        # 设置边框
        self.setStyleSheet("""
            TokenBalanceWidget {
                border: 1px solid #ddd;
                border-radius: 5px;
                margin: 2px;
                padding: 5px;
                background-color: white;
            }
            TokenBalanceWidget:hover {
                background-color: #f0f0f0;
            }
        """)


class PortfolioWidget(QWidget):
    """Portfolio主组件"""
    
    def __init__(self, api_service: Optional[APIService] = None, parent=None):
        super().__init__(parent)
        self.api_service = api_service
        self.okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        self.data_thread = None
        self.default_wallet_address = None  # 存储默认钱包地址
        
        # 创建Portfolio管理器
        if self.api_service:
            self.portfolio_monitor = PortfolioMonitor(self.api_service, parent=self)
            # 连接Portfolio状态信号
            self.portfolio_monitor.status_changed.connect(self.on_portfolio_status_changed)

            # 立即设置初始状态
            QTimer.singleShot(100, lambda: self.portfolio_monitor.status_changed.emit("Portfolio: 待启动（请刷新钱包）", 0, 0))

            logger.info("PortfolioWidget: Portfolio管理器创建成功")
        else:
            self.portfolio_monitor = None
            logger.warning("PortfolioWidget: APIService未提供，Portfolio管理功能不可用")
        
        # 🔥🔥 初始化未处理信号存储
        self.unprocessed_signals = []  # 存储未处理的Portfolio信号
        
        # 🔥🔥 唯一信号管理（防止重复信号干扰）- 参考live_trading_widget.py
        self.unique_signals = {}  # {unique_key: {'signal_type': str, 'price': float, 'timestamp': int, 'processed': bool}}
        
        
        # 🔥🔥 设置定时器定期更新未处理信号表格
        self.signals_update_timer = QTimer()
        self.signals_update_timer.timeout.connect(self.update_unprocessed_signals_table)
        self.signals_update_timer.start(5000)  # 每5秒更新一次
        
        self.init_ui()
        self.load_default_wallet_address()  # 在 UI 初始化后加载默认钱包地址
        self.setup_timer()
    
    def load_default_wallet_address(self):
        """从 OKX DEX API 获取默认钱包地址"""
        try:
            wallet_info = self.okx_client.get_wallet_info()
            if wallet_info.get('success') and wallet_info.get('data'):
                # 获取 Solana 钱包地址
                solana_address = wallet_info['data'].get('solanaWalletAddress')
                if solana_address:
                    self.default_wallet_address = solana_address
                    # 如果输入框为空，设置默认地址
                    if hasattr(self, 'wallet_address_input') and not self.wallet_address_input.text():
                        self.wallet_address_input.setText(solana_address)
                        logger.info(f"已设置默认钱包地址: {solana_address}")
                    # 启用"使用默认钱包"按钮
                    if hasattr(self, 'use_default_wallet_btn'):
                        self.use_default_wallet_btn.setEnabled(True)
                else:
                    logger.warning("API返回的钱包信息中没有 Solana 地址")
                    if hasattr(self, 'use_default_wallet_btn'):
                        self.use_default_wallet_btn.setEnabled(False)
            else:
                error_msg = wallet_info.get('error', '未知错误')
                logger.error(f"获取默认钱包地址失败: {error_msg}")
                if hasattr(self, 'use_default_wallet_btn'):
                    self.use_default_wallet_btn.setEnabled(False)
        except Exception as e:
            logger.error(f"获取默认钱包地址时发生异常: {e}", exc_info=True)
            if hasattr(self, 'use_default_wallet_btn'):
                self.use_default_wallet_btn.setEnabled(False)
    
    def use_default_wallet(self):
        """使用默认钱包地址"""
        if self.default_wallet_address:
            self.wallet_address_input.setText(self.default_wallet_address)
            logger.info(f"已切换到默认钱包地址: {self.default_wallet_address}")
            # 可选：自动刷新余额
            # self.refresh_portfolio()
        else:
            # 如果还没有获取到默认地址，尝试重新获取
            self.load_default_wallet_address()
            if self.default_wallet_address:
                self.wallet_address_input.setText(self.default_wallet_address)
            else:
                QMessageBox.warning(self, "警告", "无法获取默认钱包地址，请检查本地API服务是否正常运行")
    
    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(3)
        
        # 钱包设置区域（紧凑型）
        wallet_group = self.create_wallet_settings_group()
        main_layout.addWidget(wallet_group)
        
        # 水平布局：总估值 + 代币列表
        content_layout = QHBoxLayout()
        content_layout.setSpacing(5)
        
        # 左侧：总览面板（更窄）
        overview_widget = self.create_overview_widget()
        overview_widget.setMaximumWidth(280)
        overview_widget.setMinimumWidth(250)
        content_layout.addWidget(overview_widget)
        
        # 右侧：代币余额列表（占主要空间）
        balance_widget = self.create_balance_widget()
        content_layout.addWidget(balance_widget, 1)
        
        main_layout.addLayout(content_layout)
        
        # 紧凑状态栏
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 11px;
                padding: 2px 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3a4a5c, stop: 1 #2c3e50);
                border-top: 1px solid #1a252f;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
    
    def create_wallet_settings_group(self) -> QWidget:
        """创建钱包设置区域"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2c3e50, stop: 1 #34495e);
                border: 1px solid #1a252f;
                border-radius: 4px;
            }
        """)
        
        # 紧凑型水平布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(8, 6, 8, 6)
        main_layout.setSpacing(12)
        
        # 钱包地址（最重要，占更多空间）
        addr_label = QLabel("地址:")
        addr_label.setStyleSheet("font-weight: bold; font-size: 11px; color: #ecf0f1;")
        self.wallet_address_input = QLineEdit()
        self.wallet_address_input.setPlaceholderText("钱包地址...")
        # 不再从配置文件读取，而是等待从 API 加载
        # self.wallet_address_input.setText(PORTFOLIO_CONFIG.get("default_wallet_address", ""))
        self.wallet_address_input.setStyleSheet("""
            QLineEdit {
                padding: 4px 8px;
                font-size: 11px;
                font-family: monospace;
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background-color: #34495e;
            }
        """)
        main_layout.addWidget(addr_label)
        main_layout.addWidget(self.wallet_address_input, 2)
        
        # 添加"使用默认钱包"按钮
        self.use_default_wallet_btn = QPushButton("默认")
        self.use_default_wallet_btn.setToolTip("使用本地API的默认钱包地址")
        self.use_default_wallet_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5a6c7d, stop: 1 #4a5568);
                color: white;
                border: 1px solid #3a4a5c;
                padding: 4px 8px;
                font-size: 11px;
                border-radius: 3px;
                min-width: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6a7c8d, stop: 1 #5a6c7d);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4a5568, stop: 1 #3a4a5c);
            }
            QPushButton:disabled {
                background: #3a4a5c;
                color: #7f8c8d;
            }
        """)
        self.use_default_wallet_btn.clicked.connect(self.use_default_wallet)
        main_layout.addWidget(self.use_default_wallet_btn)
        
        # 链选择（紧凑）
        chain_label = QLabel("链:")
        chain_label.setStyleSheet("font-weight: bold; font-size: 11px; color: #ecf0f1;")
        self.chain_combo = QComboBox()
        self.chain_combo.setStyleSheet("""
            QComboBox {
                font-size: 11px;
                padding: 3px 8px;
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                min-width: 80px;
            }
            QComboBox:hover {
                border: 1px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #bdc3c7;
                margin-right: 5px;
            }
        """)
        for chain_id, chain_name in PORTFOLIO_CONFIG["supported_chains"].items():
            self.chain_combo.addItem(chain_name, chain_id)
        main_layout.addWidget(chain_label)
        main_layout.addWidget(self.chain_combo)
        
        # 过滤选项（紧凑）
        self.hide_risk_tokens_checkbox = QCheckBox("隐藏风险")
        self.hide_risk_tokens_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 11px;
                color: #bdc3c7;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border: 1px solid #5a6c7d;
                border-radius: 2px;
                background-color: #3a4a5c;
            }
            QCheckBox::indicator:checked {
                background-color: #e74c3c;
                border: 1px solid #c0392b;
            }
            QCheckBox::indicator:checked::after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """)
        self.hide_risk_tokens_checkbox.setToolTip("隐藏风险代币")
        self.hide_risk_tokens_checkbox.setChecked(True)
        self.hide_risk_tokens_checkbox.stateChanged.connect(self.on_risk_filter_changed)
        main_layout.addWidget(self.hide_risk_tokens_checkbox)
        
        # 卖出目标（紧凑）
        target_label = QLabel("目标:")
        target_label.setStyleSheet("font-weight: bold; font-size: 11px; color: #ecf0f1;")
        self.target_token_combo = QComboBox()
        self.target_token_combo.setStyleSheet("""
            QComboBox {
                font-size: 11px;
                padding: 3px 8px;
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                min-width: 80px;
            }
            QComboBox:hover {
                border: 1px solid #f39c12;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #bdc3c7;
                margin-right: 5px;
            }
        """)
        self.target_token_combo.addItem("🟣 SOL", "SOL")
        self.target_token_combo.addItem("💚 USDC", "USDC")
        self.target_token_combo.setCurrentIndex(0)
        self.target_token_combo.currentTextChanged.connect(self.on_target_token_changed)
        main_layout.addWidget(target_label)
        main_layout.addWidget(self.target_token_combo)
        
        # 操作按钮（紧凑）
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border: 1px solid #2471a3;
                padding: 4px 12px;
                font-size: 11px;
                font-weight: bold;
                border-radius: 3px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2, stop: 1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2980b9, stop: 1 #1f618d);
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_portfolio)
        main_layout.addWidget(self.refresh_button)
        
        self.auto_refresh_button = QPushButton("自动:关")
        self.auto_refresh_button.setCheckable(True)
        self.auto_refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #95a5a6, stop: 1 #7f8c8d);
                color: white;
                border: 1px solid #6c7b7c;
                padding: 4px 10px;
                font-size: 11px;
                border-radius: 3px;
            }
            QPushButton:checked {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #229954);
                border: 1px solid #1e8449;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #aab7b8, stop: 1 #95a5a6);
            }
        """)
        self.auto_refresh_button.clicked.connect(self.toggle_auto_refresh)
        main_layout.addWidget(self.auto_refresh_button)
        
        widget.setLayout(main_layout)
        return widget
    
    def create_overview_widget(self) -> QWidget:
        """创建总览组件"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34495e, stop: 1 #2c3e50);
                border: 1px solid #1a252f;
                border-radius: 4px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(12, 10, 12, 10)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("总资产")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #bdc3c7;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # 总估值（大号显示）
        self.total_value_label = QLabel("$0.00")
        self.total_value_label.setFont(QFont("Arial", 20, QFont.Bold))
        self.total_value_label.setAlignment(Qt.AlignLeft)
        self.total_value_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: transparent;
                padding: 5px 0;
                text-shadow: 0 0 10px rgba(39, 174, 96, 0.3);
            }
        """)
        layout.addWidget(self.total_value_label)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #5a6c7d; background-color: #5a6c7d;")
        layout.addWidget(separator)
        
        # 状态信息区域（固定大小）
        status_layout = QVBoxLayout()
        status_layout.setSpacing(4)
        
        # API状态
        self.api_status_label = QLabel("API: 检查中...")
        self.api_status_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #95a5a6;
                padding: 2px 0;
            }
        """)
        status_layout.addWidget(self.api_status_label)
        
        # 最后更新时间
        self.last_update_label = QLabel("更新: --")
        self.last_update_label.setStyleSheet("""
            QLabel {
                font-size: 10px;
                color: #95a5a6;
                padding: 2px 0;
            }
        """)
        status_layout.addWidget(self.last_update_label)
        
        # Portfolio监控状态（新增）
        if self.portfolio_monitor:
            # 分隔线
            separator2 = QFrame()
            separator2.setFrameShape(QFrame.HLine)
            separator2.setStyleSheet("color: #5a6c7d; background-color: #5a6c7d;")
            status_layout.addWidget(separator2)
            
            # 监控状态标签
            monitor_title = QLabel("持仓监控")
            monitor_title.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    font-weight: bold;
                    color: #ecf0f1;
                    padding: 3px 0;
                }
            """)
            status_layout.addWidget(monitor_title)
            
            self.monitor_status_label = QLabel("监控: 未启动")
            self.monitor_status_label.setStyleSheet("""
                QLabel {
                    font-size: 10px;
                    color: #95a5a6;
                    padding: 2px 0;
                }
            """)
            status_layout.addWidget(self.monitor_status_label)
            
            # 监控策略显示
            strategy_name = PORTFOLIO_CONFIG.get("default_strategy", "多时间周期综合策略")
            self.monitor_strategy_label = QLabel(f"策略: {strategy_name}")
            self.monitor_strategy_label.setStyleSheet("""
                QLabel {
                    font-size: 9px;
                    color: #7f8c8d;
                    padding: 1px 0;
                }
            """)
            status_layout.addWidget(self.monitor_strategy_label)
        else:
            # 如果没有监控器，显示提示
            separator2 = QFrame()
            separator2.setFrameShape(QFrame.HLine)
            separator2.setStyleSheet("color: #5a6c7d; background-color: #5a6c7d;")
            status_layout.addWidget(separator2)
            
            self.monitor_status_label = QLabel("监控: 不可用")
            self.monitor_status_label.setStyleSheet("""
                QLabel {
                    font-size: 10px;
                    color: #e74c3c;
                    padding: 2px 0;
                }
            """)
            status_layout.addWidget(self.monitor_status_label)
        
        # 🔥 将状态信息区域添加到主布局，但不设置拉伸
        layout.addLayout(status_layout)
        
        # 🔥🔥 添加未处理信号面板，设置为拉伸占用剩余空间
        unprocessed_signals_panel = self.create_unprocessed_signals_panel()
        layout.addWidget(unprocessed_signals_panel, 1)  # stretch=1，让它占用剩余所有空间
        
        # 🔥 移除 layout.addStretch() - 不再需要，因为信号面板会占用剩余空间
        
        widget.setLayout(layout)
        return widget
    
    def create_balance_widget(self) -> QWidget:
        """创建余额显示组件"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3a4a5c, stop: 1 #2c3e50);
                border: 1px solid #1a252f;
                border-radius: 4px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 紧凑标题栏
        title_bar = QWidget()
        title_bar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4a5568, stop: 1 #3a4a5c);
                border-bottom: 1px solid #1a252f;
                border-radius: 0;
                padding: 8px 12px;
            }
        """)
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        balance_title = QLabel("资产列表")
        balance_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #ecf0f1;
                background-color: transparent;
            }
        """)
        title_layout.addWidget(balance_title)
        title_layout.addStretch()
        
        title_bar.setLayout(title_layout)
        layout.addWidget(title_bar)
        
        # 专业表格
        self.balance_table = QTableWidget()
        self.balance_table.setColumnCount(7)
        self.balance_table.setHorizontalHeaderLabels([
            "代币", "符号", "余额", "价格", "价值", "合约", "操作"
        ])
        
        # 专业表格样式
        self.balance_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c3e50;
                border: none;
                gridline-color: #34495e;
                font-size: 11px;
                color: #ecf0f1;
                selection-background-color: #3498db;
            }
            QTableWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #34495e;
                background-color: transparent;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: #ffffff;
            }
            QTableWidget::item:alternate {
                background-color: #34495e;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4a5568, stop: 1 #3a4a5c);
                border: none;
                border-bottom: 2px solid #1a252f;
                border-right: 1px solid #2c3e50;
                padding: 8px 8px;
                font-size: 11px;
                font-weight: bold;
                color: #ecf0f1;
                text-align: left;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QScrollBar:vertical {
                background-color: #34495e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #3498db;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #5dade2;
            }
        """)
        
        # 设置表格属性
        header = self.balance_table.horizontalHeader()
        header.setDefaultSectionSize(80)  # 默认列宽
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 代币名
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # 符号
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 余额
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 价格
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 价值
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # 合约地址
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 操作
        
        self.balance_table.setColumnWidth(1, 70)   # 符号列
        self.balance_table.setColumnWidth(6, 130)   # 操作列（加宽以容纳更大的按钮）
        
        # 表格行为
        self.balance_table.setAlternatingRowColors(False)
        self.balance_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.balance_table.setSelectionMode(QTableWidget.SingleSelection)
        self.balance_table.setSortingEnabled(True)
        self.balance_table.setShowGrid(False)
        self.balance_table.verticalHeader().setVisible(False)
        self.balance_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.balance_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        layout.addWidget(self.balance_table)
        
        widget.setLayout(layout)
        return widget
    
    def setup_timer(self):
        """设置定时器"""
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.refresh_portfolio)
        self.auto_refresh_interval = PORTFOLIO_CONFIG.get("refresh_interval", 30000)
    
    def validate_solana_address(self, address: str) -> bool:
        """验证Solana钱包地址格式"""
        # Solana地址应该是44个字符的base58编码
        if len(address) != 44:
            return False
        
        # 检查是否只包含base58字符（不包含0、O、I、l）
        import string
        base58_chars = string.ascii_letters + string.digits
        base58_chars = base58_chars.replace('0', '').replace('O', '').replace('I', '').replace('l', '')
        
        return all(c in base58_chars for c in address)
    
    def get_target_token_info(self):
        """获取选择的目标代币信息"""
        target_choice = self.target_token_combo.currentData()
        
        if target_choice == "SOL":
            return {
                "symbol": "SOL",
                "address": "********************************",  # SOL的系统程序地址
                "decimals": 9,
                "display_name": "SOL"
            }
        else:  # USDC
            return {
                "symbol": "USDC", 
                "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "decimals": 6,
                "display_name": "USDC"
            }
    
    @pyqtSlot()
    def refresh_portfolio(self):
        """刷新Portfolio数据"""
        wallet_address = self.wallet_address_input.text().strip()
        
        if not wallet_address:
            QMessageBox.warning(self, "输入错误", "请先输入钱包地址")
            return
        
        # 获取选择的链ID
        chain_id = self.chain_combo.currentData()
        
        # 验证Solana地址格式
        if chain_id == "501" and not self.validate_solana_address(wallet_address):
            QMessageBox.warning(
                self, 
                "地址格式错误", 
                f"Solana钱包地址格式不正确。\n\n"
                f"要求：\n"
                f"- 长度必须是44个字符\n"
                f"- 只能包含base58字符\n\n"
                f"当前地址长度：{len(wallet_address)}个字符\n"
                f"示例地址：EHo5NW4NZe8QKFVLKVFj1zbMH1dUWLjmYhCTqU5mRtzM"
            )
            return
        
        # 检查是否使用默认钱包
        is_default_wallet = (wallet_address == self.default_wallet_address)
        wallet_type = "默认钱包" if is_default_wallet else "自定义钱包"
        
        # 禁用刷新按钮
        self.refresh_button.setEnabled(False)
        self.refresh_button.setText("刷新中...")
        
        # 更新状态
        self.status_label.setText(f"正在获取数据... ({wallet_type})")
        
        # 启动数据获取线程
        if self.data_thread and self.data_thread.isRunning():
            self.data_thread.stop()
            self.data_thread.wait()
        
        self.data_thread = PortfolioDataThread(wallet_address, chain_id)
        self.data_thread.total_value_updated.connect(self.update_total_value)
        self.data_thread.token_balances_updated.connect(self.update_token_balances)
        self.data_thread.api_status_updated.connect(self.update_api_status)
        self.data_thread.error_occurred.connect(self.handle_error)
        self.data_thread.finished.connect(self.refresh_finished)
        
        self.data_thread.start()
    
    @pyqtSlot()
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        if self.auto_refresh_button.isChecked():
            self.auto_refresh_timer.start(self.auto_refresh_interval)
            self.auto_refresh_button.setText("自动:开")
            self.status_label.setText(f"自动刷新 (每{self.auto_refresh_interval//1000}s)")
        else:
            self.auto_refresh_timer.stop()
            self.auto_refresh_button.setText("自动:关")
            self.status_label.setText("手动刷新模式")
    
    @pyqtSlot()
    def on_risk_filter_changed(self):
        """风险代币过滤状态改变时的处理"""
        # 如果已经有代币数据，重新应用过滤
        if hasattr(self, '_last_token_data') and self._last_token_data:
            logger.info(f"风险代币过滤状态改变: {'隐藏' if self.hide_risk_tokens_checkbox.isChecked() else '显示'}风险代币")
            self.update_token_balances(self._last_token_data)
        else:
            # 如果没有数据，显示提示信息
            hide_risk = self.hide_risk_tokens_checkbox.isChecked()
            self.status_label.setText(f"风险代币过滤: {'已开启' if hide_risk else '已关闭'} - 请刷新余额查看效果")
    
    @pyqtSlot()
    def on_target_token_changed(self):
        """目标代币选择改变时的处理"""
        # 获取新的目标代币信息
        target_token_info = self.get_target_token_info()
        target_symbol = target_token_info["symbol"]
        target_icon = "🟣" if target_symbol == "SOL" else "💚"
        
        logger.info(f"目标代币切换为: {target_symbol}")
        
        # 更新所有现有卖出按钮的图标
        row_count = self.balance_table.rowCount()
        for row in range(row_count):
            self.update_sell_button_icon(row, target_icon)
        
        # 更新状态提示
        if hasattr(self, '_last_token_data') and self._last_token_data:
            current_status = self.status_label.text()
            if "风险代币过滤" not in current_status:
                self.status_label.setText(f"卖出目标已切换为 {target_symbol} {target_icon}")
        else:
            self.status_label.setText(f"卖出目标: {target_symbol} {target_icon} - 请刷新余额查看效果")
    
    @pyqtSlot(dict)
    def update_total_value(self, result: Dict):
        """更新总估值"""
        if result.get('success') and result.get('data'):
            try:
                total_value = result['data'].get('totalValue', '0')
                self.total_value_label.setText(f"${total_value}")
                logger.info(f"总估值更新: ${total_value}")
            except Exception as e:
                logger.error(f"更新总估值失败: {e}")
                self.total_value_label.setText("$0.00")
        else:
            error_msg = result.get('error', '未知错误')
            logger.warning(f"获取总估值失败: {error_msg}")
            self.total_value_label.setText("获取失败")
    
    @pyqtSlot(dict)
    def update_token_balances(self, result: Dict):
        """更新代币余额表格"""
        logger.info(f"更新代币余额 - 开始处理...")
        
        # 保存原始数据以便重新过滤
        self._last_token_data = result
        
        if not result.get('success'):
            error_msg = result.get('error', '未知错误')
            logger.warning(f"获取代币余额失败: {error_msg}")
            self.balance_table.setRowCount(0)
            return
        
        if not result.get('data'):
            logger.warning("API响应成功但没有数据部分")
            self.balance_table.setRowCount(0)
            return
        
        try:
            # 解析数据
            data_part = result['data']
            balances = []
            if isinstance(data_part, list):
                balances = data_part
            elif isinstance(data_part, dict):
                if 'data' in data_part:
                    balances = data_part['data']
                elif 'tokenBalances' in data_part:
                    balances = data_part['tokenBalances']
                elif 'balances' in data_part:
                    balances = data_part['balances']
                elif 'tokenAssets' in data_part:
                    balances = data_part['tokenAssets']
                    logger.info(f"📋 使用 data.tokenAssets，包含 {len(balances)} 个代币")
                else:
                    # 尝试使用第一个列表类型的值
                    for key, value in data_part.items():
                        if isinstance(value, list) and len(value) > 0:
                            logger.info(f"使用键 '{key}' 的值作为代币列表")
                            balances = value
                            break
            
            if not balances:
                logger.warning("没有找到代币余额数据")
                self.balance_table.setRowCount(0)
                return
            
            logger.info(f"原始代币数量: {len(balances)}")
            
            # 🚀 优化1: 过滤和排序代币，只显示有价值的代币
            valuable_tokens = []
            risk_tokens_count = 0
            hide_risk_tokens = self.hide_risk_tokens_checkbox.isChecked()
            
            # 🔍 调试：特别关注molecule代币
            molecule_debug_info = []
            
            for token in balances:
                try:
                    symbol = token.get('symbol', '').upper()
                    token_name = token.get('tokenName', token.get('name', ''))
                    
                    # 🔍 调试molecule代币
                    is_molecule = 'MOLECULE' in symbol.upper() or 'MOLECULE' in token_name.upper()
                    if is_molecule:
                        logger.info(f"🔍 发现molecule代币！原始数据: {token}")
                    
                    # 检查是否为风险代币
                    is_risk_token = token.get('isRiskToken', False)
                    if is_risk_token:
                        risk_tokens_count += 1
                        if is_molecule:
                            molecule_debug_info.append("标记为风险代币")
                    
                    # 如果开启了风险代币过滤且该代币是风险代币，跳过
                    if hide_risk_tokens and is_risk_token:
                        if is_molecule:
                            molecule_debug_info.append(f"被风险代币过滤掉 (风险过滤: {hide_risk_tokens})")
                            logger.warning(f"🔍 molecule代币被风险过滤掉了！调试信息: {molecule_debug_info}")
                        continue
                    
                    # 计算代币价值
                    balance = float(token.get('balance', 0))
                    price = float(token.get('tokenPrice', 0))
                    value = balance * price
                    
                    if is_molecule:
                        molecule_debug_info.append(f"余额: {balance}")
                        molecule_debug_info.append(f"价格: {price}")
                        molecule_debug_info.append(f"计算价值: {value}")
                    
                    # 只显示价值超过0.01美元的代币，或者是主要代币
                    is_major_token = symbol in ['SOL', 'USDC', 'USDT', 'BTC', 'ETH']
                    
                    if value > 0.01 or is_major_token:
                        token['calculated_value'] = value
                        # 保持使用API原始字段名 isRiskToken
                        valuable_tokens.append(token)
                        if is_molecule:
                            molecule_debug_info.append(f"通过价值过滤 (价值: ${value:.6f} > $0.01 或主要代币: {is_major_token})")
                            logger.info(f"✅ molecule代币通过过滤！调试信息: {molecule_debug_info}")
                    else:
                        if is_molecule:
                            molecule_debug_info.append(f"被价值过滤掉 (价值: ${value:.6f} <= $0.01, 非主要代币)")
                            logger.warning(f"🔍 molecule代币被价值过滤掉了！调试信息: {molecule_debug_info}")
                        
                except (ValueError, TypeError) as e:
                    if is_molecule:
                        molecule_debug_info.append(f"计算价值时出错: {e}")
                        logger.error(f"🔍 molecule代币计算价值出错！调试信息: {molecule_debug_info}")
                    
                    # 如果无法计算价值，保留主要代币（除非是风险代币且开启了过滤）
                    symbol = token.get('symbol', '').upper()
                    is_risk_token = token.get('isRiskToken', False)
                    
                    if is_risk_token and hide_risk_tokens:
                        continue
                        
                    if symbol in ['SOL', 'USDC', 'USDT', 'BTC', 'ETH']:
                        token['calculated_value'] = 0
                        # 保持使用API原始字段名 isRiskToken
                        valuable_tokens.append(token)
            
            # 按价值排序（降序）
            valuable_tokens.sort(key=lambda x: x.get('calculated_value', 0), reverse=True)
            
            # 🚀 优化2: 限制显示数量，避免界面卡顿
            max_display_tokens = 100  # 最多显示100个代币
            display_tokens = valuable_tokens[:max_display_tokens]
            
            logger.info(f"过滤后显示代币数量: {len(display_tokens)} / {len(balances)}")
            
            # 检查molecule是否在最终显示列表中
            molecule_in_display = any('MOLECULE' in token.get('symbol', '').upper() or 'MOLECULE' in token.get('tokenName', '').upper() for token in display_tokens)
            if not molecule_in_display:
                logger.warning(f"⚠️ molecule代币没有出现在最终显示列表中！")
                # 在valuable_tokens中查找
                molecule_in_valuable = any('MOLECULE' in token.get('symbol', '').upper() or 'MOLECULE' in token.get('tokenName', '').upper() for token in valuable_tokens)
                if molecule_in_valuable:
                    logger.warning(f"⚠️ molecule在valuable_tokens中，但被数量限制过滤了！(显示前{max_display_tokens}个)")
                else:
                    logger.warning(f"⚠️ molecule没有通过价值过滤！")
            else:
                logger.info(f"✅ molecule代币在最终显示列表中")
            
            # 🚀 优化3: 暂时禁用UI更新以提高性能
            self.balance_table.setUpdatesEnabled(False)
            self.balance_table.setSortingEnabled(False)
            self.balance_table.setRowCount(len(display_tokens))
            
            # 🚀 优化4: 批量处理，每处理10个代币就刷新一次UI
            batch_size = 10
            for batch_start in range(0, len(display_tokens), batch_size):
                batch_end = min(batch_start + batch_size, len(display_tokens))
                
                for row in range(batch_start, batch_end):
                    token = display_tokens[row]
                    
                    # 代币名称
                    token_name = (token.get('tokenName') or 
                                 token.get('name') or 
                                 token.get('symbol') or 
                                 'Unknown')
                    
                    # 如果是风险代币，添加警告标识
                    if token.get('isRiskToken', False):
                        token_name = f"⚠️ {token_name}"
                    
                    name_item = QTableWidgetItem(str(token_name))
                    
                    # 为风险代币设置特殊颜色
                    if token.get('isRiskToken', False):
                        name_item.setStyleSheet("color: #ff6b6b; font-weight: bold;")
                        name_item.setToolTip("⚠️ 风险代币：可能是空投代币或貔貅盘，请谨慎交易")
                    
                    self.balance_table.setItem(row, 0, name_item)
                    
                    # 符号
                    symbol = token.get('symbol', 'N/A')
                    symbol_item = QTableWidgetItem(str(symbol))
                    self.balance_table.setItem(row, 1, symbol_item)
                    
                    # 余额
                    balance = token.get('balance', '0')
                    # 格式化余额显示（如果数值太大则使用科学计数法）
                    try:
                        balance_float = float(balance)
                        if balance_float > 1000000:
                            balance_display = f"{balance_float:.2e}"
                        elif balance_float > 1000:
                            balance_display = f"{balance_float:.2f}"
                        else:
                            balance_display = f"{balance_float:.6f}".rstrip('0').rstrip('.')
                    except (ValueError, TypeError):
                        balance_display = str(balance)
                    
                    balance_item = QTableWidgetItem(balance_display)
                    self.balance_table.setItem(row, 2, balance_item)
                    
                    # 价格
                    price = token.get('tokenPrice', '0')
                    try:
                        price_float = float(price)
                        if price_float < 0.000001:
                            price_display = f"{price_float:.2e}"
                        else:
                            price_display = f"{price_float:.6f}".rstrip('0').rstrip('.')
                    except (ValueError, TypeError):
                        price_display = str(price)
                    
                    price_item = QTableWidgetItem(f"${price_display}")
                    self.balance_table.setItem(row, 3, price_item)
                    
                    # 价值
                    value = token.get('calculated_value', 0)
                    value_item = QTableWidgetItem(f"${value:.2f}")
                    value_item.setFont(QFont("Arial", 9, QFont.Bold))
                    self.balance_table.setItem(row, 4, value_item)
                    
                    # 合约地址
                    address = token.get('tokenContractAddress', 'N/A')
                    if address and len(address) > 20:
                        address = f"{address[:8]}...{address[-8:]}"
                    address_item = QTableWidgetItem(str(address))
                    self.balance_table.setItem(row, 5, address_item)
                    
                    # 操作按钮 - 卖出功能
                    self.create_sell_button_for_row(row, token)
                
                # 每批处理完后立即更新UI，避免界面卡顿
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()
            
            # 🚀 优化5: 重新启用UI更新和排序
            self.balance_table.setSortingEnabled(True)
            self.balance_table.setUpdatesEnabled(True)
            
            # 按价值排序（降序），但不需要再排序因为数据已经排序了
            # self.balance_table.sortItems(4, Qt.DescendingOrder)
            
            # 显示统计信息
            total_tokens = len(balances)
            displayed_tokens = len(display_tokens)
            total_value = sum(token.get('calculated_value', 0) for token in valuable_tokens)
            
            logger.info(f"完成更新：显示 {displayed_tokens}/{total_tokens} 个代币，总价值约 ${total_value:.2f}")
            
            # 更新状态信息
            status_parts = []
            status_parts.append(f"显示 {displayed_tokens}/{total_tokens} 个代币")
            
            # 添加过滤信息
            filters_applied = []
            if displayed_tokens < total_tokens:
                filters_applied.append("已过滤价值<$0.01的代币")
            
            if hide_risk_tokens and risk_tokens_count > 0:
                filters_applied.append(f"已隐藏{risk_tokens_count}个风险代币")
            elif not hide_risk_tokens and risk_tokens_count > 0:
                # 统计显示的风险代币数量
                displayed_risk_count = sum(1 for token in display_tokens if token.get('isRiskToken', False))
                if displayed_risk_count > 0:
                    filters_applied.append(f"含{displayed_risk_count}个风险代币⚠️")
            
            if filters_applied:
                status_parts.append(f"（{' | '.join(filters_applied)}）")
            
            self.status_label.setText(''.join(status_parts))
            
            # 更新Portfolio管理器数据
            if self.portfolio_monitor and balances:
                logger.info("PortfolioWidget: 更新Portfolio数据...")
                # 使用原始的balances数据更新，让PortfolioMonitor自己进行过滤
                self.portfolio_monitor.update_portfolio(balances)
            elif self.portfolio_monitor:
                logger.info("PortfolioWidget: 没有代币数据，停止Portfolio管理")
                self.portfolio_monitor.stop_management()
            
        except Exception as e:
            logger.error(f"更新代币余额表格失败: {e}", exc_info=True)
            self.balance_table.setRowCount(0)
            # 确保UI更新被重新启用
            self.balance_table.setUpdatesEnabled(True)
    
    @pyqtSlot(dict)
    def update_api_status(self, result: Dict):
        """更新API状态"""
        if result.get('success'):
            self.api_status_label.setText("API: ✅ 正常")
            self.api_status_label.setStyleSheet("""
                QLabel {
                    font-size: 10px;
                    color: #27ae60;
                    padding: 2px 0;
                    font-weight: bold;
                }
            """)
        else:
            error_msg = result.get('error', '未知错误')
            self.api_status_label.setText(f"API: ❌ 异常")
            self.api_status_label.setStyleSheet("""
                QLabel {
                    font-size: 10px;
                    color: #e74c3c;
                    padding: 2px 0;
                    font-weight: bold;
                }
            """)
            logger.warning(f"API状态异常: {error_msg}")
    
    @pyqtSlot(str)
    def handle_error(self, error_message: str):
        """处理错误"""
        self.status_label.setText(f"错误: {error_message}")
        QMessageBox.critical(self, "Portfolio错误", error_message)
    
    @pyqtSlot()
    def refresh_finished(self):
        """刷新完成"""
        self.refresh_button.setEnabled(True)
        self.refresh_button.setText("刷新")
        current_time = datetime.now().strftime("%H:%M:%S")
        self.last_update_label.setText(f"更新: {current_time}")
        
        # 检查当前钱包类型
        current_address = self.wallet_address_input.text().strip()
        is_default = (current_address == self.default_wallet_address)
        wallet_type = "默认钱包" if is_default else "自定义钱包"
        
        self.status_label.setText(f"数据已更新 ({wallet_type})")
    
    def create_sell_button_for_row(self, row: int, token: Dict):
        """为表格行创建卖出按钮"""
        # 只为有价值的代币创建卖出按钮（跳过SOL，因为可能需要作为Gas费）
        symbol = token.get('symbol', '').upper()
        value = token.get('calculated_value', 0)
        is_risk_token = token.get('isRiskToken', False)
        
        if value < 0.01:  # 只过滤价值过低的代币
            # 创建空的widget或提示信息
            widget = QWidget()
            layout = QHBoxLayout(widget)
            if is_risk_token:
                label = QLabel("⚠️风险代币")
                label.setStyleSheet("color: #ff6b6b; font-size: 10px; font-weight: bold;")
            else:
                label = QLabel("价值过低")
                label.setStyleSheet("color: gray; font-size: 10px;")
            layout.addWidget(label)
            layout.setContentsMargins(5, 2, 5, 2)
            self.balance_table.setCellWidget(row, 6, widget)
            return
        
        # 获取目标代币符号用于按钮显示
        target_token_info = self.get_target_token_info()
        target_symbol = target_token_info["symbol"]
        
        # 根据目标代币显示不同的图标
        if target_symbol == "SOL":
            target_icon = "🟣"
        else:  # USDC
            target_icon = "💚"
        
        # 创建卖出按钮（优化可读性）
        if is_risk_token:
            sell_button = QPushButton(f"⚠️ 卖→{target_icon}")
            sell_button.setMinimumWidth(110)
            sell_button.setMaximumWidth(120)
            sell_button.setMinimumHeight(28)
            sell_button.setMaximumHeight(32)
            sell_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #e74c3c, stop: 1 #c0392b);
                    color: white;
                    border: 1px solid #a93226;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #ec7063, stop: 1 #e74c3c);
                    border: 2px solid #ff6b6b;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #c0392b, stop: 1 #a93226);
                }
            """)
            sell_button.setToolTip(f"⚠️ 风险代币 - 卖出获得 {target_symbol}")
        else:
            sell_button = QPushButton(f"卖→{target_icon}")
            sell_button.setMinimumWidth(100)
            sell_button.setMaximumWidth(110)
            sell_button.setMinimumHeight(28)
            sell_button.setMaximumHeight(32)
            sell_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #27ae60, stop: 1 #229954);
                    color: white;
                    border: 1px solid #1e8449;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #58d68d, stop: 1 #27ae60);
                    border: 2px solid #58d68d;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #229954, stop: 1 #1e8449);
                }
            """)
            sell_button.setToolTip(f"卖出 → {target_symbol}")
        
        # 创建卖出比例菜单 - 针对流动性优化
        sell_menu = QMenu(sell_button)
        
        # 检查代币价值，为低价值代币提供更保守的选项
        token_value = token.get('calculated_value', 0)
        
        if token_value > 10:  # 高价值代币，提供全部选项
            percentages = [10, 25, 50, 75, 99, 100]
        elif token_value > 1:  # 中等价值代币，减少大比例选项
            percentages = [10, 25, 50, 75]
        else:  # 低价值代币，只提供小比例选项
            percentages = [10, 25, 50]
        
        for percentage in percentages:
            action = QAction(f"{percentage}%", sell_button)
            action.triggered.connect(
                lambda checked, p=percentage, t=token, r=row: self.sell_token(t, p, r)
            )
            sell_menu.addAction(action)
        
        # 为低流动性代币添加特殊选项
        if token_value < 1:
            sell_menu.addSeparator()
            micro_action = QAction("🔬 测试卖出 (1%)", sell_button)
            micro_action.triggered.connect(
                lambda checked, t=token, r=row: self.sell_token(t, 1, r)
            )
            sell_menu.addAction(micro_action)
        
        # 添加分隔符和智能卖出选项
        sell_menu.addSeparator()
        smart_action = QAction("🧠 智能卖出", sell_button)
        smart_action.triggered.connect(
            lambda checked, t=token, r=row: self.smart_sell_token(t, r)
        )
        sell_menu.addAction(smart_action)
        
        sell_button.setMenu(sell_menu)
        
        # 创建容器widget
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.addWidget(sell_button)
        layout.setContentsMargins(5, 2, 5, 2)
        
        self.balance_table.setCellWidget(row, 6, widget)
    
    def smart_sell_token(self, token: Dict, row: int):
        """智能卖出 - 根据代币价值和流动性自动选择最佳卖出策略"""
        try:
            symbol = token.get('symbol', 'Unknown')
            balance = token.get('balance', '0')
            token_value = token.get('calculated_value', 0)
            
            # 根据代币价值智能选择卖出比例
            if token_value > 10:
                suggested_percentage = 25  # 高价值代币保守卖出
                reason = "高价值代币，建议保守卖出25%"
            elif token_value > 1:
                suggested_percentage = 50  # 中等价值代币适中卖出
                reason = "中等价值代币，建议卖出50%"
            elif token_value > 0.1:
                suggested_percentage = 75  # 低价值代币可以多卖
                reason = "低价值代币，建议卖出75%"
            else:
                suggested_percentage = 10  # 极低价值代币测试性卖出
                reason = "极低价值代币，建议测试性卖出10%"
            
            # 显示智能建议对话框
            reply = QMessageBox.question(
                self,
                f"🧠 智能卖出建议 - {symbol}",
                f"代币价值: ${token_value:.6f}\n"
                f"建议卖出比例: {suggested_percentage}%\n"
                f"原因: {reason}\n\n"
                f"是否采用智能建议？\n"
                f"(点击 No 可手动选择比例)",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 采用智能建议
                self.execute_sell_operation(token, suggested_percentage, row, adaptive_slippage=True)
            elif reply == QMessageBox.No:
                # 手动选择
                self.sell_token(token, suggested_percentage, row)
            # Cancel就什么都不做
            
        except Exception as e:
            logger.error(f"智能卖出时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "智能卖出错误", f"智能卖出失败: {str(e)}")

    def sell_token(self, token: Dict, percentage: int, row: int):
        """执行代币卖出操作"""
        try:
            symbol = token.get('symbol', 'Unknown')
            balance = token.get('balance', '0')
            contract_address = token.get('tokenContractAddress', '')
            is_risk_token = token.get('isRiskToken', False)
            
            # 预估滑点
            token_value = token.get('calculated_value', 0)
            if token_value < 0.1:
                estimated_slippage = "15-20"
            elif token_value < 1.0:
                estimated_slippage = "10-15"
            elif token_value < 10.0:
                estimated_slippage = "5-10"
            else:
                estimated_slippage = "3-5"
            
            # 获取目标代币信息用于显示
            target_token_info = self.get_target_token_info()
            target_symbol = target_token_info["symbol"]
            
            # 显示确认对话框
            dialog = SellConfirmDialog(symbol, balance, percentage, self, estimated_slippage, is_risk_token, target_symbol)
            
            if dialog.exec_() == QDialog.Accepted:
                # 用户确认卖出，执行卖出操作
                self.execute_sell_operation(token, percentage, row, adaptive_slippage=True)
            
        except Exception as e:
            logger.error(f"卖出代币时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "卖出错误", f"卖出操作失败: {str(e)}")
    
    def execute_sell_operation(self, token: Dict, percentage: int, row: int, adaptive_slippage: bool = False):
        """执行实际的卖出操作"""
        try:
            symbol = token.get('symbol', 'Unknown')
            balance = float(token.get('balance', '0'))
            contract_address = token.get('tokenContractAddress', '')
            sell_amount = balance * (percentage / 100.0)
            
            # 获取钱包地址
            wallet_address = self.wallet_address_input.text().strip()
            if not wallet_address:
                QMessageBox.warning(self, "错误", "请先设置钱包地址")
                return
            
            # 获取链ID
            chain_id = self.chain_combo.currentData()
            
            # 获取目标代币信息
            target_token_info = self.get_target_token_info()
            target_address = target_token_info["address"]
            target_symbol = target_token_info["symbol"]
            target_decimals = target_token_info["decimals"]
            
            # 更新按钮状态
            self.update_sell_button_status(row, "卖出中...")
            
            # 处理SOL的特殊情况（使用系统程序地址）
            if symbol == 'SOL':
                from_token_address = "********************************"  # SOL的系统程序地址
            else:
                from_token_address = contract_address
                
            if not from_token_address:
                QMessageBox.warning(self, "错误", f"无法获取 {symbol} 的合约地址")
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 计算代币数量（使用rawBalance作为基准更准确）
            raw_balance = token.get('rawBalance', None)
            if raw_balance:
                # 使用rawBalance按比例计算
                raw_balance_int = int(raw_balance)
                sell_raw_amount_int = int(raw_balance_int * (percentage / 100.0))
                
                # 确保最小交换数量 - 对于价值很低的代币，至少保留一些精度
                if sell_raw_amount_int < 1000 and percentage < 100:
                    sell_raw_amount_int = max(1000, sell_raw_amount_int)
                    
                sell_raw_amount = str(sell_raw_amount_int)
                
                logger.info(f"卖出计算: {symbol} rawBalance={raw_balance}, percentage={percentage}%, sellAmount={sell_raw_amount}")
            else:
                # 回退方案：假设代币有9位小数（Solana标准）
                sell_raw_amount_int = int(sell_amount * (10 ** 9))
                sell_raw_amount = str(sell_raw_amount_int)
                logger.warning(f"未找到rawBalance，使用回退方案: {symbol} sellAmount={sell_raw_amount}")
            
            # 检查卖出数量是否有效
            if sell_raw_amount == '0':
                QMessageBox.warning(self, "卖出失败", f"卖出数量太小，无法执行交换\n\n建议：\n• 增加卖出比例\n• 选择价值更高的代币")
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 动态计算滑点
            if adaptive_slippage:
                # 根据代币价值和卖出金额智能调整滑点
                token_value = token.get('calculated_value', 0)
                if token_value < 0.1:
                    base_slippage = 15.0  # 极低价值代币，高滑点
                elif token_value < 1.0:
                    base_slippage = 10.0  # 低价值代币，较高滑点
                elif token_value < 10.0:
                    base_slippage = 5.0   # 中等价值代币，中等滑点
                else:
                    base_slippage = 3.0   # 高价值代币，较低滑点
                
                # 根据卖出比例进一步调整
                if percentage >= 75:
                    slippage = base_slippage + 2.0  # 大额卖出增加滑点
                elif percentage >= 50:
                    slippage = base_slippage + 1.0  # 中额卖出略增滑点
                else:
                    slippage = base_slippage
                
                # 限制滑点范围
                slippage = min(max(slippage, 1.0), 20.0)
                slippage_decimal = slippage / 100.0  # 转换为小数格式
                slippage_str = f"{slippage_decimal:.3f}"
                
                logger.info(f"自适应滑点: {symbol} 价值=${token_value:.6f}, 卖出{percentage}%, 滑点={slippage}% (API值:{slippage_str})")
            else:
                slippage_str = "0.050"  # 默认5%滑点转换为0.05
            
            # 先获取报价
            quote_request = QuoteRequest(
                chain_id=chain_id,
                from_token_address=from_token_address,
                to_token_address=target_address,
                amount=sell_raw_amount,
                slippage=slippage_str
            )
            
            quote_result = self.okx_client.get_quote(quote_request)
            
            if not quote_result.get('success'):
                error_msg = quote_result.get('error', '获取报价失败')
                QMessageBox.critical(self, "卖出失败", f"获取交换报价失败: {error_msg}")
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 从报价结果中获取预期输出和路由信息
            quote_data = quote_result.get('data', {})
            expected_output = quote_data.get('toTokenAmount', '0')
            
            # 检查报价是否有效
            if not expected_output or expected_output == '0':
                QMessageBox.critical(self, "卖出失败", "无法获取有效的交换报价，可能是流动性不足")
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 显示报价确认
            expected_amount = float(expected_output) / (10 ** target_decimals)
            confirm_msg = f"预计卖出 {sell_amount:.6f} {symbol}\n预计收到 {expected_amount:.6f} {target_symbol}\n\n确认执行交换？"
            
            reply = QMessageBox.question(
                self, 
                "确认交换", 
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 执行交换
            swap_request = SwapRequest(
                chain_id=chain_id,
                from_token_address=from_token_address,
                to_token_address=target_address,
                amount=sell_raw_amount,
                slippage=slippage_str,  # 使用计算出的滑点
                user_wallet_address=wallet_address
            )
            
            swap_result = self.okx_client.execute_swap(swap_request)
            
            if swap_result.get('success'):
                # 卖出成功
                swap_data = swap_result.get('data', {})
                # 尝试多个可能的交易ID字段名
                tx_hash = (swap_data.get('txId') or 
                          swap_data.get('txHash') or 
                          swap_data.get('transactionId') or 
                          swap_data.get('hash') or 
                          'Unknown')
                received_amount = swap_data.get('toTokenAmount', 'Unknown')
                
                QMessageBox.information(
                    self, 
                    "卖出成功", 
                    f"✅ 交换交易已提交！\n\n"
                    f"卖出: {sell_amount:.6f} {symbol}\n"
                    f"预计收到: {expected_amount:.6f} {target_symbol}\n"
                    f"交易ID: {tx_hash}\n\n"
                    f"🔗 请在钱包中确认交易并签名\n"
                    f"⏳ 交易确认需要几秒到几分钟时间"
                )
                
                # 5秒后自动刷新余额
                QTimer.singleShot(5000, self.refresh_portfolio)
                
            else:
                error_msg = swap_result.get('error', '交换失败')
                
                # 提供更详细的错误处理和建议
                if 'min_return must be greater than 0' in error_msg:
                    detailed_msg = (
                        f"❌ 代币交换失败: 最小返回值错误\n\n"
                        f"可能原因:\n"
                        f"• 该代币流动性不足\n"
                        f"• 价格波动过大\n"
                        f"• 交换数量太小\n\n"
                        f"建议解决方案:\n"
                        f"• 尝试减少卖出数量\n"
                        f"• 稍后再试\n"
                        f"• 检查该代币是否可正常交易"
                    )
                elif 'simulation failed' in error_msg.lower():
                    detailed_msg = (
                        f"❌ 交易模拟失败\n\n"
                        f"可能原因:\n"
                        f"• 钱包余额不足\n"
                        f"• SOL余额不足支付Gas费\n"
                        f"• 代币合约问题\n\n"
                        f"建议检查:\n"
                        f"• 确保钱包有足够SOL作为手续费\n"
                        f"• 确认代币可以正常交易"
                    )
                elif 'slippage' in error_msg.lower() or 'price impact' in error_msg.lower():
                    # slippage_str是小数格式，需要转换为百分比显示
                    current_slippage_decimal = float(slippage_str) if 'slippage_str' in locals() else 0.05
                    current_slippage = current_slippage_decimal * 100  # 转换为百分比
                    suggested_slippage = min(current_slippage + 5.0, 25.0)
                    
                    detailed_msg = (
                        f"❌ 滑点过大，交换失败\n\n"
                        f"当前滑点: {current_slippage}%\n"
                        f"建议滑点: {suggested_slippage}%\n\n"
                        f"解决方案:\n"
                        f"• 减少卖出数量（建议{max(10, percentage//2)}%）\n"
                        f"• 使用更高滑点重试\n"
                        f"• 等待市场波动减小后再试\n"
                        f"• 考虑分批卖出"
                    )
                    
                    # 提供重试选项
                    retry_reply = QMessageBox.question(
                        self,
                        "滑点过大",
                        detailed_msg + f"\n\n是否使用{suggested_slippage}%滑点重试？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if retry_reply == QMessageBox.Yes:
                        # 使用更高滑点重试
                        self.retry_sell_with_higher_slippage(token, percentage, row, suggested_slippage)
                        return
                else:
                    detailed_msg = f"❌ 代币交换失败\n\n错误详情: {error_msg}"
                
                QMessageBox.critical(self, "卖出失败", detailed_msg)
            
            # 恢复按钮状态 - 重新获取目标代币信息
            target_token_info = self.get_target_token_info()
            target_symbol = target_token_info["symbol"]
            target_icon = "🟣" if target_symbol == "SOL" else "💚"
            is_risk_token = token.get('isRiskToken', False)
            restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
            self.update_sell_button_status(row, restore_text)
            
        except Exception as e:
            logger.error(f"执行卖出操作时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "卖出错误", f"卖出操作失败: {str(e)}")
            # 恢复按钮状态 - 获取目标代币信息
            target_token_info = self.get_target_token_info()
            target_symbol = target_token_info["symbol"]
            target_icon = "🟣" if target_symbol == "SOL" else "💚"
            is_risk_token = token.get('isRiskToken', False)
            restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
            self.update_sell_button_status(row, restore_text)
    
    def retry_sell_with_higher_slippage(self, token: Dict, percentage: int, row: int, slippage: float):
        """使用更高滑点重试卖出"""
        try:
            symbol = token.get('symbol', 'Unknown')
            balance = float(token.get('balance', '0'))
            contract_address = token.get('tokenContractAddress', '')
            sell_amount = balance * (percentage / 100.0)
            
            # 获取钱包地址和链ID
            wallet_address = self.wallet_address_input.text().strip()
            chain_id = self.chain_combo.currentData()
            
            # 获取目标代币信息
            target_token_info = self.get_target_token_info()
            target_address = target_token_info["address"]
            target_symbol = target_token_info["symbol"]
            target_decimals = target_token_info["decimals"]
            
            # 处理SOL的特殊情况（使用系统程序地址）
            if symbol == 'SOL':
                from_token_address = "********************************"
            else:
                from_token_address = contract_address
            
            # 计算卖出数量
            raw_balance = token.get('rawBalance', None)
            if raw_balance:
                raw_balance_int = int(raw_balance)
                sell_raw_amount_int = int(raw_balance_int * (percentage / 100.0))
                sell_raw_amount = str(sell_raw_amount_int)
            else:
                sell_raw_amount = str(int(sell_amount * (10 ** 9)))
            
            # 更新按钮状态
            self.update_sell_button_status(row, f"重试中...")
            
            # 使用指定滑点获取报价 - 转换为小数格式
            slippage_decimal = slippage / 100.0  # 转换为小数格式
            slippage_str = f"{slippage_decimal:.3f}"
            quote_request = QuoteRequest(
                chain_id=chain_id,
                from_token_address=from_token_address,
                to_token_address=target_address,
                amount=sell_raw_amount,
                slippage=slippage_str
            )
            
            quote_result = self.okx_client.get_quote(quote_request)
            
            if not quote_result.get('success'):
                error_msg = quote_result.get('error', '获取报价失败')
                QMessageBox.critical(self, "重试失败", f"获取交换报价失败: {error_msg}")
                # 恢复按钮状态
                target_token_info = self.get_target_token_info()
                target_symbol = target_token_info["symbol"]
                target_icon = "🟣" if target_symbol == "SOL" else "💚"
                is_risk_token = token.get('isRiskToken', False)
                restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
                self.update_sell_button_status(row, restore_text)
                return
            
            # 执行交换
            swap_request = SwapRequest(
                chain_id=chain_id,
                from_token_address=from_token_address,
                to_token_address=target_address,
                amount=sell_raw_amount,
                slippage=slippage_str,
                user_wallet_address=wallet_address
            )
            
            swap_result = self.okx_client.execute_swap(swap_request)
            
            if swap_result.get('success'):
                # 成功
                swap_data = swap_result.get('data', {})
                # 尝试多个可能的交易ID字段名
                tx_hash = (swap_data.get('txId') or 
                          swap_data.get('txHash') or 
                          swap_data.get('transactionId') or 
                          swap_data.get('hash') or 
                          'Unknown')
                
                QMessageBox.information(
                    self, 
                    "重试成功", 
                    f"✅ 使用{slippage}%滑点重试成功！\n\n"
                    f"卖出: {sell_amount:.6f} {symbol} → {target_symbol}\n"
                    f"交易ID: {tx_hash}\n\n"
                    f"🔗 请在钱包中确认交易并签名"
                )
                
                # 5秒后自动刷新余额
                QTimer.singleShot(5000, self.refresh_portfolio)
            else:
                # 重试仍然失败
                error_msg = swap_result.get('error', '交换失败')
                QMessageBox.critical(
                    self, 
                    "重试失败", 
                    f"❌ 即使使用{slippage}%滑点仍然失败\n\n"
                    f"错误: {error_msg}\n\n"
                    f"建议:\n"
                    f"• 该代币可能流动性极差\n"
                    f"• 尝试更小的卖出数量\n"
                    f"• 考虑在其他DEX交易"
                )
            
            # 恢复按钮状态
            target_token_info = self.get_target_token_info()
            target_symbol = target_token_info["symbol"]
            target_icon = "🟣" if target_symbol == "SOL" else "💚"
            is_risk_token = token.get('isRiskToken', False)
            restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
            self.update_sell_button_status(row, restore_text)
            
        except Exception as e:
            logger.error(f"重试卖出时出错: {e}", exc_info=True)
            QMessageBox.critical(self, "重试错误", f"重试卖出失败: {str(e)}")
            # 恢复按钮状态
            target_token_info = self.get_target_token_info()
            target_symbol = target_token_info["symbol"]
            target_icon = "🟣" if target_symbol == "SOL" else "💚"
            is_risk_token = token.get('isRiskToken', False)
            restore_text = f"⚠️ 卖→{target_icon}" if is_risk_token else f"卖→{target_icon}"
            self.update_sell_button_status(row, restore_text)

    def update_sell_button_status(self, row: int, text: str):
        """更新卖出按钮状态"""
        try:
            widget = self.balance_table.cellWidget(row, 6)
            if widget:
                button = widget.findChild(QPushButton)
                if button:
                    button.setText(text)
                    # 根据文本判断是否启用按钮
                    is_enabled = ("→" in text or "⚠" in text) and "中..." not in text
                    button.setEnabled(is_enabled)
        except Exception as e:
            logger.debug(f"更新按钮状态失败: {e}")
    
    def update_sell_button_icon(self, row: int, target_icon: str):
        """更新卖出按钮图标"""
        try:
            widget = self.balance_table.cellWidget(row, 6)
            if widget:
                button = widget.findChild(QPushButton)
                if button:
                    current_text = button.text()
                    
                    # 跳过正在处理中的按钮
                    if "中..." in current_text:
                        return
                    
                    # 检查是否为风险代币（通过按钮文本判断）
                    is_risk_token = current_text.startswith("⚠")
                    
                    # 构建新的按钮文本
                    if is_risk_token:
                        new_text = f"⚠️ 卖→{target_icon}"
                        tooltip = f"⚠️ 风险代币 - 卖出获得 {target_icon.replace('🟣', 'SOL').replace('💚', 'USDC')}"
                    else:
                        new_text = f"卖→{target_icon}"
                        tooltip = f"卖出获得 {target_icon.replace('🟣', 'SOL').replace('💚', 'USDC')}"
                    
                    button.setText(new_text)
                    button.setToolTip(tooltip)
                    
                    logger.debug(f"更新第{row}行按钮图标: {current_text} -> {new_text}")
        except Exception as e:
            logger.debug(f"更新按钮图标失败: {e}")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止定时器
        if self.auto_refresh_timer.isActive():
            self.auto_refresh_timer.stop()
        
        # 🔥🔥 停止未处理信号相关定时器
        if hasattr(self, 'signals_cleanup_timer') and self.signals_cleanup_timer.isActive():
            self.signals_cleanup_timer.stop()
        if hasattr(self, 'signals_update_timer') and self.signals_update_timer.isActive():
            self.signals_update_timer.stop()
        
        # 停止数据线程
        if self.data_thread and self.data_thread.isRunning():
            self.data_thread.stop()
            self.data_thread.wait()
        
        # 停止Portfolio监控（新增）
        if self.portfolio_monitor:
            logger.info("PortfolioWidget: 停止Portfolio监控...")
            self.portfolio_monitor.stop_monitoring()
        
        # 🔥🔥 清理信号数据（参考live_trading_widget.py）
        # 注释掉清理逻辑：unique_signals 只应该增多不应该减少
        # if hasattr(self, 'unprocessed_signals'):
        #     self.unprocessed_signals.clear()
        # if hasattr(self, 'unique_signals'):
        #     self.unique_signals.clear()
        
        event.accept()

    # Portfolio状态信号处理方法
    @pyqtSlot(str, int, int)
    def on_portfolio_status_changed(self, status_message: str, eligible_count: int, total_count: int):
        """
        处理Portfolio状态变化

        参数:
            status_message (str): 状态消息
            eligible_count (int): 符合条件的代币数量
            total_count (int): 总代币数量
        """
        try:
            # 更新状态显示
            logger.debug(f"PortfolioWidget: Portfolio状态 - {status_message} ({eligible_count}/{total_count})")

        except Exception as e:
            logger.error(f"PortfolioWidget: 处理Portfolio状态变化时出错: {e}")
    
    def get_portfolio_monitor(self) -> Optional[PortfolioMonitor]:
        """
        获取Portfolio管理器实例

        返回:
            Optional[PortfolioMonitor]: Portfolio管理器实例，如果不可用则返回None
        """
        return self.portfolio_monitor
    
    # 🔥🔥 新增：创建未处理信号面板
    def create_unprocessed_signals_panel(self) -> QWidget:
        """创建最近1小时未处理信号面板"""
        group = QGroupBox("🔔 最近1小时未处理信号")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                color: #ecf0f1;
                border: 1px solid #1a252f;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3a4a5c, stop: 1 #2c3e50);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #f39c12;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 8, 5, 5)
        
        self.portfolio_unprocessed_signals_table = QTableWidget()
        self.portfolio_unprocessed_signals_table.setColumnCount(5)
        self.portfolio_unprocessed_signals_table.setHorizontalHeaderLabels([
            "时间", "代币", "信号", "价格", "策略"
        ])
        
        self.portfolio_unprocessed_signals_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c3e50;
                border: none;
                gridline-color: #34495e;
                font-size: 9px;
                color: #ecf0f1;
                selection-background-color: #e67e22;
            }
            QTableWidget::item {
                padding: 2px 4px;
                border-bottom: 1px solid #34495e;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4a5568, stop: 1 #3a4a5c);
                border: none;
                border-bottom: 1px solid #1a252f;
                border-right: 1px solid #2c3e50;
                padding: 3px 5px;
                font-size: 9px;
                font-weight: bold;
                color: #ecf0f1;
            }
        """)
        
        header = self.portfolio_unprocessed_signals_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Fixed)
        self.portfolio_unprocessed_signals_table.setColumnWidth(0, 50)   # 时间
        self.portfolio_unprocessed_signals_table.setColumnWidth(1, 60)   # 代币
        self.portfolio_unprocessed_signals_table.setColumnWidth(2, 40)   # 信号
        self.portfolio_unprocessed_signals_table.setColumnWidth(3, 70)   # 价格
        self.portfolio_unprocessed_signals_table.setColumnWidth(4, 80)   # 策略
        
        self.portfolio_unprocessed_signals_table.setAlternatingRowColors(False)
        self.portfolio_unprocessed_signals_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.portfolio_unprocessed_signals_table.setSelectionMode(QTableWidget.SingleSelection)
        self.portfolio_unprocessed_signals_table.setSortingEnabled(True)
        self.portfolio_unprocessed_signals_table.setShowGrid(False)
        self.portfolio_unprocessed_signals_table.verticalHeader().setVisible(False)
        
        # 🔥 移除所有高度限制，让表格撑满剩余空间
        # self.portfolio_unprocessed_signals_table.setMinimumHeight(180)  # 移除最小高度限制
        # self.portfolio_unprocessed_signals_table.setMaximumHeight(350)  # 移除最大高度限制
        self.portfolio_unprocessed_signals_table.setSizePolicy(
            QSizePolicy.Expanding, QSizePolicy.Expanding
        )  # 允许表格在两个方向上自由扩展到最大可用空间
        
        layout.addWidget(self.portfolio_unprocessed_signals_table)
        group.setLayout(layout)
        return group
    
    # 🔥🔥 新增：更新未处理信号表格的方法
    def update_unprocessed_signals_table(self):
        """更新最近1小时未处理信号表格"""
        try:
            self.portfolio_unprocessed_signals_table.setUpdatesEnabled(False)
            self.portfolio_unprocessed_signals_table.setSortingEnabled(False)

            all_unprocessed_signals = self.get_unprocessed_signals()
            
            current_unix_time = time.time()
            onehour_ago_unix = current_unix_time - (60 * 60)  # 1小时前
            
            recent_signals = [
                s for s in all_unprocessed_signals 
                if s.get('timestamp', 0) >= onehour_ago_unix
            ]
            
            # 按时间戳降序排序（最新的在前）
            recent_signals.sort(key=lambda x: x.get('timestamp', 0), reverse=True)

            self.portfolio_unprocessed_signals_table.setRowCount(len(recent_signals))
            
            for row, signal_data in enumerate(recent_signals):
                timestamp = signal_data.get('timestamp', 0)
                
                # 时间列: HH:MM:SS
                dt_object = datetime.fromtimestamp(timestamp)
                time_str = dt_object.strftime('%H:%M:%S')
                time_item = QTableWidgetItem(time_str)
                time_item.setTextAlignment(Qt.AlignCenter)
                self.portfolio_unprocessed_signals_table.setItem(row, 0, time_item)
                
                # 代币列
                token_symbol = signal_data.get('token_symbol', 'N/A')
                token_item = QTableWidgetItem(token_symbol)
                self.portfolio_unprocessed_signals_table.setItem(row, 1, token_item)
                
                # 信号列
                signal_type = signal_data.get('signal_type', 'N/A').lower()
                signal_display = signal_type.capitalize()
                signal_item = QTableWidgetItem(signal_display)
                signal_item.setTextAlignment(Qt.AlignCenter)
                font = signal_item.font()
                font.setBold(True)
                signal_item.setFont(font)
                if signal_type == 'buy':
                    signal_item.setForeground(QColor('#27ae60'))  # Green for buy
                elif signal_type == 'sell':
                    signal_item.setForeground(QColor('#e74c3c'))  # Red for sell
                else:
                    signal_item.setForeground(QColor('#bdc3c7'))  # Gray for others
                self.portfolio_unprocessed_signals_table.setItem(row, 2, signal_item)
                
                # 价格列
                price = signal_data.get('price', 0.0)
                price_str = f"${price:.6f}"
                price_item = NumericTableWidgetItem(price_str, float(price))
                self.portfolio_unprocessed_signals_table.setItem(row, 3, price_item)
                
                # 策略列
                strategy_name = signal_data.get('strategy_name', 'N/A')
                strategy_item = QTableWidgetItem(strategy_name)
                self.portfolio_unprocessed_signals_table.setItem(row, 4, strategy_item)

        except Exception as e:
            logger.error(f"更新Portfolio未处理信号表格失败: {e}", exc_info=True)
        finally:
            self.portfolio_unprocessed_signals_table.setSortingEnabled(True)
            self.portfolio_unprocessed_signals_table.setUpdatesEnabled(True)
    
    # 🔥🔥 新增：获取未处理信号的方法
    def get_unprocessed_signals(self) -> List[Dict]:
        """获取所有未处理的Portfolio信号，按时间戳降序排序"""
        try:
            # 返回按时间戳降序排序的未处理信号列表
            return sorted(self.unprocessed_signals, key=lambda x: x.get('timestamp', 0), reverse=True)
        except Exception as e:
            logger.error(f"获取未处理信号失败: {e}")
            return []
    
    # 🔥🔥 新增：添加信号到未处理列表的方法
    def add_signal_to_unprocessed(self, token_symbol: str, signal_type: str, price: float, timestamp: int, strategy_name: str, token_address: str = "", chart_index: int = 0):
        """
        添加Portfolio信号到未处理列表
        
        参数:
            token_symbol (str): 代币符号
            signal_type (str): 信号类型
            price (float): 价格
            timestamp (int): 时间戳
            strategy_name (str): 策略名称
            token_address (str): 代币地址
            chart_index (int): 图表索引
        """
        try:
            # 🔥🔥 唯一信号存储 - 防止重复信号（参考live_trading_widget.py）
            # 使用截断到分钟的timestamp，确保标识符稳定
            timestamp_minute = (timestamp // 60) * 60  # 截断到分钟
            unique_key = f"{token_symbol}_{strategy_name}_{timestamp_minute}"
            
            # 检查是否为新信号
            is_new_signal = False
            if unique_key not in self.unique_signals:
                # 全新的信号
                is_new_signal = True
                logger.debug(f"PortfolioWidget: 检测到新信号 - {token_symbol} {signal_type} @ {timestamp}")
            else:
                # 已存在的信号，检查是否有变化
                existing_signal = self.unique_signals[unique_key]
                if (existing_signal['signal_type'] != signal_type or 
                    abs(existing_signal['price'] - price) > 0.000001):  # 价格有显著变化
                    is_new_signal = True
                    logger.debug(f"PortfolioWidget: 信号有变化 - {token_symbol} {signal_type} @ {timestamp}")
                else:
                    logger.debug(f"PortfolioWidget: 重复信号已忽略 - {token_symbol} {signal_type} @ {timestamp}")
            
            # 只有新信号才添加到未处理列表
            if is_new_signal:
                # 存储新的唯一信号
                self.unique_signals[unique_key] = {
                    'signal_type': signal_type,
                    'price': price,
                    'timestamp': timestamp,
                    'token_address': token_address,
                    'token_symbol': token_symbol,
                    'strategy_name': strategy_name,
                    'chart_index': chart_index,  # 🔥 保留chart_index信息用于显示
                    'processed': False,  # 标记是否已处理（用于交易执行）
                    'created_time': time.time()  # 记录创建时间
                }
                
                # 添加到未处理信号列表
                signal_data = {
                    'token_symbol': token_symbol,
                    'signal_type': signal_type,
                    'price': price,
                    'timestamp': timestamp,
                    'strategy_name': strategy_name,
                    'token_address': token_address,
                    'chart_index': chart_index,
                    'source': 'Portfolio',  # 标识来源为Portfolio监控
                    'unique_key': unique_key  # 添加唯一标识符用于后续管理
                }
                
                self.unprocessed_signals.append(signal_data)
                
                # 限制列表大小，保留最近100个信号
                if len(self.unprocessed_signals) > 100:
                    self.unprocessed_signals = self.unprocessed_signals[-100:]
                
                logger.info(f"PortfolioWidget: 添加新的未处理信号 - {token_symbol} {signal_type} @ ${price:.6f}")
            
        except Exception as e:
            logger.error(f"PortfolioWidget: 添加未处理信号时出错: {e}")