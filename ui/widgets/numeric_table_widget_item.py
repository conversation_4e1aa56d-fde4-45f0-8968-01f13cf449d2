from PyQt5.QtWidgets import QTableWidgetItem

class NumericTableWidgetItem(QTableWidgetItem):
    """支持数值排序的表格项"""
    
    def __init__(self, text: str, numeric_value: float = 0.0):
        super().__init__(text)
        self.numeric_value = numeric_value
    
    def __lt__(self, other):
        """重写小于比较，用于排序"""
        if isinstance(other, NumericTableWidgetItem):
            # Ensure both values are comparable as floats
            try:
                val_self = float(self.numeric_value)
                val_other = float(other.numeric_value)
                return val_self < val_other
            except (ValueError, TypeError):
                # Fallback to string comparison if conversion to float fails for either
                return super().__lt__(other)
        return super().__lt__(other) 