"""
卖出确认对话框组件
用于确认代币卖出操作，提供卖出数量选择和风险提示
"""

import logging
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QDialogButtonBox, QFrame, QSizePolicy, QFormLayout
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap

logger = logging.getLogger(__name__)


class SellConfirmDialog(QDialog):
    """卖出确认对话框"""
    
    def __init__(self, token_symbol: str, token_balance: str, sell_percentage: int, parent=None, estimated_slippage: str = "", is_risk_token: bool = False, target_symbol: str = "SOL"):
        super().__init__(parent)
        self.token_symbol = token_symbol
        self.token_balance = float(token_balance)
        self.sell_percentage = sell_percentage
        self.sell_amount = self.token_balance * (sell_percentage / 100.0)
        self.estimated_slippage = estimated_slippage
        self.is_risk_token = is_risk_token
        self.target_symbol = target_symbol
        
        title_prefix = "⚠️ 风险代币 - " if is_risk_token else ""
        self.setWindowTitle(f"{title_prefix}确认卖出 {token_symbol}")
        self.setModal(True)
        self.resize(400, 300 if is_risk_token else 250)
        
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(f"卖出 {self.token_symbol} → {self.target_symbol}")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 详细信息
        info_layout = QFormLayout()
        info_layout.addRow("代币:", QLabel(self.token_symbol))
        info_layout.addRow("目标代币:", QLabel(f"🎯 {self.target_symbol}"))
        info_layout.addRow("当前余额:", QLabel(f"{self.token_balance:.6f}"))
        info_layout.addRow("卖出比例:", QLabel(f"{self.sell_percentage}%"))
        info_layout.addRow("卖出数量:", QLabel(f"{self.sell_amount:.6f}"))
        info_layout.addRow("剩余数量:", QLabel(f"{self.token_balance - self.sell_amount:.6f}"))
        
        # 显示滑点信息（如果有）
        if self.estimated_slippage:
            slippage_label = QLabel(f"{self.estimated_slippage}%")
            slippage_label.setStyleSheet("color: orange; font-weight: bold;")
            info_layout.addRow("预计滑点:", slippage_label)
        
        # info_widget = QWidget() # QFormLayout can be directly added to QVBoxLayout if it's the only thing in a group
        # info_widget.setLayout(info_layout)
        # layout.addWidget(info_widget)
        layout.addLayout(info_layout) # Add QFormLayout directly
        
        # 警告信息
        warning_text = "⚠️ 请确认卖出操作。此操作不可撤销！"
        
        if self.is_risk_token:
            warning_text = "🚨 风险代币警告！\n\n" + warning_text
            warning_text += "\n\n⚠️ 此代币被标记为风险代币，可能是：\n• 空投代币（价值可能为零）\n• 貔貅盘代币（可能无法正常交易）\n• 存在其他风险因素\n\n请谨慎考虑是否继续交易！"
        
        if self.token_symbol.upper() == 'SOL':
            warning_text += "\n\n💰 注意：SOL用作交易手续费，请确保保留足够的SOL用于后续交易！"
        
        warning_label = QLabel(warning_text)
        if self.is_risk_token:
            warning_label.setStyleSheet("color: #ff1744; font-weight: bold; padding: 10px; background-color: #ffebee; border: 2px solid #ff5252; border-radius: 5px;")
        else:
            warning_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
        warning_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning_label)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout) 