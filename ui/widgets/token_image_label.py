"""
代币图片标签组件
用于显示代币图片，支持悬停预览和点击查看大图功能
"""

import logging
from PyQt5.QtWidgets import QLabel, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QApplication
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
from PyQt5.QtGui import QPixmap, QFont, QPainter, QPen, QBrush, QColor, QCursor, QPixmapCache
from PyQt5.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply

logger = logging.getLogger(__name__)


class TokenImageLabel(QLabel):
    """代币图片标签，支持网络图片加载和点击放大"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(32, 32)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 1px solid #34495e;
                border-radius: 4px;
                background-color: #2c3e50;
            }
            QLabel:hover {
                border: 2px solid #3498db;
            }
        """)
        
        # 网络管理器
        self.network_manager = QNetworkAccessManager()
        self.network_manager.finished.connect(self.on_image_loaded)
        
        # 大图对话框相关
        self.large_image_dialog = None
        self.large_network_manager = QNetworkAccessManager()
        self.large_network_manager.finished.connect(self.on_large_image_loaded)
        
        # 当前图片信息
        self.current_image_url = None
        self.current_symbol = None
        
        # 设置默认占位符
        self.set_placeholder()
    
    def set_placeholder(self):
        """设置默认占位符"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor("#34495e"))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制圆形边框
        pen = QPen(QColor("#bdc3c7"))
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawEllipse(1, 1, 30, 30)
        
        # 绘制问号
        painter.setPen(QColor("#bdc3c7"))
        font = QFont("Arial", 12, QFont.Bold)
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "?")
        
        painter.end()
        self.setPixmap(pixmap)
    
    def load_token_image(self, image_url: str, symbol: str):
        """加载代币图片"""
        if not image_url or image_url == self.current_image_url:
            return
            
        self.current_image_url = image_url
        self.current_symbol = symbol
        
        cache_key = f"token_icon_{image_url}"
        cached_pixmap = QPixmapCache.find(cache_key)
        if cached_pixmap and not cached_pixmap.isNull():
            scaled_pixmap = cached_pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            return
        
        try:
            request = QNetworkRequest(QUrl(image_url))
            request.setHeader(QNetworkRequest.UserAgentHeader, "TrendTrader/1.0")
            self.network_manager.get(request)
        except Exception as e:
            logger.error(f"加载图片失败 {image_url}: {e}")
            self.set_placeholder_with_symbol(symbol)
    
    def set_placeholder_with_symbol(self, symbol: str):
        """设置带符号的占位符"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor("#34495e"))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制圆形边框
        pen = QPen(QColor("#bdc3c7"))
        pen.setWidth(1)
        painter.setPen(pen)
        painter.drawEllipse(1, 1, 30, 30)
        
        # 绘制符号首字母
        painter.setPen(QColor("#bdc3c7"))
        font = QFont("Arial", 10, QFont.Bold)
        painter.setFont(font)
        text = symbol[:2].upper() if symbol and len(symbol) >= 2 else (symbol[0].upper() if symbol else "?")
        painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
        
        painter.end()
        self.setPixmap(pixmap)
    
    def on_image_loaded(self, reply: QNetworkReply):
        """处理图片加载完成"""
        try:
            if reply.error() == QNetworkReply.NoError:
                data = reply.readAll()
                pixmap = QPixmap()
                if pixmap.loadFromData(data):
                    # 创建圆形图片
                    scaled_pixmap = self.create_scaled_pixmap(pixmap, 32)
                    self.setPixmap(scaled_pixmap)
                    cache_key = f"token_icon_{self.current_image_url}"
                    QPixmapCache.insert(cache_key, scaled_pixmap)
                else:
                    self.set_placeholder_with_symbol(self.current_symbol or "")
            else:
                logger.warning(f"图片加载失败: {reply.errorString()}")
                self.set_placeholder_with_symbol(self.current_symbol or "")
        except Exception as e:
            logger.error(f"处理图片数据失败: {e}")
            self.set_placeholder_with_symbol(self.current_symbol or "")
        finally:
            reply.deleteLater()
    
    def create_scaled_pixmap(self, pixmap: QPixmap, size: int) -> QPixmap:
        """创建缩放的圆形图片"""
        scaled = pixmap.scaled(size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        # 创建圆形遮罩
        result = QPixmap(size, size)
        result.fill(Qt.transparent)
        
        painter = QPainter(result)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QBrush(scaled))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, size, size)
        painter.end()
        
        return result
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 4px;
                background-color: #2c3e50;
            }
        """)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.setStyleSheet("""
            QLabel {
                border: 1px solid #34495e;
                border-radius: 4px;
                background-color: #2c3e50;
            }
        """)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标点击事件 - 显示大图"""
        if event.button() == Qt.LeftButton and self.current_image_url:
            self.show_large_image()
        super().mousePressEvent(event)
    
    def show_large_image(self):
        """显示大图对话框"""
        if not self.current_image_url:
            return
            
        try:
            # 创建对话框
            self.large_image_dialog = QDialog(self)
            self.large_image_dialog.setWindowTitle(f"代币图片 - {self.current_symbol or 'Unknown'}")
            self.large_image_dialog.setModal(True)
            self.large_image_dialog.resize(400, 400)
            
            layout = QVBoxLayout(self.large_image_dialog)
            
            # 图片标签
            self.large_image_label = QLabel()
            self.large_image_label.setAlignment(Qt.AlignCenter)
            self.large_image_label.setMinimumSize(300, 300)
            self.large_image_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    background-color: white;
                }
            """)
            layout.addWidget(self.large_image_label)
            
            # 按钮
            button_layout = QHBoxLayout()
            close_button = QPushButton("关闭")
            close_button.clicked.connect(self.large_image_dialog.close)
            button_layout.addStretch()
            button_layout.addWidget(close_button)
            layout.addLayout(button_layout)
            
            # 加载大图
            self.load_large_image()
            
            # 显示对话框
            self.large_image_dialog.show()
            
        except Exception as e:
            logger.error(f"显示大图失败: {e}")
    
    def load_large_image(self):
        """加载大图"""
        try:
            # 显示加载提示
            self.large_image_label.setText("正在加载图片...")
            
            request = QNetworkRequest(QUrl(self.current_image_url))
            request.setHeader(QNetworkRequest.UserAgentHeader, "TrendTrader/1.0")
            self.large_network_manager.get(request)
        except Exception as e:
            logger.error(f"加载大图失败: {e}")
            self.large_image_label.setText("加载失败")
    
    def on_large_image_loaded(self, reply: QNetworkReply, manager: QNetworkAccessManager):
        """处理大图加载完成"""
        try:
            if not self.large_image_dialog:
                return
                
            if reply.error() == QNetworkReply.NoError:
                data = reply.readAll()
                pixmap = QPixmap()
                if pixmap.loadFromData(data):
                    # 缩放图片以适应显示区域
                    label_size = self.large_image_label.size()
                    scaled_pixmap = pixmap.scaled(
                        label_size.width() - 20, 
                        label_size.height() - 20, 
                        Qt.KeepAspectRatio, 
                        Qt.SmoothTransformation
                    )
                    self.large_image_label.setPixmap(scaled_pixmap)
                else:
                    self.large_image_label.setText("图片格式不支持")
            else:
                logger.warning(f"大图加载失败: {reply.errorString()}")
                self.large_image_label.setText(f"加载失败: {reply.errorString()}")
        except Exception as e:
            logger.error(f"处理大图数据失败: {e}")
            if self.large_image_dialog:
                self.large_image_label.setText("处理图片失败")
        finally:
            reply.deleteLater()
            manager.deleteLater() 