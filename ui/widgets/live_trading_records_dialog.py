"""
实盘交易记录对话框组件
用于显示和管理实盘交易记录，包括状态监控和统计信息
"""

import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QSizePolicy, QGroupBox, QTextEdit, QSpacerItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QColor

logger = logging.getLogger(__name__)


class LiveTradingRecordsDialog(QDialog):
    """实盘交易记录对话框"""
    
    record_selected = pyqtSignal(dict)  # 当选择记录时发出信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("实盘交易记录")
        self.setModal(False)
        self.resize(1000, 700)
        
        # 交易记录数据
        self.live_records = []
        self.is_monitoring = False
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_display)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 顶部状态栏
        status_layout = QHBoxLayout()
        
        # 监控状态指示器
        self.status_indicator = QLabel("●")
        self.status_indicator.setFont(QFont("Arial", 16))
        self.status_indicator.setStyleSheet("color: #ff4444;")
        status_layout.addWidget(self.status_indicator)
        
        self.status_label = QLabel("监控已停止")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 时间标签
        self.time_label = QLabel()
        self.time_label.setFont(QFont("Arial", 10))
        self.time_label.setStyleSheet("color: #666;")
        status_layout.addWidget(self.time_label)
        
        layout.addLayout(status_layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 统计信息面板
        stats_group = QGroupBox("交易统计")
        stats_layout = QHBoxLayout(stats_group)
        
        # 总记录数
        self.total_records_label = QLabel("总记录: 0")
        self.total_records_label.setFont(QFont("Arial", 11))
        stats_layout.addWidget(self.total_records_label)
        
        # 买入信号
        self.buy_signals_label = QLabel("买入信号: 0")
        self.buy_signals_label.setFont(QFont("Arial", 11))
        self.buy_signals_label.setStyleSheet("color: #4CAF50;")
        stats_layout.addWidget(self.buy_signals_label)
        
        # 卖出信号
        self.sell_signals_label = QLabel("卖出信号: 0")
        self.sell_signals_label.setFont(QFont("Arial", 11))
        self.sell_signals_label.setStyleSheet("color: #ff6b35;")
        stats_layout.addWidget(self.sell_signals_label)
        
        # 今日记录
        self.today_records_label = QLabel("今日: 0")
        self.today_records_label.setFont(QFont("Arial", 11))
        self.today_records_label.setStyleSheet("color: #2196F3;")
        stats_layout.addWidget(self.today_records_label)
        
        stats_layout.addStretch()
        
        # 清空按钮
        self.clear_button = QPushButton("清空记录")
        self.clear_button.setFixedSize(80, 30)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        self.clear_button.clicked.connect(self.clear_records)
        stats_layout.addWidget(self.clear_button)
        
        layout.addWidget(stats_group)
        
        # 交易记录表格
        records_group = QGroupBox("交易记录")
        records_layout = QVBoxLayout(records_group)
        
        self.records_table = QTableWidget()
        self.records_table.setColumnCount(8)
        self.records_table.setHorizontalHeaderLabels([
            "时间", "代币", "类型", "价格", "策略", "置信度", "市值", "状态"
        ])
        
        # 设置表格样式
        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.records_table.setSelectionMode(QTableWidget.SingleSelection)
        self.records_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.records_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 时间
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 代币
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 类型
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # 价格
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 策略
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 置信度
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 市值
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 状态
        
        self.records_table.setColumnWidth(0, 120)  # 时间
        self.records_table.setColumnWidth(2, 60)   # 类型
        self.records_table.setColumnWidth(3, 80)   # 价格
        self.records_table.setColumnWidth(5, 70)   # 置信度
        self.records_table.setColumnWidth(6, 80)   # 市值
        self.records_table.setColumnWidth(7, 80)   # 状态
        
        # 连接选择信号
        self.records_table.itemSelectionChanged.connect(self.on_record_selected)
        
        records_layout.addWidget(self.records_table)
        layout.addWidget(records_group)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.setFixedSize(80, 35)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
            QPushButton:pressed {
                background-color: #424242;
            }
        """)
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # 启动状态定时器
        self.status_timer.start(1000)  # 每秒更新一次
        self.update_status_display()
    
    def set_monitoring_status(self, is_active: bool):
        """设置监控状态"""
        self.is_monitoring = is_active
        self.update_status_display()
    
    def update_status_display(self):
        """更新状态显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"当前时间: {current_time}")
        
        if self.is_monitoring:
            self.status_indicator.setStyleSheet("color: #4CAF50;")  # 绿色
            self.status_label.setText("实盘监控中...")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        else:
            self.status_indicator.setStyleSheet("color: #ff4444;")  # 红色
            self.status_label.setText("监控已停止")
            self.status_label.setStyleSheet("color: #ff4444; font-weight: bold;")
    
    def add_live_record(self, record_data: dict):
        """添加实盘交易记录"""
        try:
            # 添加时间戳
            record_data['timestamp'] = datetime.now()
            self.live_records.append(record_data)
            
            # 更新显示
            self.update_records_display()
            self.update_statistics()
            
            logger.info(f"添加实盘交易记录: {record_data.get('symbol')} - {record_data.get('type')}")
            
        except Exception as e:
            logger.error(f"添加实盘交易记录失败: {e}")
    
    def update_records_display(self):
        """更新记录显示"""
        try:
            self.records_table.setRowCount(len(self.live_records))
            
            for row, record in enumerate(self.live_records):
                # 时间
                time_str = record.get('timestamp', datetime.now()).strftime("%H:%M:%S")
                self.records_table.setItem(row, 0, QTableWidgetItem(time_str))
                
                # 代币
                symbol = record.get('symbol', 'Unknown')
                self.records_table.setItem(row, 1, QTableWidgetItem(symbol))
                
                # 类型
                signal_type = record.get('type', 'Unknown')
                type_item = QTableWidgetItem(signal_type)
                if signal_type == '买入':
                    type_item.setBackground(QColor(76, 175, 80, 50))  # 浅绿色
                elif signal_type == '卖出':
                    type_item.setBackground(QColor(255, 107, 53, 50))  # 浅橙色
                self.records_table.setItem(row, 2, type_item)
                
                # 价格
                price = record.get('price', 0)
                if isinstance(price, (int, float)):
                    price_str = f"${price:.6f}" if price < 1 else f"${price:.2f}"
                else:
                    price_str = str(price)
                self.records_table.setItem(row, 3, QTableWidgetItem(price_str))
                
                # 策略
                strategy = record.get('strategy', 'Unknown')
                self.records_table.setItem(row, 4, QTableWidgetItem(strategy))
                
                # 置信度
                confidence = record.get('confidence', 0)
                if isinstance(confidence, (int, float)):
                    confidence_str = f"{confidence:.1f}%"
                else:
                    confidence_str = str(confidence)
                self.records_table.setItem(row, 5, QTableWidgetItem(confidence_str))
                
                # 市值
                market_cap = record.get('market_cap', 0)
                if isinstance(market_cap, (int, float)) and market_cap > 0:
                    if market_cap >= 1_000_000:
                        market_cap_str = f"${market_cap/1_000_000:.1f}M"
                    elif market_cap >= 1_000:
                        market_cap_str = f"${market_cap/1_000:.1f}K"
                    else:
                        market_cap_str = f"${market_cap:.0f}"
                else:
                    market_cap_str = "N/A"
                self.records_table.setItem(row, 6, QTableWidgetItem(market_cap_str))
                
                # 状态
                status = "待执行"  # 默认状态
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(255, 193, 7, 50))  # 浅黄色
                self.records_table.setItem(row, 7, status_item)
            
            # 滚动到最新记录
            if self.live_records:
                self.records_table.scrollToBottom()
                
        except Exception as e:
            logger.error(f"更新记录显示失败: {e}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            total_count = len(self.live_records)
            self.total_records_label.setText(f"总记录: {total_count}")
            
            # 统计买入和卖出信号
            buy_count = sum(1 for record in self.live_records if record.get('type') == '买入')
            sell_count = sum(1 for record in self.live_records if record.get('type') == '卖出')
            
            self.buy_signals_label.setText(f"买入信号: {buy_count}")
            self.sell_signals_label.setText(f"卖出信号: {sell_count}")
            
            # 统计今日记录
            today = datetime.now().date()
            today_count = sum(1 for record in self.live_records 
                            if record.get('timestamp', datetime.now()).date() == today)
            self.today_records_label.setText(f"今日: {today_count}")
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def clear_records(self):
        """清空记录"""
        try:
            self.live_records.clear()
            self.records_table.setRowCount(0)
            self.update_statistics()
            logger.info("实盘交易记录已清空")
        except Exception as e:
            logger.error(f"清空记录失败: {e}")
    
    def on_record_selected(self):
        """处理记录选择事件"""
        try:
            current_row = self.records_table.currentRow()
            if 0 <= current_row < len(self.live_records):
                selected_record = self.live_records[current_row]
                self.record_selected.emit(selected_record)
                logger.debug(f"选择了记录: {selected_record.get('symbol')} - {selected_record.get('type')}")
        except Exception as e:
            logger.error(f"处理记录选择失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止定时器
        if self.status_timer.isActive():
            self.status_timer.stop()
        super().closeEvent(event) 