from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

class TokenInfoWidget(QWidget):
    """自定义代币信息组件 - 上下显示符号和代币名称"""
    
    def __init__(self, symbol: str = "", name: str = "", token_data: dict = None, parent=None):
        super().__init__(parent)
        self.symbol = symbol
        self.name = name
        self.token_data = token_data  # 存储完整的代币数据
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(1)
        
        # 符号标签 - 加粗、稍大字体
        self.symbol_label = QLabel(self.symbol)
        self.symbol_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
                font-weight: bold;
                padding: 0;
                margin: 0;
            }
        """)
        self.symbol_label.setAlignment(Qt.AlignCenter)
        
        # 代币名称标签 - 较小字体
        self.name_label = QLabel(self.name)
        self.name_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 9px;
                font-weight: normal;
                padding: 0;
                margin: 0;
            }
        """)
        self.name_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(self.symbol_label)
        layout.addWidget(self.name_label)
    
    def update_info(self, symbol: str, name: str, token_data: dict = None):
        """更新显示的代币信息"""
        self.symbol = symbol
        self.name = name
        self.token_data = token_data
        self.symbol_label.setText(symbol)
        self.name_label.setText(name) 