import logging
import requests
import os
from PyQt5.QtCore import QThread, pyqtSignal

logger = logging.getLogger(__name__)

class TrendDataThread(QThread):
    """趋势币数据获取线程"""
    
    data_updated = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # Consider making the API URL configurable or passed in
        self.api_url = "https://token-news-roan.vercel.app/api/tokens/aggregated-data" 
        self.should_stop = False
    
    def run(self):
        """获取趋势币数据"""
        logger.info("TREND_DATA_THREAD: Run method started.")
        try:
            if self.should_stop:
                logger.info("TREND_DATA_THREAD: should_stop is true, returning early.")
                return
                
            logger.info(f"TREND_DATA_THREAD: Starting to fetch trend data from {self.api_url}...")
            response = requests.get(self.api_url, timeout=30)
            response.raise_for_status() # Raises an HTTPError for bad responses (4XX or 5XX)
            
            data = response.json()
            logger.info(f"TREND_DATA_THREAD: API response received. Status: {data.get('status')}")
            
            if data.get("status") == "success" and data.get("data"):
                trend_tokens = data["data"]
                
                # 数据质量过滤：只显示有价值的代币
                filtered_tokens = []
                for token in trend_tokens:
                    market_cap = token.get('marketCap', 0)
                    holders = token.get('holders', 0)
                    volume_24h = token.get('volume24h', 0)
                    formula_score = token.get('formulaPrediction', 0)
                    vm_ratio = token.get('vmRatio', 0) # Volatility-to-MarketCap Ratio
                    
                    # 多维度过滤条件 (Adjust these thresholds as needed)
                    if (market_cap >= 5000 and         # 市值至少5000美元
                        holders >= 50 and              # 至少50个持有者
                        vm_ratio <= 8 and              # 波动市值比 <= 8 (较低风险偏好)
                        volume_24h >= 1000 and         # 24小时交易量至少1000美元
                        formula_score > 0):            # 预测评分为正
                        token['source'] = 'trend'      # 为趋势数据源明确设置 source
                        filtered_tokens.append(token)
                
                logger.info(f"TREND_DATA_THREAD: Filtering complete. {len(filtered_tokens)} high-quality tokens remaining.")
                
                # DEBUG: Log data for MASK token
                for tkn in filtered_tokens:
                    if tkn.get('symbol', '').upper() == 'MASK':
                        timestamp_fields = {
                            'lastUpdatedAt': tkn.get('lastUpdatedAt'),
                            'timestamp': tkn.get('timestamp'),
                            'updatedAt': tkn.get('updatedAt'),
                            'latestTweetTimestamp': tkn.get('latestTweetTimestamp'),
                            # Add any other potential timestamp field names you suspect
                        }
                        logger.info(f"TREND_DATA_THREAD: DEBUG - MASK token data before emit: {timestamp_fields}")
                        logger.info(f"TREND_DATA_THREAD: DEBUG - MASK full data: {tkn}") # Log full MASK data
                        break
                
                # 调试：输出前几个代币的原始数据结构
                if filtered_tokens and os.getenv("ENABLE_LOGGING") == "1":
                    logger.debug("=== TREND_DATA_THREAD: Top 3 filtered token data examples ===")
                    for i, token_data_item in enumerate(filtered_tokens[:3]):
                        logger.debug(f"Token {i+1}: {token_data_item.get('symbol', 'Unknown')}")
                        key_fields = {k: v for k, v in token_data_item.items() if k in ['symbol', 'name', 'marketCap', 'volume24h', 'priceChange30m', 'holders', 'vmRatio']}
                        logger.debug(f"  Key fields: {key_fields}")
                
                logger.info(f"TREND_DATA_THREAD: About to emit data_updated with {len(filtered_tokens)} tokens.")
                self.data_updated.emit(filtered_tokens)
                logger.info("TREND_DATA_THREAD: data_updated signal emitted.")
            else:
                error_msg = f"API returned non-success status or no data: status={data.get('status')}, data_exists={bool(data.get('data'))}"
                logger.error(f"TREND_DATA_THREAD: {error_msg}")
                self.error_occurred.emit(error_msg)
                
        except requests.exceptions.Timeout:
            error_msg = f"Network request timed out after 30s for URL: {self.api_url}"
            logger.error(f"TREND_DATA_THREAD: {error_msg}")
            self.error_occurred.emit(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"Network request failed: {str(e)}"
            logger.error(f"TREND_DATA_THREAD: {error_msg}", exc_info=True) # exc_info logs traceback
            self.error_occurred.emit(error_msg)
        except Exception as e: # Catch any other unexpected errors
            error_msg = f"Data processing error or other unexpected issue: {str(e)}"
            logger.error(f"TREND_DATA_THREAD: {error_msg}", exc_info=True)
            self.error_occurred.emit(error_msg)
        finally:
            logger.info("TREND_DATA_THREAD: Run method finished.")
    
    def stop(self):
        """设置停止标志，线程将在下一次检查时安全退出"""
        logger.info("TREND_DATA_THREAD: stop() called, setting should_stop to True.")
        self.should_stop = True 