import logging
from PyQt5.QtCore import QThread, pyqtSignal

# Assuming OKXDexClient and AllTokenBalancesRequest are correctly importable
# If not, you might need to adjust the import path based on your project structure.
# For example: from ...okx_dex_client import OKXDexClient, AllTokenBalancesRequest
# Or ensure okx_dex_client.py is in PYTHONPATH or the same directory.
from okx_dex_client import OKXDexClient, AllTokenBalancesRequest

logger = logging.getLogger(__name__)

class TokenBalanceQueryThread(QThread):
    """代币余额查询线程"""
    
    balance_result = pyqtSignal(dict)  # 查询结果
    balance_error = pyqtSignal(str)    # 错误信息
    
    def __init__(self, okx_client: OKXDexClient, wallet_address: str, token_address: str, token_symbol: str, parent=None):
        super().__init__(parent)
        if okx_client is None:
            raise ValueError("okx_client cannot be None for TokenBalanceQueryThread")
        self.okx_client = okx_client
        self.wallet_address = wallet_address
        self.token_address = token_address # Contract address of the token to query
        self.token_symbol = token_symbol
    
    def run(self):
        """查询代币余额"""
        logger.info(f"TOKEN_BALANCE_QUERY_THREAD: Starting balance query for {self.token_symbol} ({self.token_address[:8]}...) on wallet {self.wallet_address[:8]}...")
        try:
            # 使用真实的OKX DEX API查询余额
            # 查询所有代币余额 (API might return all, then we filter)
            token_balances_request = AllTokenBalancesRequest(
                address=self.wallet_address,
                chains="501",  # Solana Chain ID
                exclude_risk_token="0"  # 0: include risk tokens, 1: exclude
            )
            
            api_result = self.okx_client.get_all_token_balances(token_balances_request)
            
            if api_result.get('success') and api_result.get('data'):
                data_part = api_result['data']
                balances_list = [] # Initialize to an empty list
                
                # The API response structure can vary, so we try to find the list of balances
                if isinstance(data_part, list):
                    balances_list = data_part
                elif isinstance(data_part, dict):
                    # Common keys for the list of token balances
                    possible_keys = ['tokenAssets', 'data', 'tokenBalances', 'balances']
                    for key in possible_keys:
                        if key in data_part and isinstance(data_part[key], list):
                            balances_list = data_part[key]
                            break
                
                target_balance_value = 0.0
                found_specific_token = None
                
                for token_data_item in balances_list:
                    contract_address = token_data_item.get('tokenContractAddress', '')
                    symbol = token_data_item.get('symbol', '').upper()
                    
                    # Match by contract address primarily, or by symbol if address matches
                    if contract_address.lower() == self.token_address.lower():
                        found_specific_token = token_data_item
                        try:
                            target_balance_value = float(token_data_item.get('balance', 0.0))
                            logger.info(f"TOKEN_BALANCE_QUERY_THREAD: Found {self.token_symbol} by address. Balance = {target_balance_value}")
                        except (ValueError, TypeError):
                            target_balance_value = 0.0
                            logger.warning(f"TOKEN_BALANCE_QUERY_THREAD: {self.token_symbol} balance data format error for address {self.token_address}")
                        break # Found by address, no need to check further
                
                # Fallback: If not found by address, try to find by symbol (less reliable)
                if not found_specific_token:
                    for token_data_item in balances_list:
                        symbol = token_data_item.get('symbol', '').upper()
                        if symbol == self.token_symbol.upper():
                            found_specific_token = token_data_item
                            try:
                                target_balance_value = float(token_data_item.get('balance', 0.0))
                                logger.info(f"TOKEN_BALANCE_QUERY_THREAD: Found {self.token_symbol} by symbol (fallback). Balance = {target_balance_value}")
                            except (ValueError, TypeError):
                                target_balance_value = 0.0
                                logger.warning(f"TOKEN_BALANCE_QUERY_THREAD: {self.token_symbol} balance data format error for symbol (fallback)")
                            break 

                if found_specific_token:
                    balance_formatted = f"{target_balance_value:.6f}".rstrip('0').rstrip('.')
                    if target_balance_value > 1000000:
                        balance_formatted = f"{target_balance_value:.2e}"
                    elif target_balance_value > 1000:
                        balance_formatted = f"{target_balance_value:.2f}"
                    
                    result = {
                        'symbol': self.token_symbol,
                        'balance': target_balance_value,
                        'balance_formatted': balance_formatted,
                        'token_address': self.token_address,
                        'wallet_address': self.wallet_address,
                        'raw_token_data': found_specific_token # Include raw data for potential further use
                    }
                else:
                    logger.info(f"TOKEN_BALANCE_QUERY_THREAD: {self.token_symbol} (Address: {self.token_address}) not found in wallet {self.wallet_address} or balance is zero.")
                    result = {
                        'symbol': self.token_symbol,
                        'balance': 0.0,
                        'balance_formatted': '0',
                        'token_address': self.token_address,
                        'wallet_address': self.wallet_address,
                        'raw_token_data': None
                    }
                self.balance_result.emit(result)
            elif api_result.get('msg'): # Check for specific error message from API
                error_msg = f"API call failed for balance: {api_result.get('msg')} (Code: {api_result.get('code')})"
                logger.error(f"TOKEN_BALANCE_QUERY_THREAD: {error_msg}")
                self.balance_error.emit(error_msg)
            else:
                error_msg = f"API call failed for balance check or returned no data. API Response: {api_result}"
                logger.error(f"TOKEN_BALANCE_QUERY_THREAD: {error_msg}")
                self.balance_error.emit(error_msg)

        except Exception as e:
            error_msg = f"Error querying token balance for {self.token_symbol}: {str(e)}"
            logger.error(f"TOKEN_BALANCE_QUERY_THREAD: {error_msg}", exc_info=True)
            self.balance_error.emit(error_msg)
        finally:
            logger.info(f"TOKEN_BALANCE_QUERY_THREAD: Balance query finished for {self.token_symbol}.") 