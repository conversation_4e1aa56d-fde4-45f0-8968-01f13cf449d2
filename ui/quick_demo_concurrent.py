"""
快速并发演示 - 展示核心性能差异
"""

import time
from concurrent.futures import ThreadPoolExecutor


def quick_api_request(token_symbol: str) -> dict:
    """快速模拟API请求"""
    time.sleep(0.1)  # 100ms延迟
    return {'symbol': token_symbol, 'success': True}


def serial_delay_demo(tokens: list):
    """串行延迟演示（原有问题方案）"""
    print("🐌 串行延迟方案（原有问题）:")
    start_time = time.time()
    
    for i, token in enumerate(tokens):
        delay_ms = i * 100  # 递增延迟
        print(f"   ⏰ {token} 等待 {delay_ms}ms...")
        time.sleep(delay_ms / 1000.0)
        quick_api_request(token)
        print(f"   ✅ {token} 完成")
    
    total_time = time.time() - start_time
    print(f"🐌 串行总耗时: {total_time:.2f}秒\n")
    return total_time


def concurrent_demo(tokens: list):
    """真正并发演示（新方案）"""
    print("🚀 真正并发方案（新优化）:")
    start_time = time.time()
    
    # 🚀 关键：所有代币同时开始！
    with ThreadPoolExecutor(max_workers=len(tokens)) as executor:
        futures = [executor.submit(quick_api_request, token) for token in tokens]
        print(f"   🚀 所有 {len(tokens)} 个代币同时开始处理...")
        
        for i, future in enumerate(futures):
            result = future.result()
            print(f"   ✅ {tokens[i]} 完成")
    
    total_time = time.time() - start_time
    print(f"🚀 并发总耗时: {total_time:.2f}秒\n")
    return total_time


def main():
    """快速演示"""
    # 只用6个代币进行快速演示
    tokens = ["BTC", "ETH", "SOL", "USDT", "BNB", "ADA"]
    
    print(f"⚡ 快速对比演示：{len(tokens)} 个代币处理")
    print("=" * 50)
    
    # 计算理论时间
    api_time = 0.1  # 每个API请求100ms
    serial_delay_time = sum(i * 0.1 for i in range(len(tokens)))  # 递增延迟时间
    theoretical_serial = serial_delay_time + len(tokens) * api_time
    theoretical_concurrent = api_time  # 并发只需要一个API请求的时间
    
    print(f"📊 理论计算:")
    print(f"   串行延迟: {serial_delay_time:.1f}s(等待) + {len(tokens) * api_time:.1f}s(API) = {theoretical_serial:.1f}s")
    print(f"   真正并发: {theoretical_concurrent:.1f}s (所有API同时进行)")
    print(f"   理论提升: {theoretical_serial/theoretical_concurrent:.1f}x 倍\n")
    
    # 实际测试
    print("🧪 实际测试:")
    serial_time = serial_delay_demo(tokens)
    concurrent_time = concurrent_demo(tokens)
    
    print("=" * 50)
    print("📈 实际结果对比:")
    print(f"🐌 串行延迟: {serial_time:.2f}秒")
    print(f"🚀 真正并发: {concurrent_time:.2f}秒")
    print(f"🎉 性能提升: {serial_time/concurrent_time:.1f}x 倍!")
    
    print(f"\n💡 关键问题:")
    print(f"   ❌ 原代码: delay_ms = i * 100 # 人为串行化!")
    print(f"   ✅ 新方案: 所有代币同时开始，真正并发!")
    print(f"   🎯 30个代币: 从 ~{theoretical_serial*5:.0f}秒 降低到 ~{theoretical_concurrent:.1f}秒!")


if __name__ == "__main__":
    main() 