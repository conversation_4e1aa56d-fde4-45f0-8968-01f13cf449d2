# ui/historical_tokens_widget.py
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QHeaderView, QLabel, QHBoxLayout, QDialog, QComboBox, QDialogButtonBox, QFrame, QFileDialog, QMessageBox
from PyQt5.QtCore import pyqtSlot, Qt, pyqtSignal
from datetime import datetime
import time

# 假设 APIService 在父级目录或可被导入
# 这可能需要根据实际项目结构调整
import sys
import os
# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取父目录 (ui 目录的父目录，即项目根目录)
parent_dir = os.path.dirname(current_dir)
# 将项目根目录添加到 sys.path
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from api_service import APIService
from strategies import StrategyFactory # Import StrategyFactory
from PyQt5.QtGui import QColor

logger = logging.getLogger(__name__)
# 基本日志配置，如果主程序中未配置
# if not logger.handlers:
#     logging.basicConfig(
#         level=logging.DEBUG,
#         format='%(asctime)s - %(name)s - %(levelname)s - [%(module)s.%(funcName)s:%(lineno)d] - %(message)s',
#         handlers=[
#             logging.FileHandler("app_debug.log", mode='w'),
#             logging.StreamHandler()
#         ]
#     )

# 自定义表格项类，支持数值排序
class NumericTableWidgetItem(QTableWidgetItem):
    """支持基于UserRole数据进行数值排序的QTableWidgetItem子类"""
    
    def __init__(self, text=""):
        super().__init__(text)
    
    def __lt__(self, other):
        """重写比较方法，使用UserRole数据进行数值比较"""
        try:
            # 尝试获取UserRole数据进行比较
            self_value = self.data(Qt.UserRole)
            other_value = other.data(Qt.UserRole)
            
            # 如果两者都有UserRole数据，使用数值比较
            if self_value is not None and other_value is not None:
                return float(self_value) < float(other_value)
            
            # 否则回退到文本比较
            return self.text() < other.text()
        except (ValueError, TypeError):
            # 如果转换失败，回退到文本比较
            return self.text() < other.text()

# --- StrategySelectionDialog class (Embedded for now, ideally in its own file) ---
class StrategySelectionDialog(QDialog):
    def __init__(self, available_strategies, default_strategy_name, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择批量回测策略")
        self.setMinimumWidth(300)
        layout = QVBoxLayout(self)
        self.label = QLabel("请为批量回测选择一个策略:")
        layout.addWidget(self.label)
        self.strategy_combo = QComboBox()
        if available_strategies:
            for strategy_name in available_strategies:
                self.strategy_combo.addItem(strategy_name)
            if default_strategy_name and default_strategy_name in available_strategies:
                self.strategy_combo.setCurrentText(default_strategy_name)
            elif self.strategy_combo.count() > 0:
                 self.strategy_combo.setCurrentIndex(0) # Default to first if preferred not found
        else:
            self.strategy_combo.addItem("无可用策略")
            self.strategy_combo.setEnabled(False)
        layout.addWidget(self.strategy_combo)
        self.buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, self)
        self.buttons.accepted.connect(self.accept)
        self.buttons.rejected.connect(self.reject)
        layout.addWidget(self.buttons)
        self.selected_strategy_name = None
        if self.strategy_combo.isEnabled() and self.strategy_combo.count() > 0 :
             self.selected_strategy_name = self.strategy_combo.currentText() # Pre-select if possible

    def accept(self):
        if self.strategy_combo.isEnabled() and self.strategy_combo.count() > 0:
            self.selected_strategy_name = self.strategy_combo.currentText()
        else:
            self.selected_strategy_name = None # No valid strategy if combo is disabled or empty
        super().accept()

    @staticmethod
    def get_strategy(parent=None):
        default_vwap_strategy = "VWAP 交叉策略"
        try:
            strategy_names = [s.name for s in StrategyFactory.get_all_strategies()]
            if not strategy_names:
                strategy_names = [default_vwap_strategy, "MACDjia交叉策略"] # Fallback
        except Exception as e:
            logger.error(f"Error getting strategies from factory: {e}")
            strategy_names = [default_vwap_strategy, "MACD交叉策略"] # Fallback
        
        dialog = StrategySelectionDialog(strategy_names, default_vwap_strategy, parent)
        if dialog.exec_() == QDialog.Accepted:
            return dialog.selected_strategy_name
        return None
# --- End of StrategySelectionDialog class ---

class HistoricalTokensWidget(QWidget):
    token_selected = pyqtSignal(dict)
    # Signal to request batch backtest, parameters: list of tokens, strategy_name, initial_capital
    batch_backtest_requested = pyqtSignal(list, str, float)
    # Restore signal for individual backtest: token_info, strategy_name, initial_capital
    individual_backtest_requested = pyqtSignal(dict, str, float) 

    def __init__(self, api_service: APIService, parent=None):
        super().__init__(parent)
        self.api_service = api_service
        self.historical_tokens_data = []
        self.default_strategy_name = "VWAP 交叉策略" # Define default strategy
        self.init_ui()
        self.connect_signals()
        self.load_historical_tokens() # 初始加载

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 状态栏布局（包含加载状态和缓存信息）
        status_layout = QHBoxLayout()
        self.status_label = QLabel("正在加载历史代币...")
        self.status_label.setAlignment(Qt.AlignLeft)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 缓存状态标签
        self.cache_status_label = QLabel("")
        self.cache_status_label.setStyleSheet("color: #666; font-style: italic;")
        self.cache_status_label.setAlignment(Qt.AlignRight)
        status_layout.addWidget(self.cache_status_label)
        
        layout.addLayout(status_layout)

        # --- Top button layout (Refresh and Batch Backtest) ---
        top_button_layout = QHBoxLayout()
        
        # 刷新按钮（普通刷新）
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.setToolTip("刷新数据（优先使用缓存）")
        top_button_layout.addWidget(self.refresh_button)
        
        # 强制刷新按钮（跳过缓存）
        self.force_refresh_button = QPushButton("强制刷新")
        self.force_refresh_button.setToolTip("强制从API获取最新数据（忽略缓存）")
        self.force_refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        top_button_layout.addWidget(self.force_refresh_button)

        self.batch_backtest_button = QPushButton("批量回测全部")
        top_button_layout.addWidget(self.batch_backtest_button)
        
        # Add a vertical separator line
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        top_button_layout.addWidget(separator_line)
        
        # --- Single token backtest with strategy dropdown ---
        self.individual_strategy_combo = QComboBox()
        try:
            strategy_names = [s.name for s in StrategyFactory.get_all_strategies()]
            if strategy_names:
                self.individual_strategy_combo.addItems(strategy_names)
                if self.default_strategy_name in strategy_names:
                    self.individual_strategy_combo.setCurrentText(self.default_strategy_name)
                elif self.individual_strategy_combo.count() > 0:
                    self.individual_strategy_combo.setCurrentIndex(0) # Default to first if preferred not found
            else:
                self.individual_strategy_combo.addItem("无可用策略")
                self.individual_strategy_combo.setEnabled(False)
        except Exception as e:
            logger.error(f"Error populating individual strategy combo: {e}")
            self.individual_strategy_combo.addItem("策略加载失败")
            self.individual_strategy_combo.setEnabled(False)
        top_button_layout.addWidget(self.individual_strategy_combo)

        self.selected_token_backtest_button = QPushButton("用选中策略回测此币") 
        self.selected_token_backtest_button.setEnabled(False) 
        top_button_layout.addWidget(self.selected_token_backtest_button)
        # --- End of single token backtest with strategy dropdown ---
        
        top_button_layout.addStretch()

        self.export_csv_button_historical = QPushButton("导出CSV") # New button
        self.export_csv_button_historical.setFixedWidth(100)       # New button
        self.export_csv_button_historical.clicked.connect(self.export_historical_to_csv) # New button
        top_button_layout.addWidget(self.export_csv_button_historical) # New button

        layout.addLayout(top_button_layout)

        self.tokens_table = QTableWidget()
        self.tokens_table.setColumnCount(10) # Increased from 9 to 10
        self.tokens_table.setHorizontalHeaderLabels([
            "Address", "Symbol", "Name", "Decimals", 
            "Created At", "OHLCV Count", "Data Start", "Data End",
            "本地缓存", # New column for cache status
            "回测状态/结果" # Column 9 (previously 8) for status
        ])
        
        header = self.tokens_table.horizontalHeader()
        # First, set a default resize mode for all, e.g., Interactive or Stretch
        # header.setSectionResizeMode(QHeaderView.Stretch) # Option 1: Stretch all then override
        # Option 2: Set most to interactive/content based, and specific ones to stretch or fixed.
        
        # Set specific column behaviors
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents) # Address
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents) # Symbol
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Name (let it stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents) # Decimals
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents) # Created At
        # For "OHLCV Count" column (index 5)
        header.setSectionResizeMode(5, QHeaderView.Interactive)      # OHLCV Count - allow interactive resize
        self.tokens_table.setColumnWidth(5, 70)                       # Set a narrower initial width (e.g., 70px)

        header.setSectionResizeMode(6, QHeaderView.Stretch)           # Data Start (can stretch or be ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.Stretch)           # Data End (can stretch or be ResizeToContents)
        
        # New column for Cache Status (index 8)
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents) # Cache status
        # For "回测状态/结果" column (index 9, previously 8)
        header.setSectionResizeMode(9, QHeaderView.Interactive) # Allow user to resize if they want more/less
        self.tokens_table.setColumnWidth(9, 150) # Set initial width to 150px for status column

        self.tokens_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.tokens_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tokens_table.setSelectionMode(QTableWidget.SingleSelection)
        self.tokens_table.setSortingEnabled(True) # 允许排序
        self.tokens_table.itemSelectionChanged.connect(self.on_historical_token_selected_in_table)
        layout.addWidget(self.tokens_table)

    def connect_signals(self):
        self.refresh_button.clicked.connect(self.load_historical_tokens)
        self.force_refresh_button.clicked.connect(self.force_refresh_historical_tokens)
        self.batch_backtest_button.clicked.connect(self.on_batch_backtest_all_clicked) # Connect new button
        self.selected_token_backtest_button.clicked.connect(self.on_selected_token_backtest_clicked)
        if self.api_service:
            self.api_service.historical_tokens_ready.connect(self.on_historical_tokens_ready)
            self.api_service.historical_tokens_error.connect(self.on_historical_tokens_error)
        else:
            logger.error("HistoricalTokensWidget: APIService instance is None. Cannot connect signals.")

    @pyqtSlot()
    def force_refresh_historical_tokens(self):
        """强制刷新历史代币（清除缓存后重新加载）"""
        self.status_label.setText("正在强制刷新历史代币...")
        self.cache_status_label.setText("清除缓存中...")
        self.tokens_table.setRowCount(0)  # 清空表格
        
        if self.api_service and self.api_service.db_service:
            # 清除缓存
            self.api_service.db_service.clear_historical_tokens_cache()
            self.cache_status_label.setText("缓存已清除")
            logger.info("历史代币缓存已清除，正在重新获取数据...")
        
        # 重新加载数据
        if self.api_service:
            self.api_service.get_historical_tokens_async()
        else:
            self.on_historical_tokens_error("APIService未初始化。")

    @pyqtSlot()
    def load_historical_tokens(self):
        self.status_label.setText("正在加载历史代币...")
        self.cache_status_label.setText("")  # 清空缓存状态
        self.tokens_table.setRowCount(0) # 清空表格
        if self.api_service:
            self.api_service.get_historical_tokens_async()
        else:
            self.on_historical_tokens_error("APIService未初始化。")

    @pyqtSlot(list)
    def on_historical_tokens_ready(self, tokens: list):
        self.historical_tokens_data = tokens
        self.status_label.setText(f"成功加载 {len(tokens)} 个历史代币。")
        
        # 检查是否是从缓存加载的
        if self.api_service and self.api_service.db_service:
            # 获取缓存信息
            try:
                from config import HISTORICAL_TOKENS_CACHE_CONFIG
                cache_max_age = HISTORICAL_TOKENS_CACHE_CONFIG.get("cache_max_age_seconds", 3600)
            except ImportError:
                cache_max_age = 3600
                
            cached_data = self.api_service.db_service.get_historical_tokens_list(max_age_seconds=cache_max_age)
            
            # 获取缓存时间信息
            conn = None
            try:
                conn = self.api_service.db_service._get_connection()
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT last_updated FROM ohlcv_cache 
                    WHERE token_address = '__HISTORICAL_TOKENS_LIST__' 
                    AND timeframe = 'list' 
                    AND source = 'historical_tokens_api'
                """)
                row = cursor.fetchone()
                
                if row and cached_data:
                    last_updated = row['last_updated']
                    current_time = time.time()
                    age_seconds = current_time - last_updated
                    
                    # 格式化时间差
                    if age_seconds < 60:
                        age_str = f"{int(age_seconds)}秒前"
                    elif age_seconds < 3600:
                        age_str = f"{int(age_seconds/60)}分钟前"
                    elif age_seconds < 86400:
                        age_str = f"{int(age_seconds/3600)}小时前"
                    else:
                        age_str = f"{int(age_seconds/86400)}天前"
                    
                    # 判断缓存状态
                    if age_seconds <= cache_max_age:
                        # 有效缓存
                        self.cache_status_label.setText(f"(缓存: {age_str})")
                        self.cache_status_label.setStyleSheet("color: #4caf50; font-style: italic;")
                    else:
                        # 过期缓存（可能是网络错误时使用的）
                        self.cache_status_label.setText(f"(过期缓存: {age_str})")
                        self.cache_status_label.setStyleSheet("color: #ff9800; font-style: italic;")
                else:
                    # 数据来自API
                    self.cache_status_label.setText("(最新数据)")
                    self.cache_status_label.setStyleSheet("color: #2196f3; font-style: italic;")
                    
            except Exception as e:
                logger.debug(f"获取缓存时间信息失败: {e}")
                if cached_data:
                    self.cache_status_label.setText("(从缓存加载)")
                    self.cache_status_label.setStyleSheet("color: #4caf50; font-style: italic;")
                else:
                    self.cache_status_label.setText("(从API加载)")
                    self.cache_status_label.setStyleSheet("color: #2196f3; font-style: italic;")
            finally:
                if conn:
                    conn.close()
        
        self.populate_table()

    @pyqtSlot(str)
    def on_historical_tokens_error(self, error_message: str):
        self.status_label.setText(f"加载历史代币失败: {error_message}")
        logger.error(f"历史代币加载错误: {error_message}")

    @pyqtSlot()
    def on_historical_token_selected_in_table(self):
        """当历史代币表格中的选择发生变化时调用"""
        selected_items = self.tokens_table.selectedItems()
        can_backtest_selected = False
        selected_token_info_for_main = {} # Initialize

        if selected_items and self.tokens_table.rowCount() > 0: # Ensure table is not empty
            selected_row = self.tokens_table.currentRow()
            if selected_row < 0: # No valid row selected (e.g. if table is cleared during selection)
                logger.info("HistoricalTokensWidget: Invalid selected_row < 0.")
                self.token_selected.emit({}) # Clear selection
                self.selected_token_backtest_button.setEnabled(False)
                return

            # Get address from the first column of the selected row in the VIEW
            address_item = self.tokens_table.item(selected_row, 0) 
            
            if address_item:
                token_address = address_item.text()
                actual_token_data = None
                # Find the full token data from the original data source using the address
                for token_in_list in self.historical_tokens_data:
                    if token_in_list.get("address") == token_address:
                        actual_token_data = token_in_list
                        break
                
                if actual_token_data:
                    selected_token_info_for_main = {
                        "name": actual_token_data.get("name", token_address),
                        "symbol": actual_token_data.get("symbol", "N/A"),
                        "address": token_address, 
                        "tokenAddress": token_address, 
                        "source": "historical" 
                    }
                    self.token_selected.emit(selected_token_info_for_main)
                    logger.info(f"HistoricalTokensWidget: Token selected via UI address '{token_address}' -> Symbol '{selected_token_info_for_main.get('symbol')}'")
                    can_backtest_selected = True
                else:
                    logger.error(f"HistoricalTokensWidget: Could not find token data for address '{token_address}' in self.historical_tokens_data. This might happen if data source changed without UI refresh or internal data inconsistency.")
                    self.token_selected.emit({}) # Emit empty if data mismatch
            else:
                logger.warning("HistoricalTokensWidget: No address item found in selected row's first column (column 0).")
                self.token_selected.emit({}) # Emit empty if address item not found
        else:
            # No items selected or table is empty
            logger.info("HistoricalTokensWidget: No items selected or table is empty.")
            self.token_selected.emit({}) # Clear selection
        
        # Enable/disable the individual backtest button based on whether a valid selection was made
        self.selected_token_backtest_button.setEnabled(can_backtest_selected and self.individual_strategy_combo.isEnabled())
        
        # If no valid selection could be processed into selected_token_info_for_main, ensure an empty dict is emitted
        if not selected_token_info_for_main or not selected_token_info_for_main.get("address"):
            if selected_items: # Only log if there was an attempt to select
                 logger.debug("HistoricalTokensWidget: Emitting empty token_selected signal due to no valid token info processed.")
            # self.token_selected.emit({}) # This might be redundant if already emitted above
            pass # Covered by emit({}) in branches above

    @pyqtSlot()
    def on_batch_backtest_all_clicked(self):
        """当'批量回测全部'按钮被点击时调用"""
        if not self.historical_tokens_data:
            logger.info("HistoricalTokensWidget: No historical tokens loaded to batch backtest.")
            self.status_label.setText("没有历史代币数据可供批量回测。")
            return

        # 1. 直接使用主界面的策略选择器
        selected_strategy_name = self.individual_strategy_combo.currentText()
        if not selected_strategy_name or "无可用策略" in selected_strategy_name or "策略加载失败" in selected_strategy_name:
            logger.warning("HistoricalTokensWidget: No valid strategy selected for batch backtest.")
            self.status_label.setText("请先在策略下拉菜单中选择有效的策略。")
            return
        
        logger.info(f"HistoricalTokensWidget: Using selected strategy for batch: {selected_strategy_name}")

        tokens_to_backtest = []
        for token_data_from_list in self.historical_tokens_data:
            token_info = {
                "name": token_data_from_list.get("name", token_data_from_list.get("address")),
                "symbol": token_data_from_list.get("symbol", "N/A"),
                "address": token_data_from_list.get("address"),
                "tokenAddress": token_data_from_list.get("address"), 
                "source": "historical"
            }
            if token_info["address"]:
                tokens_to_backtest.append(token_info)
        
        if not tokens_to_backtest:
            logger.info("HistoricalTokensWidget: No valid tokens found for batch backtest after filtering.")
            self.status_label.setText("未找到有效代币进行批量回测。")
            return

        # 使用主界面选择的策略
        default_initial_capital = 10000.0 # Keep default capital for now

        logger.info(f"HistoricalTokensWidget: Batch backtest requested for {len(tokens_to_backtest)} tokens.")
        logger.info(f"HistoricalTokensWidget: Using strategy: {selected_strategy_name}, Initial Capital: {default_initial_capital}")
        for token in tokens_to_backtest[:5]: # Log first 5 tokens as sample
            logger.debug(f"  - Token for batch backtest: {token.get('symbol')} ({token.get('address')})")
        
        self.batch_backtest_requested.emit(tokens_to_backtest, selected_strategy_name, default_initial_capital)
        self.status_label.setText(f"已请求对 {len(tokens_to_backtest)} 个代币使用策略 '{selected_strategy_name}' 进行批量回测...")

    @pyqtSlot()
    def on_selected_token_backtest_clicked(self):
        """Slot for when the top '回测选中代币' button is clicked."""
        selected_row = self.tokens_table.currentRow()
        if selected_row < 0 or selected_row >= len(self.historical_tokens_data):
            logger.warning("HistoricalTokensWidget: '回测选中代币' clicked but no valid row selected.")
            return

        token_data_from_list = self.historical_tokens_data[selected_row]
        token_address = token_data_from_list.get("address")

        if not token_address:
            logger.error(f"HistoricalTokensWidget: No address found for selected row {selected_row}.")
            return
            
        selected_strategy_name = self.individual_strategy_combo.currentText()
        if not selected_strategy_name or "无可用策略" in selected_strategy_name or "策略加载失败" in selected_strategy_name:
            logger.warning(f"HistoricalTokensWidget: No valid strategy selected from dropdown ('{selected_strategy_name}') for individual backtest.")
            # Optionally show a QMessageBox to the user
            self.update_token_backtest_status(token_address, "请先选择有效策略")
            return

        # Now proceed using the logic similar to _on_individual_test_button_clicked
        token_info = {
            "name": token_data_from_list.get("name", token_address),
            "symbol": token_data_from_list.get("symbol", "N/A"),
            "address": token_address,
            "tokenAddress": token_address, 
            "source": "historical" 
        }
        default_initial_capital = 10000.0
        logger.info(f"HistoricalTokensWidget: Individual backtest (via top button) for {token_info.get('symbol')} with strategy '{selected_strategy_name}'")
        self.update_token_backtest_status(token_address, f"策略'{selected_strategy_name[:10]}...'测试中...")
        self.individual_backtest_requested.emit(token_info, selected_strategy_name, default_initial_capital)

    def populate_table(self):
        self.tokens_table.setSortingEnabled(False) 
        self.tokens_table.setRowCount(0) 
        if not self.historical_tokens_data:
            self.tokens_table.setSortingEnabled(True)
            return
        for row, token_data in enumerate(self.historical_tokens_data):
            self.tokens_table.insertRow(row)
            address = token_data.get("address", "N/A")
            symbol = token_data.get("symbol", "") 
            if not symbol: 
                raw_name = token_data.get("name", "")
                if raw_name and "Unknown Token" in raw_name and len(raw_name.split()) > 2 :
                    symbol = raw_name.split()[-1] 
                elif raw_name:
                    symbol = raw_name.split()[0] if raw_name.split() else ""
            name = token_data.get("name", "N/A")
            decimals = token_data.get("decimals", "N/A")
            created_at_raw = token_data.get("createdAt", "")
            created_at_str = ""
            if created_at_raw:
                try: 
                    if created_at_raw.endswith('Z'):
                         dt_obj = datetime.fromisoformat(created_at_raw[:-1] + '+00:00')
                    else:
                         dt_obj = datetime.fromisoformat(created_at_raw)
                    created_at_str = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError as e_iso:
                    logger.warning(f"Could not parse createdAt '{created_at_raw}' with fromisoformat: {e_iso}. Trying manual parsing for Z.")
                    created_at_str = created_at_raw 
                except Exception as e_gen:
                    logger.error(f"General error parsing createdAt '{created_at_raw}': {e_gen}")
                    created_at_str = created_at_raw
            ohlcv_count = token_data.get("ohlcvCount", "N/A")
            time_range = token_data.get("ohlcvTimeRange", {})
            start_ts = time_range.get("start")
            end_ts = time_range.get("end")
            start_dt_str = datetime.fromtimestamp(start_ts).strftime('%Y-%m-%d %H:%M:%S') if isinstance(start_ts, (int, float)) else "N/A"
            end_dt_str = datetime.fromtimestamp(end_ts).strftime('%Y-%m-%d %H:%M:%S') if isinstance(end_ts, (int, float)) else "N/A"
            self.tokens_table.setItem(row, 0, QTableWidgetItem(str(address)))
            self.tokens_table.setItem(row, 1, QTableWidgetItem(str(symbol)))
            self.tokens_table.setItem(row, 2, QTableWidgetItem(str(name)))
            self.tokens_table.setItem(row, 3, QTableWidgetItem(str(decimals)))
            self.tokens_table.setItem(row, 4, QTableWidgetItem(str(created_at_str)))
            self.tokens_table.setItem(row, 5, QTableWidgetItem(str(ohlcv_count)))
            self.tokens_table.setItem(row, 6, QTableWidgetItem(str(start_dt_str)))
            self.tokens_table.setItem(row, 7, QTableWidgetItem(str(end_dt_str)))

            # Check for local cache (column 8)
            cache_status_text = "查询中..."
            if self.api_service and self.api_service.db_service and address != "N/A":
                # Construct cache key as used in api_service.py for 1m timeframe historical data
                # Assumes ApiWorker.CACHE_SOURCE_HISTORICAL_RAW_JSON is "historical_1d_1m_raw_json"
                timeframe_to_check = '1m' # Primary timeframe for historical cache check
                cache_key_source = f"historical_1d_1m_raw_json_{timeframe_to_check}"
                try:
                    cached_data = self.api_service.db_service.get_ohlcv_from_cache(address, timeframe_to_check, cache_key_source)
                    cache_status_text = "是" if cached_data else "否"
                except Exception as e_cache_check:
                    logger.error(f"Error checking cache for {address} in HistoricalTokensWidget: {e_cache_check}")
                    cache_status_text = "错误"
            else:
                cache_status_text = "N/A" # If no address or db_service
            
            cache_status_item = QTableWidgetItem(cache_status_text)
            cache_status_item.setTextAlignment(Qt.AlignCenter)
            if cache_status_text == "是":
                cache_status_item.setForeground(QColor("green"))
            elif cache_status_text == "否":
                cache_status_item.setForeground(QColor("red"))
            self.tokens_table.setItem(row, 8, cache_status_item)
            
            # Status in column 9 with proper sort data - 使用自定义的NumericTableWidgetItem
            status_item = NumericTableWidgetItem("待回测")
            status_item.setData(Qt.UserRole, -999997.0)  # 设置待回测状态的排序值
            self.tokens_table.setItem(row, 9, status_item)

        self.tokens_table.resizeColumnsToContents() # Resize all columns to content first
        # Then, specifically set the width for the status column (index 9) if ResizeToContents made it too small
        # and ensure its resize mode allows for this set width or further interaction.
        header = self.tokens_table.horizontalHeader()
        header.setSectionResizeMode(9, QHeaderView.Interactive) # Ensure it's interactive
        current_status_col_width = self.tokens_table.columnWidth(9)
        desired_status_col_width = 150 # Your desired width (e.g., 150px)
        if current_status_col_width < desired_status_col_width:
             self.tokens_table.setColumnWidth(9, desired_status_col_width)
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents) # Ensure cache column resizes to content
        
        # Ensure other columns are still reasonably sized (e.g., allow stretching for Name)
        # This might need to re-apply specific modes if resizeColumnsToContents() changed them all to Fixed.
        # However, setSectionResizeMode in init_ui should define the general behavior.
        # The following line might be redundant if init_ui settings are persistent enough,
        # but can be a safeguard.
        header.setSectionResizeMode(2, QHeaderView.Stretch) # Name
        header.setSectionResizeMode(6, QHeaderView.Stretch) # Data Start
        header.setSectionResizeMode(7, QHeaderView.Stretch) # Data End

        self.tokens_table.setSortingEnabled(True) 
        logger.info(f"历史代币表格已填充 {len(self.historical_tokens_data)} 行。")

    def _extract_percentage_from_text(self, text: str) -> float:
        """从文本中提取百分比数值，用于排序"""
        import re
        try:
            # 使用正则表达式查找百分比模式，如 "59.11%"、"-2.5%"、"100.00%"
            pattern = r'[-+]?\d*\.?\d+%'
            matches = re.findall(pattern, text)
            if matches:
                # 取最后一个匹配的百分比（通常是盈亏百分比）
                percentage_str = matches[-1].replace('%', '')
                return float(percentage_str)
        except (ValueError, AttributeError, IndexError):
            pass
        return None

    def update_token_backtest_status(self, token_address: str, status_message: str, result_summary: dict = None):
        """更新指定代币在表格中的回测状态"""
        # Find the row for the given token_address
        for row in range(self.tokens_table.rowCount()):
            address_item = self.tokens_table.item(row, 0) # Address is in column 0
            if address_item and address_item.text() == token_address:
                status_column_index = 9 # "回测状态/结果" column is now 9
                status_item = self.tokens_table.item(row, status_column_index)
                if not status_item:
                    status_item = NumericTableWidgetItem()  # 使用自定义的NumericTableWidgetItem
                    self.tokens_table.setItem(row, status_column_index, status_item)
                
                current_text = status_message
                sort_value = 0.0  # 默认排序值
                
                if result_summary:
                    profit_percentage = result_summary.get('profit_percentage')
                    trade_count = result_summary.get('trade_count', 0)
                    if isinstance(profit_percentage, (int, float)):
                        # 检查状态文本是否已经包含百分比，避免重复添加
                        if self._extract_percentage_from_text(current_text) is None:
                            current_text += f" (盈亏: {profit_percentage:.2f}%, {trade_count}笔)"
                        sort_value = float(profit_percentage)  # 使用实际的百分比数值用于排序
                    else:
                        # 如果result_summary存在但没有有效的profit_percentage，尝试从文本提取
                        extracted_percentage = self._extract_percentage_from_text(current_text)
                        sort_value = extracted_percentage if extracted_percentage is not None else 0.0
                else:
                    # 尝试从当前状态文本中提取百分比用于排序
                    extracted_percentage = self._extract_percentage_from_text(current_text)
                    if extracted_percentage is not None:
                        sort_value = extracted_percentage  # 使用从文本中提取的百分比
                    else:
                        # 为不同状态设置不同的排序值，确保它们排在有结果的项目之后
                        if "失败" in status_message:
                            sort_value = -999999.0  # 失败的排在最底部
                        elif "进行中" in status_message or "测试中" in status_message:
                            sort_value = -999998.0  # 进行中的排在失败之上
                        elif "待回测" in status_message:
                            sort_value = -999997.0  # 待回测的排在进行中之上
                        else:
                            sort_value = -999996.0  # 其他状态
                
                status_item.setText(current_text)
                # 设置用于排序的数值数据
                status_item.setData(Qt.UserRole, sort_value)

                # Optional: Set background color based on status
                if "完成" in status_message and result_summary:
                    if result_summary.get('total_profit', 0) > 0:
                        status_item.setBackground(QColor("#d4edda")) # Light green for profit
                        status_item.setForeground(QColor("#155724"))
                    elif result_summary.get('total_profit', 0) < 0:
                        status_item.setBackground(QColor("#f8d7da")) # Light red for loss
                        status_item.setForeground(QColor("#721c24"))
                    else:
                        status_item.setBackground(QColor("#e2e3e5")) # Light gray for no profit/loss
                elif "失败" in status_message:
                    status_item.setBackground(QColor("#fff3cd")) # Light yellow for error/warning
                    status_item.setForeground(QColor("#856404"))
                elif "进行中" in status_message or "测试中" in status_message:
                    status_item.setBackground(QColor("#cce5ff")) # Light blue for in progress
                    status_item.setForeground(QColor("#004085"))
                else:
                    status_item.setBackground(Qt.white) # Default
                    status_item.setForeground(Qt.black)

                logger.info(f"Updated backtest status for {token_address} (row {row}) to: {current_text}")
                return True # Found and updated
        logger.warning(f"Could not find token {token_address} in table to update status.")
        return False

    def export_historical_to_csv(self):
        """导出历史代币表格数据到CSV文件"""
        if not self.historical_tokens_data:
            QMessageBox.information(self, "无数据", "没有可导出的历史代币数据。")
            return

        path, _ = QFileDialog.getSaveFileName(self, "保存CSV文件", "historical_tokens.csv", "CSV 文件 (*.csv)")
        if not path:
            return

        try:
            with open(path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                import csv
                writer = csv.writer(csvfile)

                # 写入表头
                header_labels = [self.tokens_table.horizontalHeaderItem(i).text() for i in range(self.tokens_table.columnCount())]
                writer.writerow(header_labels)

                # 写入数据行
                for row in range(self.tokens_table.rowCount()):
                    row_data = []
                    for column in range(self.tokens_table.columnCount()):
                        item = self.tokens_table.item(row, column)
                        row_data.append(item.text() if item else '')
                    writer.writerow(row_data)
            
            QMessageBox.information(self, "导出成功", f"数据已成功导出到 {path}")
            logger.info(f"历史代币数据已导出到: {path}")
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出数据时发生错误: {e}")
            logger.error(f"导出历史代币到CSV失败: {e}", exc_info=True)

# Example usage (for testing this widget independently)
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    
    # Mock APIService for testing if needed, or a real one if config is set up
    # For simplicity, we'll assume a real APIService can be instantiated
    # Ensure your config.py and other dependencies for APIService are met
    try:
        api_service_instance = APIService() # Needs to be a QObject for signals/slots
    except Exception as e:
        print(f"Could not create APIService: {e}")
        api_service_instance = None


    if api_service_instance:
        main_window = HistoricalTokensWidget(api_service=api_service_instance)
        main_window.setWindowTitle("Historical Tokens Test")
        main_window.setGeometry(100, 100, 1000, 600)
        main_window.show()
    else:
        error_label = QLabel("无法启动：APIService 初始化失败。请检查依赖和配置。")
        error_label.show()


    sys.exit(app.exec_()) 