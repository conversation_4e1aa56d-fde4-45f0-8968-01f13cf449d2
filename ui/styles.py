"""
实盘交易组件样式定义
包含所有UI组件的QSS样式
"""

class LiveTradingStyles:
    """实盘交易组件的样式集合"""
    
    # 主面板样式
    MAIN_PANEL = """
        QWidget {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #2c3e50, stop: 1 #34495e);
            border: 1px solid #1a252f;
            border-radius: 4px;
        }
    """
    
    # 控制面板样式
    CONTROL_PANEL = """
        QWidget {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #2c3e50, stop: 1 #34495e);
            border: 1px solid #1a252f;
            border-radius: 4px;
        }
    """
    
    # 分组框样式
    GROUP_BOX_TREND = """
        QGroupBox {
            font-weight: bold;
            font-size: 12px;
            color: #ecf0f1;
            border: 1px solid #1a252f;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 5px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #34495e, stop: 1 #2c3e50);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #f39c12;
        }
    """
    
    GROUP_BOX_CHART = """
        QGroupBox {
            font-weight: bold;
            font-size: 12px;
            color: #ecf0f1;
            border: 1px solid #1a252f;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 5px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #34495e, stop: 1 #2c3e50);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #3498db;
        }
    """
    
    GROUP_BOX_TRADE_RECORDS = """
        QGroupBox {
            font-weight: bold;
            font-size: 12px;
            color: #ecf0f1;
            border: 1px solid #1a252f;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 5px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #34495e, stop: 1 #2c3e50);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #e67e22;
        }
    """
    
    GROUP_BOX_RESULTS = """
        QGroupBox {
            font-weight: bold;
            font-size: 11px;
            color: #f39c12;
            border: 1px solid #1a252f;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 5px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #34495e, stop: 1 #2c3e50);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 6px 0 6px;
        }
    """
    
    # 下拉框样式
    COMBOBOX_DEFAULT = """
        QComboBox {
            background-color: #3a4a5c;
            color: #ecf0f1;
            border: 1px solid #5a6c7d;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 10px;
            min-width: 130px;
            max-height: 26px;
        }
        QComboBox:hover {
            border: 1px solid #3498db;
        }
        QComboBox::drop-down {
            border: none;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #bdc3c7;
            margin-right: 5px;
        }
    """
    
    COMBOBOX_SMALL = """
        QComboBox {
            background-color: #3a4a5c;
            color: #ecf0f1;
            border: 1px solid #5a6c7d;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 9px;
            min-width: 60px;
            max-height: 22px;
        }
        QComboBox:hover {
            border: 1px solid #3498db;
        }
        QComboBox::drop-down {
            border: none;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #bdc3c7;
            margin-right: 5px;
        }
    """
    
    COMBOBOX_SOL_AMOUNT = """
        QComboBox {
            background-color: #3a4a5c;
            color: #ecf0f1;
            border: 1px solid #5a6c7d;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 10px;
            min-width: 60px;
            max-height: 26px;
        }
        QComboBox:hover {
            border: 1px solid #3498db;
        }
        QComboBox::drop-down {
            border: none;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid #bdc3c7;
            margin-right: 5px;
        }
    """
    
    # 复选框样式
    CHECKBOX_MARKET_MONITOR = """
        QCheckBox {
            color: #ecf0f1;
            font-size: 10px;
            font-weight: bold;
            spacing: 6px;
        }
        QCheckBox::indicator {
            width: 14px;
            height: 14px;
            border: 2px solid #5a6c7d;
            border-radius: 3px;
            background-color: #3a4a5c;
        }
        QCheckBox::indicator:checked {
            background-color: #f39c12;
            border: 2px solid #e67e22;
        }
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: white;
            font-weight: bold;
        }
    """
    
    # 按钮样式
    BUTTON_TRADING_TOGGLE_OFF = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #95a5a6, stop: 1 #7f8c8d);
            color: white;
            border: 1px solid #6c7b7c;
            border-radius: 3px;
            padding: 4px 10px;
            font-size: 10px;
            font-weight: bold;
            min-width: 90px;
            max-height: 26px;
        }
        QPushButton:checked {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #e74c3c, stop: 1 #c0392b);
            border: 1px solid #a93226;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #aab7b8, stop: 1 #95a5a6);
        }
        QPushButton:checked:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #ec7063, stop: 1 #e74c3c);
        }
    """
    
    BUTTON_REFRESH = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #3498db, stop: 1 #2980b9);
            color: white;
            border: 1px solid #2471a3;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 10px;
            font-weight: bold;
            min-width: 30px;
            max-height: 26px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #5dade2, stop: 1 #3498db);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #2980b9, stop: 1 #1f618d);
        }
    """
    
    BUTTON_EXECUTE_STRATEGY = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #27ae60, stop: 1 #229954);
            color: white;
            border: 1px solid #1e8449;
            border-radius: 3px;
            padding: 4px 12px;
            font-size: 10px;
            font-weight: bold;
            min-width: 100px;
            max-height: 26px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #2ecc71, stop: 1 #27ae60);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #229954, stop: 1 #1e8449);
        }
        QPushButton:disabled {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #95a5a6, stop: 1 #7f8c8d);
            border: 1px solid #6c7b7c;
        }
    """
    
    BUTTON_BUY = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #f39c12, stop: 1 #e67e22);
            color: white;
            border: 1px solid #d35400;
            border-radius: 3px;
            padding: 4px 12px;
            font-size: 10px;
            font-weight: bold;
            min-width: 70px;
            max-height: 26px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #f7dc6f, stop: 1 #f39c12);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #e67e22, stop: 1 #d35400);
        }
        QPushButton:disabled {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #95a5a6, stop: 1 #7f8c8d);
            border: 1px solid #6c7b7c;
        }
    """
    
    BUTTON_SELL = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #e74c3c, stop: 1 #c0392b);
            color: white;
            border: 1px solid #a93226;
            border-radius: 3px;
            padding: 4px 12px;
            font-size: 10px;
            font-weight: bold;
            min-width: 70px;
            max-height: 26px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #ec7063, stop: 1 #e74c3c);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #c0392b, stop: 1 #a93226);
        }
        QPushButton:disabled {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #95a5a6, stop: 1 #7f8c8d);
            border: 1px solid #6c7b7c;
        }
    """
    
    BUTTON_WALLET = """
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #9b59b6, stop: 1 #8e44ad);
            color: white;
            border: 1px solid #7d3c98;
            border-radius: 3px;
            padding: 4px 10px;
            font-size: 10px;
            font-weight: bold;
            min-width: 80px;
            max-height: 26px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #bb8fce, stop: 1 #9b59b6);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #8e44ad, stop: 1 #7d3c98);
        }
    """
    
    BUTTON_TEST_CHART = """
        QPushButton {
            background-color: #27ae60;
            color: white;
            border: 1px solid #229954;
            border-radius: 3px;
            padding: 2px 8px;
            font-size: 9px;
            font-weight: bold;
            max-height: 22px;
        }
        QPushButton:hover {
            background-color: #2ecc71;
        }
    """
    
    BUTTON_CLEAR_RESULTS = """
        QPushButton {
            background-color: #e74c3c;
            color: white;
            border: 1px solid #c0392b;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 9px;
            font-weight: bold;
            max-height: 22px;
        }
        QPushButton:hover {
            background-color: #ec7063;
        }
    """
    
    BUTTON_CLEAR_TRADES = """
        QPushButton {
            background-color: #e74c3c;
            color: white;
            border: 1px solid #c0392b;
            border-radius: 3px;
            padding: 2px 8px;
            font-size: 9px;
            font-weight: bold;
            max-height: 22px;
        }
        QPushButton:hover {
            background-color: #ec7063;
        }
    """
    
    # 表格样式
    TABLE_TREND = """
        QTableWidget {
            background-color: #2c3e50;
            border: none;
            gridline-color: #34495e;
            font-size: 10px;
            color: #ecf0f1;
            selection-background-color: #3498db;
        }
        QTableWidget::item {
            padding: 4px 6px;
            border-bottom: 1px solid #34495e;
            background-color: transparent;
        }
        QTableWidget::item:selected {
            background-color: #3498db;
            color: #ffffff;
        }
        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #4a5568, stop: 1 #3a4a5c);
            border: none;
            border-bottom: 2px solid #1a252f;
            border-right: 1px solid #2c3e50;
            padding: 6px 8px;
            font-size: 10px;
            font-weight: bold;
            color: #ecf0f1;
            text-align: left;
        }
        QHeaderView::section:last {
            border-right: none;
        }
    """
    
    TABLE_TRADE_RECORDS = """
        QTableWidget {
            background-color: #2c3e50;
            border: none;
            gridline-color: #34495e;
            font-size: 9px;
            color: #ecf0f1;
            selection-background-color: #3498db;
        }
        QTableWidget::item {
            padding: 2px 4px;
            border-bottom: 1px solid #34495e;
        }
        QTableWidget::item:selected {
            background-color: #3498db;
        }
        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #4a5568, stop: 1 #3a4a5c);
            border: none;
            border-bottom: 2px solid #1a252f;
            border-right: 1px solid #2c3e50;
            padding: 4px 6px;
            font-size: 9px;
            font-weight: bold;
            color: #ecf0f1;
        }
    """
    
    # 分割器样式
    SPLITTER_HORIZONTAL = """
        QSplitter::handle {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #4a5568, stop: 1 #3a4a5c);
            width: 3px;
        }
        QSplitter { 
            background-color: transparent; 
        }
    """
    
    SPLITTER_VERTICAL = """
        QSplitter::handle {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #4a5568, stop: 1 #3a4a5c);
            height: 3px;
        }
        QSplitter { 
            background-color: transparent; 
        }
    """
    
    # 标签样式
    LABEL_CONTROL = "font-weight: bold; color: #ecf0f1; font-size: 11px;"
    LABEL_CHART_CONTROL = "color: #ecf0f1; font-size: 10px; font-weight: bold;"
    LABEL_TRADE_FILTER = "color: #ecf0f1; font-size: 10px;"
    
    LABEL_SELECTED_TOKEN = """
        QLabel {
            color: #bdc3c7;
            font-size: 11px;
            font-weight: bold;
            padding: 5px;
        }
    """
    
    LABEL_INDICATORS_SUMMARY = """
        QLabel {
            color: #95a5a6;
            font-size: 9px;
            padding: 3px;
            background-color: rgba(52, 73, 94, 0.3);
            border-radius: 3px;
            margin: 2px;
        }
    """
    
    LABEL_TRADE_STATS = """
        color: #bdc3c7;
        font-size: 10px;
        padding: 3px 8px;
        background: rgba(52, 73, 94, 0.5);
        border-radius: 3px;
    """
    
    LABEL_TRADE_SUMMARY = """
        color: #95a5a6;
        font-size: 9px;
        padding: 3px;
    """
    
    LABEL_QUICK_STATS = """
        color: #bdc3c7;
        font-size: 9px;
        padding: 3px;
    """
    
    LABEL_RESULT_VALUE = """
        color: #ecf0f1; 
        background: rgba(52, 73, 94, 0.5); 
        padding: 3px 6px; 
        border-radius: 3px;
    """
    
    # 状态标签样式
    STATUS_LABEL = """
        QLabel {
            color: #bdc3c7;
            font-size: 10px;
            padding: 2px 5px;
            max-height: 20px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #3a4a5c, stop: 1 #2c3e50);
            border-top: 1px solid #1a252f;
        }
    """
    
    # 输入框样式
    SPINBOX_CAPITAL = """
        QDoubleSpinBox {
            background-color: #3a4a5c;
            color: #ecf0f1;
            border: 1px solid #5a6c7d;
            border-radius: 3px;
            padding: 3px 6px;
            font-size: 10px;
            min-width: 80px;
            max-height: 26px;
        }
        QDoubleSpinBox:hover {
            border: 1px solid #3498db;
        }
    """
    
    # 进度条样式
    PROGRESS_BAR = """
        QProgressBar {
            border: 1px solid #5a6c7d;
            border-radius: 3px;
            text-align: center;
            height: 20px;
            color: #ecf0f1;
            font-size: 10px;
            background-color: #3a4a5c;
        }
        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 3px;
        }
    """
    
    # 分隔符样式
    SEPARATOR = "color: #5a6c7d; background-color: #5a6c7d;"
    
    # 右键菜单样式
    CONTEXT_MENU = """
        QMenu {
            background-color: #34495e;
            border: 1px solid #2c3e50;
            padding: 5px;
        }
        QMenu::item {
            color: #ecf0f1;
            padding: 5px 20px;
        }
        QMenu::item:selected {
            background-color: #3498db;
        }
    """

    @classmethod
    def get_profit_color_style(cls, profit_percentage: float) -> str:
        """根据收益率返回对应的颜色样式"""
        color = "#27ae60" if profit_percentage >= 0 else "#e74c3c"
        return f"""
            color: {color}; 
            background: rgba(52, 73, 94, 0.5); 
            padding: 3px 6px; 
            border-radius: 3px; 
            font-weight: bold;
        """ 