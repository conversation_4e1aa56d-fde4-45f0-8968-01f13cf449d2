"""
性能监控工具 - 监控应用程序的CPU、内存使用情况
"""

import psutil
import time
import logging
from typing import Dict, List
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit

logger = logging.getLogger('performance_monitor')


class PerformanceMonitor(QObject):
    """性能监控器"""
    
    # 信号
    performance_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 监控数据
        self.cpu_history: List[float] = []
        self.memory_history: List[float] = []
        self.api_request_count = 0
        self.cache_hit_count = 0
        self.start_time = time.time()
        
        # 定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_performance_data)
        self.monitor_timer.start(5000)  # 每5秒更新一次
        
        logger.info("性能监控器启动")
    
    def update_performance_data(self):
        """更新性能数据"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_history.append(cpu_percent)
            if len(self.cpu_history) > 60:  # 保留最近60个数据点（5分钟）
                self.cpu_history.pop(0)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_history.append(memory_percent)
            if len(self.memory_history) > 60:
                self.memory_history.pop(0)
            
            # 运行时间
            uptime = time.time() - self.start_time
            
            # 构建性能数据
            perf_data = {
                'cpu_current': cpu_percent,
                'cpu_avg': sum(self.cpu_history) / len(self.cpu_history) if self.cpu_history else 0,
                'cpu_max': max(self.cpu_history) if self.cpu_history else 0,
                'memory_current': memory_percent,
                'memory_used_mb': memory.used / (1024 * 1024),
                'memory_available_mb': memory.available / (1024 * 1024),
                'api_requests': self.api_request_count,
                'cache_hits': self.cache_hit_count,
                'cache_hit_rate': (self.cache_hit_count / max(1, self.api_request_count)) * 100,
                'uptime_minutes': uptime / 60,
                'timestamp': time.time()
            }
            
            self.performance_updated.emit(perf_data)
            
        except Exception as e:
            logger.error(f"更新性能数据失败: {e}")
    
    def record_api_request(self):
        """记录API请求"""
        self.api_request_count += 1
    
    def record_cache_hit(self):
        """记录缓存命中"""
        self.cache_hit_count += 1
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        return {
            'cpu_avg': sum(self.cpu_history) / len(self.cpu_history) if self.cpu_history else 0,
            'memory_current': psutil.virtual_memory().percent,
            'api_requests': self.api_request_count,
            'cache_hits': self.cache_hit_count,
            'uptime': time.time() - self.start_time
        }


class PerformanceWidget(QWidget):
    """性能监控显示组件"""
    
    def __init__(self, performance_monitor: PerformanceMonitor, parent=None):
        super().__init__(parent)
        self.performance_monitor = performance_monitor
        self.performance_monitor.performance_updated.connect(self.update_display)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("🔥 性能监控")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #f39c12; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # CPU信息
        self.cpu_label = QLabel("CPU: 0%")
        self.cpu_label.setStyleSheet("font-size: 14px; color: #3498db;")
        layout.addWidget(self.cpu_label)
        
        # 内存信息
        self.memory_label = QLabel("内存: 0%")
        self.memory_label.setStyleSheet("font-size: 14px; color: #27ae60;")
        layout.addWidget(self.memory_label)
        
        # API统计
        self.api_label = QLabel("API请求: 0")
        self.api_label.setStyleSheet("font-size: 14px; color: #e74c3c;")
        layout.addWidget(self.api_label)
        
        # 缓存统计
        self.cache_label = QLabel("缓存命中: 0 (0%)")
        self.cache_label.setStyleSheet("font-size: 14px; color: #9b59b6;")
        layout.addWidget(self.cache_label)
        
        # 运行时间
        self.uptime_label = QLabel("运行时间: 0分钟")
        self.uptime_label.setStyleSheet("font-size: 14px; color: #95a5a6;")
        layout.addWidget(self.uptime_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("重置统计")
        self.reset_button.clicked.connect(self.reset_stats)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        button_layout.addWidget(self.reset_button)
        
        self.refresh_button = QPushButton("立即刷新")
        self.refresh_button.clicked.connect(self.force_refresh)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.refresh_button)
        
        layout.addLayout(button_layout)
        
        # 详细信息（可展开）
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(100)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                font-family: monospace;
                font-size: 10px;
            }
        """)
        layout.addWidget(self.details_text)
    
    def update_display(self, perf_data: Dict):
        """更新显示内容"""
        try:
            # 更新标签
            self.cpu_label.setText(f"CPU: {perf_data['cpu_current']:.1f}% (平均: {perf_data['cpu_avg']:.1f}%, 峰值: {perf_data['cpu_max']:.1f}%)")
            
            memory_used = perf_data['memory_used_mb']
            memory_available = perf_data['memory_available_mb']
            self.memory_label.setText(f"内存: {perf_data['memory_current']:.1f}% ({memory_used:.0f}MB 已用, {memory_available:.0f}MB 可用)")
            
            self.api_label.setText(f"API请求: {perf_data['api_requests']}")
            
            cache_rate = perf_data['cache_hit_rate']
            self.cache_label.setText(f"缓存命中: {perf_data['cache_hits']} ({cache_rate:.1f}%)")
            
            uptime_min = perf_data['uptime_minutes']
            self.uptime_label.setText(f"运行时间: {uptime_min:.1f}分钟")
            
            # 更新详细信息
            details = f"""性能详情:
CPU: 当前 {perf_data['cpu_current']:.1f}% | 平均 {perf_data['cpu_avg']:.1f}% | 峰值 {perf_data['cpu_max']:.1f}%
内存: {perf_data['memory_current']:.1f}% ({memory_used:.0f}MB / {memory_used + memory_available:.0f}MB)
API统计: {perf_data['api_requests']} 请求, {perf_data['cache_hits']} 缓存命中 ({cache_rate:.1f}%)
运行时间: {uptime_min:.1f} 分钟
更新时间: {time.strftime('%H:%M:%S', time.localtime(perf_data['timestamp']))}"""
            
            self.details_text.setPlainText(details)
            
        except Exception as e:
            logger.error(f"更新性能显示失败: {e}")
    
    def reset_stats(self):
        """重置统计数据"""
        self.performance_monitor.api_request_count = 0
        self.performance_monitor.cache_hit_count = 0
        self.performance_monitor.start_time = time.time()
        self.performance_monitor.cpu_history.clear()
        self.performance_monitor.memory_history.clear()
        logger.info("性能统计已重置")
    
    def force_refresh(self):
        """强制刷新"""
        self.performance_monitor.update_performance_data() 