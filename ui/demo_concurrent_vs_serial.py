"""
并发 vs 串行演示
展示为什么真正的并发比串行延迟快得多
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor
import requests


def simulate_api_request(token_symbol: str, delay: float = 0.5) -> dict:
    """模拟API请求"""
    print(f"🌐 开始请求: {token_symbol}")
    time.sleep(delay)  # 模拟网络延迟
    print(f"✅ 完成请求: {token_symbol}")
    return {'symbol': token_symbol, 'data': f'mock_data_for_{token_symbol}'}


def serial_with_delay_approach(tokens: list):
    """原有方案：串行 + 递增延迟（模拟当前代码）"""
    print("🐌 串行延迟方案开始...")
    start_time = time.time()
    
    results = []
    for i, token in enumerate(tokens):
        # 模拟递增延迟：0ms, 100ms, 200ms, ...
        delay_ms = i * 100
        print(f"⏰ {token} 等待 {delay_ms}ms 后开始...")
        time.sleep(delay_ms / 1000.0)  # 转换为秒
        
        result = simulate_api_request(token)
        results.append(result)
    
    total_time = time.time() - start_time
    print(f"🐌 串行延迟方案完成，耗时: {total_time:.2f}秒")
    return results, total_time


def true_concurrent_approach(tokens: list):
    """新方案：真正的并发处理"""
    print("🚀 真正并发方案开始...")
    start_time = time.time()
    
    results = []
    
    # 使用线程池实现真正的并发
    with ThreadPoolExecutor(max_workers=8) as executor:
        # 🚀 关键：所有任务同时提交！
        futures = [executor.submit(simulate_api_request, token) for token in tokens]
        
        # 等待所有任务完成
        for future in futures:
            result = future.result()
            results.append(result)
    
    total_time = time.time() - start_time
    print(f"🚀 真正并发方案完成，耗时: {total_time:.2f}秒")
    return results, total_time


def smart_batched_concurrent(tokens: list, batch_size: int = 5):
    """智能分批并发：避免API过载同时保持高效"""
    print("🧠 智能分批并发方案开始...")
    start_time = time.time()
    
    results = []
    batches = [tokens[i:i + batch_size] for i in range(0, len(tokens), batch_size)]
    
    for batch_index, batch in enumerate(batches):
        print(f"📦 处理第 {batch_index + 1} 批: {batch}")
        
        # 每批内部真正并发
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            futures = [executor.submit(simulate_api_request, token) for token in batch]
            
            for future in futures:
                result = future.result()
                results.append(result)
        
        # 批次间短暂间隔（避免API限流）
        if batch_index < len(batches) - 1:
            time.sleep(0.2)  # 200ms间隔
    
    total_time = time.time() - start_time
    print(f"🧠 智能分批并发完成，耗时: {total_time:.2f}秒")
    return results, total_time


def main():
    """主演示函数"""
    # 模拟30个代币
    tokens = [f"TOKEN{i:02d}" for i in range(1, 31)]
    print(f"📊 测试 {len(tokens)} 个代币的处理性能\n")
    
    print("=" * 60)
    
    # 方案1：串行延迟（原有方案）
    serial_results, serial_time = serial_with_delay_approach(tokens)
    
    print("\n" + "=" * 60)
    
    # 方案2：真正并发
    concurrent_results, concurrent_time = true_concurrent_approach(tokens)
    
    print("\n" + "=" * 60)
    
    # 方案3：智能分批并发
    batched_results, batched_time = smart_batched_concurrent(tokens, batch_size=8)
    
    print("\n" + "=" * 60)
    print("📈 性能对比:")
    print(f"🐌 串行延迟:     {serial_time:.2f}秒")
    print(f"🚀 真正并发:     {concurrent_time:.2f}秒 (提升 {serial_time/concurrent_time:.1f}x)")
    print(f"🧠 智能分批并发: {batched_time:.2f}秒 (提升 {serial_time/batched_time:.1f}x)")
    
    print(f"\n💡 结论:")
    print(f"   • 串行延迟是最慢的方案，每个代币都要等待前面的完成")
    print(f"   • 真正并发最快，所有代币同时开始处理")
    print(f"   • 智能分批在性能和API友好性之间取得平衡")
    print(f"   • 30个代币的处理时间从 {serial_time:.0f}秒 减少到 {concurrent_time:.0f}秒！")


if __name__ == "__main__":
    main() 