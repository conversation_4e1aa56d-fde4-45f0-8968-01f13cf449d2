"""
K线图表组件 - 显示代币的K线图和技术指标
"""

import os
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import pandas as pd
import numpy as np
import pyqtgraph as pg
from pyqtgraph import PlotWidget, AxisItem

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QGroupBox, QFormLayout, QCheckBox, QSpinBox, QDoubleSpinBox,
    QSplitter, QFrame, QTabWidget, QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QKeyEvent

from api_service import APIService
from signal_client import SignalData
from indicators import TechnicalIndicators
from config import (
    DEFAULT_TIMEFRAME, AVAILABLE_TIMEFRAMES, CANDLE_COLORS,
    INDICATOR_PARAMS, CHART_REFRESH_INTERVAL
)
from strategies import StrategyFactory

# 配置日志
logger = logging.getLogger('chart_widget')

class CandlestickItem(pg.GraphicsObject):
    """自定义K线图项目类"""
    
    def __init__(self, data):
        """
        初始化K线图项目
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
        """
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = None
        self.scaling_factor = 1_000_000_000  # 将价格乘以10亿
        logger.info(f"CandlestickItem: __init__ called with data of length {len(data) if data is not None else 'None'}")
        self.generatePicture()
    
    def generatePicture(self):
        """生成K线图绘图对象"""
        logger.info("CandlestickItem: generatePicture called")
        self.picture = pg.QtGui.QPicture()
        painter = pg.QtGui.QPainter(self.picture)
        
        w = 0.8  # 蜡烛宽度，占X轴单位宽度的比例
        
        if self.data is None or self.data.empty:
            logger.warning("CandlestickItem: No data to generate picture.")
            painter.end()
            return

        logger.info(f"CandlestickItem: Drawing {len(self.data)} candles with scaling factor {self.scaling_factor}.")
        for i in range(len(self.data)):
            # 获取当前蜡烛的数据
            t = i  # X轴位置
            open_price = self.data['open'].iloc[i] * self.scaling_factor
            high_price = self.data['high'].iloc[i] * self.scaling_factor
            low_price = self.data['low'].iloc[i] * self.scaling_factor
            close_price = self.data['close'].iloc[i] * self.scaling_factor
            
            # logger.debug(f"Candle {i}: O={open_price}, H={high_price}, L={low_price}, C={close_price}")

            # 判断是上涨还是下跌蜡烛
            if close_price >= open_price:
                # 上涨蜡烛，使用绿色
                color_config = CANDLE_COLORS['up']
                color = pg.mkColor(color_config)
                # 绘制实体
                painter.setPen(pg.mkPen(color))
                painter.setBrush(pg.mkBrush(color))
                # 实体从开盘价到收盘价
                painter.drawRect(pg.QtCore.QRectF(t-w/2, open_price, w, close_price-open_price))
                # logger.debug(f"Candle {i}: Up. Color: {color_config}. Rect: x={t-w/2}, y={open_price}, w={w}, h={close_price-open_price}")
            else:
                # 下跌蜡烛，使用红色
                color_config = CANDLE_COLORS['down']
                color = pg.mkColor(color_config)
                # 绘制实体
                painter.setPen(pg.mkPen(color))
                painter.setBrush(pg.mkBrush(color))
                # 实体从开盘价到收盘价
                painter.drawRect(pg.QtCore.QRectF(t-w/2, close_price, w, open_price-close_price))
                # logger.debug(f"Candle {i}: Down. Color: {color_config}. Rect: x={t-w/2}, y={close_price}, w={w}, h={open_price-close_price}")
            
            # 绘制上下影线
            painter.setPen(pg.mkPen(color))
            # 上影线
            if high_price > max(open_price, close_price):
                painter.drawLine(
                    pg.QtCore.QPointF(t, max(open_price, close_price)),
                    pg.QtCore.QPointF(t, high_price)
                )
            # 下影线
            if low_price < min(open_price, close_price):
                painter.drawLine(
                    pg.QtCore.QPointF(t, min(open_price, close_price)),
                    pg.QtCore.QPointF(t, low_price)
                )
        
        painter.end()
        logger.info("CandlestickItem: generatePicture finished")
    
    def paint(self, painter, option, widget):
        """绘制K线图"""
        # logger.info("CandlestickItem: paint called")
        if self.picture:
            painter.drawPicture(0, 0, self.picture)
        else:
            logger.warning("CandlestickItem: No picture to paint")
    
    def boundingRect(self):
        """返回K线图的边界矩形"""
        return pg.QtCore.QRectF(self.picture.boundingRect())


class TimeAxisItem(pg.AxisItem):
    """自定义时间轴项目类"""
    
    def __init__(self, timestamps, *args, **kwargs):
        """
        初始化时间轴
        
        参数:
            timestamps (List): 时间戳列表
            *args, **kwargs: 传递给父类的参数
        """
        super().__init__(*args, **kwargs)
        self.timestamps = timestamps
    
    def tickStrings(self, values, scale, spacing):
        """
        生成刻度字符串
        
        参数:
            values (List): 刻度值列表
            scale (float): 刻度比例
            spacing (float): 刻度间距
            
        返回:
            List[str]: 刻度字符串列表
        """
        result = []
        for value in values:
            try:
                # 将刻度值转换为整数索引
                index = int(value)
                if 0 <= index < len(self.timestamps):
                    # 获取对应的时间戳
                    timestamp = self.timestamps[index]
                    # 转换为日期时间字符串
                    dt = datetime.fromtimestamp(timestamp)
                    result.append(dt.strftime('%H:%M\n%m-%d'))
                else:
                    result.append('')
            except (ValueError, IndexError):
                result.append('')
        
        return result


class ChartWidget(QWidget):
    """K线图表组件类"""
    
    # 添加信号定义，用于通知外部组件买卖信号
    trade_signal_generated = pyqtSignal(object)  # Changed to emit SignalData object  # 信号类型(buy/sell), 价格, 时间戳, 策略名, 索引
    # 🔥🔥 新增：策略分析完成信号，发射最终状态
    strategy_analysis_completed = pyqtSignal(str, str, str, int)  # token_address, token_symbol, final_signal, chart_index
    
    def __init__(self, api_service: APIService, parent=None):
        """
        初始化K线图表组件
        
        参数:
            api_service (APIService): APIService的实例，用于获取数据。
            parent (QWidget, optional): 父组件
        """
        super().__init__(parent)
        self.api_service = api_service
        
        # 🔥🔥 新增：标识是否为后台并行组件
        self.is_background_parallel = False  # 默认为前台UI组件
        
        self.token_data = None
        self.ohlcv_data = None
        self.df = None
        self.timeframe = DEFAULT_TIMEFRAME
        self.days = 1
        self.auto_refresh = False
        self.current_strategy_name: Optional[str] = None
        
        self.context_token_list: Optional[List[Dict]] = None
        self.current_token_index_in_context: int = -1
        
        # 🔥 存储当前策略的买卖信号位置数据
        self.current_buy_signals = []  # 存储买入信号的位置: [(index, price, timestamp), ...]
        self.current_sell_signals = [] # 存储卖出信号的位置: [(index, price, timestamp), ...]
        
        # 初始化UI
        self.init_ui()
        
        # 创建定时器，定时刷新数据
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data)
        
        # 显示初始提示
        self.update_status("请从趋势榜单中选择一个代币")
        
        # 连接APIService的信号 (在构造函数末尾或专门的方法中进行)
        if self.api_service:
            self.api_service.ohlcv_data_ready.connect(self.on_ohlcv_data_received)
            self.api_service.ohlcv_data_error.connect(self.on_ohlcv_fetch_error)
            logger.info("ChartWidget: APIService信号连接成功")
            
            # 添加连接验证
            self.verify_api_connections()
        else:
            logger.error("ChartWidget: APIService instance is None. Cannot connect signals.")
            self.update_status("错误: API服务未初始化")

        # 自动刷新定时器
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data) # 定时器仍然可以触发refresh_data
        # 初始不启动，等待set_token或用户操作
        
        # 闪烁相关变量
        self.highlight_timer = QTimer(self)
        self.highlight_timer.timeout.connect(self.toggle_highlight)
        self.highlight_active = False
        self.highlight_visible = False
        self.highlight_timestamp = None
        self.highlight_signal_type = None
        self.highlight_price = None
        self.highlight_scatter_item = None
    
    def verify_api_connections(self):
        """验证API连接状态"""
        try:
            if not hasattr(self.api_service, 'ohlcv_data_ready'):
                logger.error("ChartWidget: APIService缺少ohlcv_data_ready信号")
                return False
            
            if not hasattr(self.api_service, 'ohlcv_data_error'):
                logger.error("ChartWidget: APIService缺少ohlcv_data_error信号")
                return False
                
            logger.info("ChartWidget: API连接验证通过")
            return True
        except Exception as e:
            logger.error(f"ChartWidget: API连接验证失败: {e}")
            return False
    
    def init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题和控制区域
        header_layout = QHBoxLayout()
        
        # 添加导航按钮
        self.prev_button = QPushButton("<")
        self.prev_button.setFixedWidth(30)
        self.prev_button.clicked.connect(self.show_previous_token)
        self.prev_button.setEnabled(False) # 初始禁用
        header_layout.addWidget(self.prev_button)

        self.token_info_label = QLabel("未选择代币")
        self.token_info_label.setFont(QFont("Arial", 14, QFont.Bold))
        header_layout.addWidget(self.token_info_label)

        self.next_button = QPushButton(">")
        self.next_button.setFixedWidth(30)
        self.next_button.clicked.connect(self.show_next_token)
        self.next_button.setEnabled(False) # 初始禁用
        header_layout.addWidget(self.next_button)

        header_layout.addStretch()
        
        # 时间周期选择
        timeframe_layout = QHBoxLayout()
        timeframe_layout.addWidget(QLabel("时间周期:"))
        
        self.timeframe_combo = QComboBox()
        for tf in AVAILABLE_TIMEFRAMES:
            self.timeframe_combo.addItem(tf)
        self.timeframe_combo.setCurrentText(DEFAULT_TIMEFRAME)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        timeframe_layout.addWidget(self.timeframe_combo)
        
        header_layout.addLayout(timeframe_layout)
        
        # 天数选择
        days_layout = QHBoxLayout()
        days_layout.addWidget(QLabel("天数:"))
        
        self.days_spin = QSpinBox()
        self.days_spin.setRange(1, 30)
        self.days_spin.setValue(1)
        self.days_spin.valueChanged.connect(self.on_days_changed)
        days_layout.addWidget(self.days_spin)
        
        header_layout.addLayout(days_layout)
        
        # --- 添加策略选择下拉框 ---
        strategy_layout = QHBoxLayout()
        strategy_layout.addWidget(QLabel("显示策略信号:"))
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItem("不显示") # 默认不显示任何策略信号
        try:
            for strategy in StrategyFactory.get_all_strategies():
                self.strategy_combo.addItem(strategy.name)
        except Exception as e:
            logger.error(f"加载策略列表失败: {e}")
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        strategy_layout.addWidget(self.strategy_combo)
        header_layout.addLayout(strategy_layout)
        # --- 策略选择下拉框结束 ---
        
        # 自动刷新选项
        self.auto_refresh_check = QCheckBox("自动刷新")
        self.auto_refresh_check.setChecked(False)
        self.auto_refresh_check.stateChanged.connect(self.on_auto_refresh_changed)
        header_layout.addWidget(self.auto_refresh_check)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setFixedWidth(80)
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(header_layout)
        
        # 创建状态标签
        self.status_label = QLabel("准备就绪")
        main_layout.addWidget(self.status_label)
        
        # 创建分割器，上方是K线图，下方是指标
        self.splitter = QSplitter(Qt.Vertical)
        
        # 创建K线图区域
        self.chart_widget = PlotWidget()
        self.chart_widget.setBackground('w' if not CANDLE_COLORS['up'].startswith('#') else '#121212')
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.setLabel('left', '价格 (每十亿代币)', units='$')
        
        # 创建十字光标
        self.crosshair_v = pg.InfiniteLine(angle=90, movable=False)
        self.crosshair_h = pg.InfiniteLine(angle=0, movable=False)
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        
        # 创建价格标签
        self.price_label = pg.TextItem(text="", anchor=(0, 0))
        self.chart_widget.addItem(self.price_label, ignoreBounds=True)
        
        # 连接鼠标移动事件
        self.chart_widget.scene().sigMouseMoved.connect(self.on_mouse_moved)
        
        self.splitter.addWidget(self.chart_widget)
        
        # 为主K线图添加图例 (只需要一次)
        self.chart_legend = self.chart_widget.addLegend(offset=(-10,10)) # offset slightly from top-left
        
        # 创建指标标签页
        self.indicators_tab = QTabWidget()
        # 设置标签页位置为底部，让技术指标图表和K线主图贴得更紧密
        self.indicators_tab.setTabPosition(QTabWidget.South)
        
        # 创建MACD指标页
        self.macd_widget = PlotWidget()
        self.macd_widget.setBackground('w' if not CANDLE_COLORS['up'].startswith('#') else '#121212')
        self.macd_widget.showGrid(x=True, y=True, alpha=0.3)
        # 禁用MACD图表的交互功能
        self.macd_widget.setMouseEnabled(x=False, y=False)
        self.macd_widget.setMenuEnabled(False)
        self.macd_widget.hideButtons()
        self.indicators_tab.addTab(self.macd_widget, "MACD")
        
        # 创建RSI指标页
        self.rsi_widget = PlotWidget()
        self.rsi_widget.setBackground('w' if not CANDLE_COLORS['up'].startswith('#') else '#121212')
        self.rsi_widget.showGrid(x=True, y=True, alpha=0.3)
        # 禁用RSI图表的交互功能
        self.rsi_widget.setMouseEnabled(x=False, y=False)
        self.rsi_widget.setMenuEnabled(False)
        self.rsi_widget.hideButtons()
        self.indicators_tab.addTab(self.rsi_widget, "RSI")
        
        # 创建成交量指标页
        self.volume_widget = PlotWidget()
        self.volume_widget.setBackground('w' if not CANDLE_COLORS['up'].startswith('#') else '#121212')
        self.volume_widget.showGrid(x=True, y=True, alpha=0.3)
        # 禁用成交量图表的交互功能
        self.volume_widget.setMouseEnabled(x=False, y=False)
        self.volume_widget.setMenuEnabled(False)
        self.volume_widget.hideButtons()
        self.indicators_tab.addTab(self.volume_widget, "成交量")
        
        self.splitter.addWidget(self.indicators_tab)
        
        # 设置分割器初始大小
        self.splitter.setSizes([700, 300])
        
        main_layout.addWidget(self.splitter)
        
        # 创建底部信息区域
        footer_layout = QHBoxLayout()
        
        self.token_address_label = QLabel("代币地址: 未选择")
        footer_layout.addWidget(self.token_address_label)
        
        footer_layout.addStretch()
        
        self.last_update_label = QLabel("上次更新: 未更新")
        footer_layout.addWidget(self.last_update_label)
        
        main_layout.addLayout(footer_layout)
        
        # 连接主图表的视图变化信号到技术指标图表的同步方法
        self.chart_widget.getViewBox().sigRangeChanged.connect(self.sync_indicator_charts)
    
    def set_token(self, token_data):
        """
        设置当前显示的代币
        
        参数:
            token_data (Dict): 代币数据, 可能包含 'strategy_name' 和 'timeframe'
        """
        # 🔥 清空上一个代币的信号位置数据
        self.current_buy_signals.clear()
        self.current_sell_signals.clear()
        # 同样，也应该清空图表上可能残留的旧信号标记（如果它们是持久化的）
        if hasattr(self, 'buy_scatter_item') and self.buy_scatter_item:
            if self.buy_scatter_item.scene(): # 检查是否仍在场景中
                self.chart_widget.removeItem(self.buy_scatter_item)
            if self.chart_legend and self.buy_scatter_item.name() and self.buy_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                try:
                    self.chart_legend.removeItem(self.buy_scatter_item.name())
                except Exception as e_legend_remove_set_token:
                    logger.warning(f"Error removing old buy_scatter_item from legend in set_token: {e_legend_remove_set_token}")

            self.buy_scatter_item = None
        if hasattr(self, 'sell_scatter_item') and self.sell_scatter_item:
            if self.sell_scatter_item.scene(): # 检查是否仍在场景中
                self.chart_widget.removeItem(self.sell_scatter_item)
            if self.chart_legend and self.sell_scatter_item.name() and self.sell_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                try:
                    self.chart_legend.removeItem(self.sell_scatter_item.name())
                except Exception as e_legend_remove_set_token:
                     logger.warning(f"Error removing old sell_scatter_item from legend in set_token: {e_legend_remove_set_token}")
            self.sell_scatter_item = None
        # 停止任何可能正在进行的旧信号闪烁
        self.stop_signal_highlight()
        
        self.token_data = token_data
        
        # 更新UI
        symbol = token_data.get('symbol', 'Unknown')
        name = token_data.get('name', 'Unknown')
        price = token_data.get('price', 0)
        
        price_change_key = next((key for key in ['priceChange24h', 'change24h', 'percentChange24h', 'priceChange30m'] if key in token_data), None)
        price_change_val = token_data.get(price_change_key, 0.0)
        if price_change_val is None: price_change_val = 0.0

        price_text = f"${price:.8f}" if price < 0.00001 else f"${price:.6f}"
        
        try:
            price_change_float = float(price_change_val)
            change_text = f"{price_change_float:.2f}%"
            change_color = "color: #4caf50;" if price_change_float > 0 else ("color: #f44336;" if price_change_float < 0 else "color: #808080;")
        except (ValueError, TypeError):
            change_text = "N/A"
            change_color = "color: #808080;"
        
        self.token_info_label.setText(f"{name} ({symbol}) - {price_text} <span style='{change_color}'>{change_text}</span>")
        
        token_address = token_data.get('tokenAddress', 'Unknown')
        # 设置代币地址标签为可选择和复制
        self.token_address_label.setText(f"代币地址: {token_address}")
        self.token_address_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        self.token_address_label.setToolTip("代币地址 (可选择复制)")
        self.token_address_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                padding: 3px;
                background-color: rgba(52, 73, 94, 0.3);
                border-radius: 3px;
                margin: 2px;
                selection-background-color: #3498db;
                selection-color: #ffffff;
            }
        """)
        
        # 优先使用token_data中指定的timeframe (如果有)
        new_timeframe = token_data.get('timeframe', self.timeframe) # 使用旧的self.timeframe作为默认值
        if new_timeframe != self.timeframe:
            self.timeframe = new_timeframe
            # 更新UI中的timeframe_combo，但不触发其currentTextChanged信号（避免循环调用）
            self.timeframe_combo.blockSignals(True)
            self.timeframe_combo.setCurrentText(self.timeframe)
            self.timeframe_combo.blockSignals(False)
            logger.info(f"ChartWidget: Timeframe set from token_data to {self.timeframe}")
        
        # 根据数据来源调整 "天数" 选择框
        token_source = token_data.get('source', 'unknown')
        if token_source == 'historical': # 自定义API (ohlcv-sync)
            self.days_spin.setValue(1)
            self.days_spin.setEnabled(False)
            self.days = 1 # 确保内部状态也更新
            logger.info(f"ChartWidget: Data source is 'historical' (custom API). Days selection disabled and set to 1.")
        elif token_source == 'trend': # Birdeye API
            self.days_spin.setEnabled(True)
            logger.info(f"ChartWidget: Data source is 'trend' (Birdeye API). Days selection enabled.")
        elif token_source == 'holdings': # 🔥🔥 新增：持仓来源
            self.days_spin.setEnabled(True)
            logger.info(f"ChartWidget: Data source is 'holdings' (Wallet Holdings). Days selection enabled.")
        else: # 其他未知来源，默认为可用
            self.days_spin.setEnabled(True)
            logger.warning(f"ChartWidget: Unknown token source '{token_source}'. Days selection defaulted to enabled.")
        
        # 处理外部指定的策略名称
        external_strategy_name = token_data.get('strategy_name')
        if external_strategy_name:
            self.current_strategy_name = external_strategy_name
            
            # 🔥🔥 修复：处理带前缀的策略名称
            # 先尝试直接匹配
            if self.strategy_combo.findText(self.current_strategy_name) != -1:
                self.strategy_combo.blockSignals(True)
                self.strategy_combo.setCurrentText(self.current_strategy_name)
                self.strategy_combo.blockSignals(False)
                logger.info(f"ChartWidget: Strategy set from token_data to '{self.current_strategy_name}'")
            else:
                # 如果直接匹配失败，尝试去除 "📊 " 前缀
                cleaned_strategy_name = self.current_strategy_name.replace("📊 ", "")
                if self.strategy_combo.findText(cleaned_strategy_name) != -1:
                    self.strategy_combo.blockSignals(True)
                    self.strategy_combo.setCurrentText(cleaned_strategy_name)
                    self.strategy_combo.blockSignals(False)
                    # 更新当前策略名称为清理后的名称
                    self.current_strategy_name = cleaned_strategy_name
                    logger.info(f"ChartWidget: Strategy set from token_data to '{cleaned_strategy_name}' (cleaned from '{external_strategy_name}')")
                else:
                    logger.warning(f"ChartWidget: Strategy '{self.current_strategy_name}' from token_data not found in combo. Defaulting to '不显示'.")
                    self.current_strategy_name = "不显示"
                    self.strategy_combo.blockSignals(True)
                    self.strategy_combo.setCurrentText("不显示")
                    self.strategy_combo.blockSignals(False)
        else:
            # 如果外部没有指定策略，则 current_strategy_name 沿用 self.strategy_combo 的当前值
            self.current_strategy_name = self.strategy_combo.currentText()

        self.refresh_data()
        
        # 在set_token的末尾，如果上下文列表存在，尝试更新当前索引
        if self.context_token_list and token_data:
            found_idx = -1
            # 根据token_data的source来决定用哪个key比较地址
            key_to_compare = 'tokenAddress' if token_data.get('source') == 'trend' else 'address'
            for idx, t in enumerate(self.context_token_list):
                if t.get(key_to_compare) == token_data.get(key_to_compare):
                    found_idx = idx
                    break
            if found_idx != -1:
                self.current_token_index_in_context = found_idx
            # 总是更新导航按钮状态，即使token不是通过导航按钮设置的
        self._update_navigation_buttons_state() 
    
    @pyqtSlot() # 明确这是一个槽函数
    def refresh_data(self):
        """触发异步刷新K线数据"""
        if not self.token_data:
            self.update_status("请从趋势榜单中选择一个代币")
            return
        
        token_address = self.token_data.get('tokenAddress')
        token_source = self.token_data.get('source', 'unknown') # 获取来源，默认为'unknown'以防万一
        
        api_description = "未知API"
        if token_source == 'historical':
            api_description = "自定义API (ohlcv-sync.vercel.app)"
        elif token_source == 'trend':
            api_description = "Birdeye API"
        elif token_source == 'holdings':  # 🔥🔥 新增：持仓来源支持
            api_description = "Birdeye API (持仓代币)"

        if not token_address:
            self.update_status("无效的代币地址")
            self.refresh_button.setEnabled(False)
            return
        
        if token_source == 'unknown':
            logger.warning(f"ChartWidget: Token source is unknown for {token_address}. Cannot determine API for OHLCV.")
            self.on_ohlcv_fetch_error("代币来源未知，无法获取K线数据。")
            return
        
        # 🔥🔥 映射holdings来源到trend，因为都使用Birdeye API
        api_source = token_source
        if token_source == 'holdings':
            api_source = 'trend'  # holdings代币使用Birdeye API获取数据"

        # 🔥 优先尝试从 LiveTradingWidget 获取缓存的 OHLCV 数据
        cached_data = self.try_get_cached_ohlcv_data(token_address, self.timeframe)
        if cached_data:
            logger.info(f"ChartWidget: 使用缓存的OHLCV数据 for {token_address} [{self.timeframe}]")
            self.update_status(f"正在使用缓存的K线数据... (来源: {api_description})")
            
            # 直接使用缓存数据更新图表
            self.display_provided_ohlcv(cached_data, self.timeframe, f"缓存数据 ({api_description})")
            return
        
        # 🔥 如果没有缓存数据，则从 API 获取
        self.update_status(f"正在从 {api_description} 获取K线数据...")
        self.refresh_button.setEnabled(False)
            
        logger.info(f"ChartWidget: Attempting to fetch OHLCV data for {token_address} [{self.timeframe}], Days: {self.days}, Source: {token_source} (Using: {api_description})")
        if self.api_service:
            self.api_service.get_ohlcv_data_async(
                token_address=token_address,
                timeframe=self.timeframe,
                days=self.days, 
                source=api_source  # 🔥 使用映射后的source
            )
        else:
            self.on_ohlcv_fetch_error("APIService未初始化，无法获取数据。")
    
    @pyqtSlot(str, str, list) # 修改槽函数签名以匹配新的信号
    def on_ohlcv_data_received(self, received_token_address: str, received_timeframe: str, ohlcv_data: list):
        """处理从APIService收到的OHLCV数据（异步）"""
        
        log_prefix = f"ChartWidget ({self.token_data.get('symbol', 'N/A') if self.token_data else 'Unset'} for {self.timeframe})"
        timestamp_received = time.time()
        
        current_token_address = self.token_data.get('tokenAddress') if self.token_data else None
        current_source = self.token_data.get('source', 'unknown') if self.token_data else 'unknown'

        api_description_received = "未知API来源"
        if current_source == 'historical':
            api_description_received = "自定义API (ohlcv-sync.vercel.app)"
        elif current_source == 'trend':
            api_description_received = "Birdeye API"
        elif current_source == 'holdings':  # 🔥🔥 新增：持仓来源支持
            api_description_received = "Birdeye API (持仓代币)"

        logger.info(f"{log_prefix}: Received OHLCV data signal for {received_token_address} [{received_timeframe}] from source type: {current_source} ({api_description_received}) at {timestamp_received:.2f}. Data points: {len(ohlcv_data) if ohlcv_data else 0}")

        # 🔥🔥 修改可见性检查：后台并行组件即使不可见也要处理数据
        if not self.isVisible() and not self.is_background_parallel: 
             logger.debug(f"{log_prefix}: Received data but widget itself is not visible and not a background parallel component. Ignoring.")
             return

        if not self.token_data or current_token_address != received_token_address:
            logger.debug(f"{log_prefix}: Ignoring data for different token {received_token_address} (current: {current_token_address}).")
            return
        
        if self.timeframe != received_timeframe:
            logger.debug(f"{log_prefix}: Ignoring data for different timeframe {received_timeframe} (current: {self.timeframe}).")
            return
        
        self.refresh_button.setEnabled(True)
        if not ohlcv_data:
            logger.warning(f"{log_prefix}: OHLCV data list is empty for {received_token_address} [{received_timeframe}] from {api_description_received}.")
            self.df = pd.DataFrame() # 确保df被清空
            self.show_error_in_chart(
                f"数据获取成功，但无K线数据返回\n\n来源: {api_description_received}\n代币: {received_token_address}\n周期: {received_timeframe}\n\n可能是该代币在此周期无交易数据。"
            )
            return
        
        logger.info(f"{log_prefix}: Processing OHLCV data ({len(ohlcv_data)} records) for {received_token_address} [{received_timeframe}] from {api_description_received}.")
        if ohlcv_data:
            logger.debug(f"{log_prefix}: First 3 records for {received_token_address}: {ohlcv_data[:3]}")
        
        self.ohlcv_data = ohlcv_data
        try:
            logger.info(f"{log_prefix}: Starting DataFrame creation at {time.time():.2f}")
            self.df = pd.DataFrame(ohlcv_data)
            logger.info(f"{log_prefix}: DataFrame created at {time.time():.2f}. Shape: {self.df.shape if not self.df.empty else 'Empty'}")

            if self.df.empty:
                logger.warning(f"{log_prefix}: DataFrame is empty after creation from ohlcv_data.")
                self.update_status(f"处理后的K线数据为空 (来源: {api_description_received})。")
                self.update_chart()
                return

            logger.info(f"{log_prefix}: Starting indicator calculation at {time.time():.2f}")
            self.df = self.calculate_indicators(self.df)
            logger.info(f"{log_prefix}: Indicator calculation finished at {time.time():.2f}")
            
            logger.info(f"{log_prefix}: Starting chart update (self.update_chart()) at {time.time():.2f}")
            self.update_chart()
            logger.info(f"{log_prefix}: Chart update finished at {time.time():.2f}")
            
            current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_update_label.setText(f"上次更新: {current_time_str}")
            self.update_status(f"已加载 {len(self.df)} 条K线数据 (来源: {api_description_received})")
            
            # 🔥🔥 修改：确保后台并行组件始终保持自动刷新
            if hasattr(self, 'is_background_parallel') and self.is_background_parallel:
                # 后台并行组件：强制保持定时器运行
                if self.auto_refresh and not self.refresh_timer.isActive():
                    logger.info(f"{log_prefix}: Starting auto-refresh timer for background parallel component.")
                    self.refresh_timer.start(CHART_REFRESH_INTERVAL)
            else:
                # 前台组件：正常逻辑
                if self.auto_refresh and not self.refresh_timer.isActive():
                    self.refresh_timer.start(CHART_REFRESH_INTERVAL)
                    logger.info(f"{log_prefix}: Auto-refresh timer started for front-end component.")

        except Exception as e:
            logger.error(f"{log_prefix}: Error processing received K-line data: {str(e)}", exc_info=True)
            self.update_status(f"处理K线数据时出错 (来源: {api_description_received}): {str(e)}")
            self.df = pd.DataFrame()
            self.update_chart()
    
    @pyqtSlot(str)
    def on_ohlcv_fetch_error(self, error_msg: str):
        """处理从APIService收到的OHLCV数据获取错误（异步）"""
        current_source = self.token_data.get('source', 'unknown') if self.token_data else 'unknown'
        api_description_error = "未知API来源"
        if current_source == 'historical':
            api_description_error = "自定义API (ohlcv-sync.vercel.app)"
        elif current_source == 'trend':
            api_description_error = "Birdeye API"
        elif current_source == 'holdings':  # 🔥🔥 新增：持仓来源支持
            api_description_error = "Birdeye API (持仓代币)"
        
        logger.error(f"ChartWidget: Failed to fetch OHLCV data from source {current_source} ({api_description_error}): {error_msg}")
        self.update_status(f"获取K线数据失败 ({api_description_error}): {error_msg}")
        self.refresh_button.setEnabled(True)
        self.df = pd.DataFrame() # 清空df
        
        # 在图表区域显示错误信息
        self.show_error_in_chart(f"数据获取失败\n\nAPI来源: {api_description_error}\n错误信息: {error_msg}\n\n请检查网络连接或稍后重试")
        
        # 🔥🔥 修改：确保后台并行组件即使在错误后也保持定时器运行
        if hasattr(self, 'is_background_parallel') and self.is_background_parallel:
            # 后台并行组件：强制保持定时器运行，即使出错也要继续尝试
            if self.auto_refresh and not self.refresh_timer.isActive():
                logger.info(f"ChartWidget: Starting auto-refresh timer for background parallel component after error.")
                self.refresh_timer.start(CHART_REFRESH_INTERVAL)
        else:
            # 前台组件：正常逻辑
            if self.auto_refresh and not self.refresh_timer.isActive():
                self.refresh_timer.start(CHART_REFRESH_INTERVAL)
                logger.info(f"ChartWidget: Auto-refresh timer started for front-end component after error.")
    
    def show_error_in_chart(self, error_message: str):
        """在图表区域显示错误信息"""
        logger.info(f"ChartWidget: Displaying error in chart: {error_message}")
        # 清空所有图表
        self.chart_widget.clear()
        self.macd_widget.clear()
        self.rsi_widget.clear()
        self.volume_widget.clear()
        
        # 移除图例项，而不是整个图例对象
        if hasattr(self, 'chart_legend') and self.chart_legend is not None:
            try:
                # 清除所有图例项，但保留图例对象本身
                items_to_remove = list(self.chart_legend.items)
                for _, item_label_tuple in items_to_remove:
                    if item_label_tuple and hasattr(item_label_tuple, 'text'):
                         self.chart_legend.removeItem(item_label_tuple.text)
                    elif item_label_tuple: # 如果是PlotDataItem等，直接移除
                         self.chart_legend.removeItem(item_label_tuple)
                logger.debug("Chart legend items cleared.")
            except Exception as e_legend_clear:
                logger.error(f"Error clearing chart legend items: {e_legend_clear}")
        
        # 确保十字光标和价格标签在清除后重新添加，并且位于最顶层
        # 移除旧的，以防万一重复添加
        if hasattr(self, 'crosshair_v') and self.crosshair_v.scene(): self.chart_widget.removeItem(self.crosshair_v)
        if hasattr(self, 'crosshair_h') and self.crosshair_h.scene(): self.chart_widget.removeItem(self.crosshair_h)
        if hasattr(self, 'price_label') and self.price_label.scene(): self.chart_widget.removeItem(self.price_label)
        
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        self.chart_widget.addItem(self.price_label, ignoreBounds=True)
        
        # 在图表中央显示错误信息
        # 移除旧的错误文本项（如果存在）
        if hasattr(self, 'error_text_item') and self.error_text_item is not None and self.error_text_item.scene():
            self.chart_widget.removeItem(self.error_text_item)
            self.error_text_item = None
            
        self.error_text_item = pg.TextItem(text=error_message, anchor=(0.5, 0.5))
        self.error_text_item.setFont(QFont("Arial", 12))
        self.error_text_item.setColor((255, 100, 100))  # 红色文字
        
        self.chart_widget.addItem(self.error_text_item, ignoreBounds=True)
        # 将错误文本放在图表视图的中心
        # 获取当前视图范围来定位中心
        view_box = self.chart_widget.getViewBox()
        if view_box:
            x_range, y_range = view_box.viewRange()
            center_x = (x_range[0] + x_range[1]) / 2
            center_y = (y_range[0] + y_range[1]) / 2
            self.error_text_item.setPos(center_x, center_y)
        else:
            self.error_text_item.setPos(0,0) # Fallback if view_box is not available

        self.update_status(error_message.split('\n')[0]) # 更新状态栏为错误信息的第一行
        logger.info("ChartWidget: Error message displayed in chart.")

    def update_chart(self):
        """更新图表显示"""
        if self.df is None or self.df.empty:
            self.chart_widget.clear()
            self.macd_widget.clear()
            self.rsi_widget.clear()
            self.volume_widget.clear()
            if hasattr(self, 'chart_legend') and self.chart_legend is not None:
                try:
                    self.chart_legend.clear() # 清理图例项
                except Exception as e_legend_clear:
                    logger.error(f"Error clearing chart legend: {e_legend_clear}")
            return
        
        # 清空图表
        self.chart_widget.clear()
        self.macd_widget.clear()
        self.rsi_widget.clear()
        self.volume_widget.clear()
        
        # 重新添加十字光标和价格标签
        self.chart_widget.addItem(self.crosshair_v, ignoreBounds=True)
        self.chart_widget.addItem(self.crosshair_h, ignoreBounds=True)
        self.chart_widget.addItem(self.price_label, ignoreBounds=True)
        
        # 获取时间戳列表
        timestamps = self.df['timestamp'].tolist()
        
        # 创建自定义X轴
        axis = TimeAxisItem(timestamps, orientation='bottom')
        self.chart_widget.setAxisItems({'bottom': axis})
        
        # 创建K线图项目
        candlestick = CandlestickItem(self.df)
        self.chart_widget.addItem(candlestick)
        
        # 更新Y轴标签，表明是按十亿代币计算的价格
        self.chart_widget.setLabel('left', '价格 (每十亿代币)', units='$')

        # 添加MA5和MA10 - 同样需要乘以scaling_factor
        scaling_factor = candlestick.scaling_factor
        if 'close' in self.df.columns:
            # 计算MA5和MA10
            self.df['ma5'] = self.df['close'].rolling(window=5).mean()
            self.df['ma10'] = self.df['close'].rolling(window=10).mean()
            
            # 绘制MA5
            ma5_pen = pg.mkPen(color='#2196f3', width=1.5)
            self.chart_widget.plot(
                x=range(len(self.df)), 
                y=self.df['ma5'].values * scaling_factor, 
                pen=ma5_pen, 
                name="MA5"
            )
            
            # 绘制MA10
            ma10_pen = pg.mkPen(color='#ff9800', width=1.5)
            self.chart_widget.plot(
                x=range(len(self.df)), 
                y=self.df['ma10'].values * scaling_factor, 
                pen=ma10_pen, 
                name="MA10"
            )
            
            # 添加布林带到主图表
            if all(col in self.df.columns for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                # 绘制布林带上轨
                bb_upper_pen = pg.mkPen(color='#9c27b0', width=1.5, style=Qt.DashLine)
                self.chart_widget.plot(
                    x=range(len(self.df)), 
                    y=self.df['bb_upper'].values * scaling_factor, 
                    pen=bb_upper_pen, 
                    name="BB Upper"
                )
                
                # 绘制布林带中轨
                bb_middle_pen = pg.mkPen(color='#9c27b0', width=1)
                self.chart_widget.plot(
                    x=range(len(self.df)), 
                    y=self.df['bb_middle'].values * scaling_factor, 
                    pen=bb_middle_pen, 
                    name="BB Middle"
                )
                
                # 绘制布林带下轨
                bb_lower_pen = pg.mkPen(color='#9c27b0', width=1.5, style=Qt.DashLine)
                self.chart_widget.plot(
                    x=range(len(self.df)), 
                    y=self.df['bb_lower'].values * scaling_factor, 
                    pen=bb_lower_pen, 
                    name="BB Lower"
                )
                logger.info("布林带绘制完成")

            # 添加VWAP线到主图表
            if 'vwap' in self.df.columns:
                vwap_pen = pg.mkPen(color='#FFA500', width=3.0, style=Qt.DotLine) # 橙色, 点线, 加粗
                # self.chart_widget.plot(...)
                # 为了图例能正确工作，我们需要将 plot item 存储起来，或者在添加图例时重新创建 PlotDataItem
                self.vwap_plot_item = self.chart_widget.plot(
                    x=range(len(self.df)),
                    y=self.df['vwap'].values * scaling_factor,
                    pen=vwap_pen,
                    name="VWAP"
                )
                logger.info("VWAP线绘制完成")
            else:
                self.vwap_plot_item = None #确保如果不存在，则清空

            # --- 绘制SAR指标 (对应当前图表主要时间周期的SAR) ---
            if self.timeframe != '1m': # 条件：仅当 timeframe 不是 '1m' 时才显示主图的SAR
                if 'sar' in self.df.columns:
                    sar_values_scaled = self.df['sar'].values * scaling_factor 
                    
                    x_indices = np.arange(len(self.df))
                    
                    valid_sar_mask = ~np.isnan(self.df['sar'].values)
                    sar_x = x_indices[valid_sar_mask]
                    sar_y_scaled = sar_values_scaled[valid_sar_mask]

                    if len(sar_x) > 0:
                        if hasattr(self, 'sar_scatter_item') and self.sar_scatter_item is not None:
                            self.chart_widget.removeItem(self.sar_scatter_item)
                            if self.chart_legend and self.sar_scatter_item.name() and self.sar_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                                 try:
                                    self.chart_legend.removeItem(self.sar_scatter_item.name())
                                 except Exception as e_legend_remove:
                                    logger.warning(f"Error removing old SAR from legend: {e_legend_remove}")
                            self.sar_scatter_item = None

                        self.sar_scatter_item = pg.ScatterPlotItem(
                            x=sar_x, 
                            y=sar_y_scaled,
                            symbol='o', 
                            pen=pg.mkPen(None), 
                            brush=pg.mkBrush(color='#FFA500', alpha=150), 
                            size=5,  
                            name="SAR"
                        )
                        self.chart_widget.addItem(self.sar_scatter_item)
                        logger.info(f"SAR指标绘制完成 ({self.timeframe})，共 {len(sar_x)} 个点")
                    else:
                        logger.info(f"SAR指标数据为空或全为NaN ({self.timeframe})，未绘制。")
                        if hasattr(self, 'sar_scatter_item') and self.sar_scatter_item is not None: 
                            self.chart_widget.removeItem(self.sar_scatter_item)
                            if self.chart_legend and self.sar_scatter_item.name() and self.sar_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                                 try:
                                    self.chart_legend.removeItem(self.sar_scatter_item.name())
                                 except Exception as e_legend_remove:
                                    logger.warning(f"Error removing old SAR from legend on empty data: {e_legend_remove}")
                            self.sar_scatter_item = None
                else:
                    logger.info(f"SAR列未在DataFrame中找到 ({self.timeframe})，未绘制SAR指标。")
                    if hasattr(self, 'sar_scatter_item') and self.sar_scatter_item is not None: 
                        self.chart_widget.removeItem(self.sar_scatter_item)
                        if self.chart_legend and self.sar_scatter_item.name() and self.sar_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                            try:
                                self.chart_legend.removeItem(self.sar_scatter_item.name())
                            except Exception as e_legend_remove:
                                logger.warning(f"Error removing old SAR from legend on column not found: {e_legend_remove}")
                        self.sar_scatter_item = None
            else: # 当 timeframe 是 '1m' 时
                logger.info(f"主图SAR指标在1m时间周期下不显示。")
                if hasattr(self, 'sar_scatter_item') and self.sar_scatter_item is not None:
                    self.chart_widget.removeItem(self.sar_scatter_item)
                    if self.chart_legend and self.sar_scatter_item.name() and self.sar_scatter_item.name() in [item[1].text for item in self.chart_legend.items if item[1]]:
                        try:
                            self.chart_legend.removeItem(self.sar_scatter_item.name())
                        except Exception as e_legend_remove:
                            logger.warning(f"Error removing old SAR from legend on 1m timeframe: {e_legend_remove}")
                        self.sar_scatter_item = None
            # --- SAR指标绘制结束 ---

            # --- 绘制选定策略的信号 ---
            if hasattr(self, 'buy_scatter_item') and self.buy_scatter_item:
                self.chart_legend.removeItem(self.buy_scatter_item.name())
                self.chart_widget.removeItem(self.buy_scatter_item)
                self.buy_scatter_item = None
            if hasattr(self, 'sell_scatter_item') and self.sell_scatter_item:
                self.chart_legend.removeItem(self.sell_scatter_item.name())
                self.chart_widget.removeItem(self.sell_scatter_item)
                self.sell_scatter_item = None

            selected_strategy_name_to_draw = self.current_strategy_name 

            
            if selected_strategy_name_to_draw and selected_strategy_name_to_draw != "不显示":
                logger.info(f"ChartWidget: 开始处理策略 '{selected_strategy_name_to_draw}' 的信号")
                try:
                    strategy_instance = StrategyFactory.get_strategy_by_name(selected_strategy_name_to_draw)
                    if strategy_instance:
                        logger.info(f"ChartWidget: 成功获取策略实例: {strategy_instance.name}")
                        
                        # 🔥 新增：获取策略的多时间周期需求
                        primary_timeframe = strategy_instance.get_primary_timeframe()
                        aux_requirements = strategy_instance.get_auxiliary_timeframes_and_indicators()
                        
                        logger.info(f"ChartWidget: 策略 '{selected_strategy_name_to_draw}' 主周期: {primary_timeframe}, 辅助需求: {aux_requirements}")
                        
                        # 🔥 准备基础 DataFrame
                        temp_df_for_signals = self.df.copy()
                        
                        # 🔥 如果当前图表时间周期与策略主周期不同，给出警告
                        if self.timeframe != primary_timeframe:
                            logger.warning(f"ChartWidget: 当前图表周期 {self.timeframe} 与策略主周期 {primary_timeframe} 不匹配")
                            logger.warning(f"ChartWidget: 策略信号可能不准确，建议切换到 {primary_timeframe} 时间周期")
                        
                        # 🔥 处理多时间周期需求
                        if aux_requirements and self.token_data:
                            logger.info(f"ChartWidget: 策略需要辅助时间周期数据，开始获取...")
                            token_address = self.token_data.get('tokenAddress')
                            token_source = self.token_data.get('source', 'unknown')
                            
                            # 🔥 同步获取辅助周期数据（为了保持图表响应性，这里使用同步方式）
                            for aux_tf, aux_indicators_needed in aux_requirements.items():
                                logger.info(f"ChartWidget: 获取辅助周期 {aux_tf} 的数据...")
                                
                                # 🔥 尝试从缓存获取辅助周期数据
                                aux_ohlcv_data = None
                                if self.api_service and hasattr(self.api_service, 'get_cached_ohlcv_data'):
                                    aux_ohlcv_data = self.api_service.get_cached_ohlcv_data(token_address, aux_tf)
                                
                                if aux_ohlcv_data:
                                    logger.info(f"ChartWidget: 从缓存获取到 {len(aux_ohlcv_data)} 条 {aux_tf} 数据")
                                    
                                    # 转换为 DataFrame 并计算指标
                                    aux_df = pd.DataFrame(aux_ohlcv_data)
                                    if not aux_df.empty:
                                        # 确保有必要的时间索引
                                        if 'timestamp' in aux_df.columns:
                                            aux_df['datetime_pd'] = pd.to_datetime(aux_df['timestamp'], unit='s')
                                            aux_df = aux_df.set_index('datetime_pd').sort_index()
                                        
                                        # 计算技术指标
                                        aux_df = TechnicalIndicators.add_all_indicators(aux_df.copy())
                                        
                                        # 🔥 将辅助周期的指标合并到主 DataFrame
                                        # 首先确保主 DataFrame 有时间索引
                                        if 'datetime_pd' not in temp_df_for_signals.columns:
                                            temp_df_for_signals['datetime_pd'] = pd.to_datetime(temp_df_for_signals['timestamp'], unit='s')
                                        if temp_df_for_signals.index.name != 'datetime_pd':
                                            temp_df_for_signals = temp_df_for_signals.set_index('datetime_pd')
                                        
                                        # 合并所需的辅助指标
                                        for indicator_name in aux_indicators_needed:
                                            if indicator_name in aux_df.columns:
                                                # 生成新列名，例如 sar_5m, low_1h
                                                new_col_name = f"{indicator_name}_{aux_tf}"
                                                
                                                # 使用前向填充方法对齐数据
                                                temp_df_for_signals[new_col_name] = aux_df[indicator_name].reindex(
                                                    temp_df_for_signals.index, 
                                                    method='ffill'
                                                )
                                                logger.info(f"ChartWidget: 合并辅助指标 '{new_col_name}'")
                                            else:
                                                logger.warning(f"ChartWidget: 辅助周期 {aux_tf} 缺少指标 '{indicator_name}'")
                                    else:
                                        logger.warning(f"ChartWidget: 辅助周期 {aux_tf} 数据为空")
                                else:
                                    logger.warning(f"ChartWidget: 无法获取辅助周期 {aux_tf} 的数据（缓存未命中）")
                                    logger.warning(f"ChartWidget: 策略信号可能不准确，因为缺少必要的辅助周期数据")
                        
                        # 🔥 调试：输出合并后的 DataFrame 信息
                        if aux_requirements:
                            logger.info(f"ChartWidget: 合并后的 DataFrame 列: {list(temp_df_for_signals.columns)}")
                            # 检查是否成功合并了辅助指标
                            for aux_tf, aux_indicators_needed in aux_requirements.items():
                                for indicator_name in aux_indicators_needed:
                                    col_name = f"{indicator_name}_{aux_tf}"
                                    if col_name in temp_df_for_signals.columns:
                                        non_nan_count = temp_df_for_signals[col_name].notna().sum()
                                        logger.info(f"ChartWidget: 辅助指标 {col_name} 有 {non_nan_count} 个非空值")
                        
                        # 🔥 生成策略信号（现在包含了多时间周期数据）
                        logger.info(f"ChartWidget: 调用策略的generate_signals方法，数据长度: {len(temp_df_for_signals)}")
                        temp_df_for_signals = strategy_instance.generate_signals(temp_df_for_signals)
                        
                        if 'signal' not in temp_df_for_signals.columns:
                            logger.error(f"ChartWidget: 策略 {selected_strategy_name_to_draw} 未生成signal列")
                            return
                        
                        # logger.info(f"ChartWidget: 策略信号生成完成. signal列统计: {temp_df_for_signals['signal'].value_counts().to_dict()}")

                        buy_signals_x, buy_signals_y = [], []
                        sell_signals_x, sell_signals_y = [], []

                        for i in range(len(temp_df_for_signals)):
                            if temp_df_for_signals['signal'].iloc[i] == 1:
                                buy_signals_x.append(i)
                                buy_signals_y.append(temp_df_for_signals['low'].iloc[i] * scaling_factor * 0.998)
                                
                                # 🔥 存储买入信号的精确位置信息
                                try:
                                    if hasattr(temp_df_for_signals.index[i], 'timestamp'):
                                        timestamp = int(temp_df_for_signals.index[i].timestamp())
                                    else:
                                        timestamp = int(temp_df_for_signals['timestamp'].iloc[i])
                                    price = temp_df_for_signals['close'].iloc[i]
                                    
                                    # 存储信号位置：(索引, 价格, 时间戳)
                                    self.current_buy_signals.append((i, price, timestamp))
                                    
                                    # 🔥 调试：验证索引是否正确
                                    # logger.info(f"🔍 发射买入信号调试: DataFrame长度={len(temp_df_for_signals)}, 索引i={i}, close价格={price}")
                                    if i < len(temp_df_for_signals):
                                        actual_close = temp_df_for_signals['close'].iloc[i]
                                        # logger.info(f"   └─ 实际iloc[{i}]的close价格={actual_close}, 匹配={abs(actual_close-price)<0.0001}")
                                    
                                    # 构建并发射买入信号 (SignalData)
                                    if self.token_data:
                                        token_address = self.token_data.get('tokenAddress', 'UnknownTokenAddress')
                                        symbol = self.token_data.get('symbol', 'UnknownSymbol')
                                    else:
                                        token_address = 'UnknownTokenAddress'
                                        symbol = 'UnknownSymbol'
                                        logger.warning("ChartWidget: self.token_data is None, cannot reliably get token_address and symbol for SignalData.")

                                    signal_data = SignalData(
                                        token_address=token_address,
                                        symbol=symbol,
                                        signal_type='buy',
                                        price=price,
                                        timestamp=timestamp, # Assumed to be in seconds
                                        strategy_name=selected_strategy_name_to_draw,
                                        source='chart_strategy',
                                        confidence=1.0, # Or None, assuming chart signals are confident
                                        metadata={'df_index': i, 'chart_timeframe': self.timeframe if hasattr(self, 'timeframe') else 'N/A'}
                                    )
                                    self.trade_signal_generated.emit(signal_data)
                                except Exception as e:
                                    logger.warning(f"发射买入信号时出错: {e}")
                                
                            elif temp_df_for_signals['signal'].iloc[i] == -1:
                                sell_signals_x.append(i)
                                sell_signals_y.append(temp_df_for_signals['high'].iloc[i] * scaling_factor * 1.002)
                                
                                # 🔥 存储卖出信号的精确位置信息
                                try:
                                    if hasattr(temp_df_for_signals.index[i], 'timestamp'):
                                        timestamp = int(temp_df_for_signals.index[i].timestamp())
                                    else:
                                        timestamp = int(temp_df_for_signals['timestamp'].iloc[i])
                                    price = temp_df_for_signals['close'].iloc[i]
                                    
                                    # 存储信号位置：(索引, 价格, 时间戳)
                                    self.current_sell_signals.append((i, price, timestamp))
                                    
                                    # 构建并发射卖出信号 (SignalData)
                                    if self.token_data:
                                        token_address = self.token_data.get('tokenAddress', 'UnknownTokenAddress')
                                        symbol = self.token_data.get('symbol', 'UnknownSymbol')
                                    else:
                                        token_address = 'UnknownTokenAddress'
                                        symbol = 'UnknownSymbol'
                                        logger.warning("ChartWidget: self.token_data is None, cannot reliably get token_address and symbol for SignalData.")

                                    signal_data = SignalData(
                                        token_address=token_address,
                                        symbol=symbol,
                                        signal_type='sell',
                                        price=price,
                                        timestamp=timestamp, # Assumed to be in seconds
                                        strategy_name=selected_strategy_name_to_draw,
                                        source='chart_strategy',
                                        confidence=1.0, # Or None, assuming chart signals are confident
                                        metadata={'df_index': i, 'chart_timeframe': self.timeframe if hasattr(self, 'timeframe') else 'N/A'}
                                    )
                                    self.trade_signal_generated.emit(signal_data)
                                except Exception as e:
                                    logger.warning(f"发射卖出信号时出错: {e}")

                        # logger.info(f"ChartWidget: 找到 {len(buy_signals_x)} 个买入信号，{len(sell_signals_x)} 个卖出信号")
                        # logger.info(f"ChartWidget: 存储了 {len(self.current_buy_signals)} 个买入位置，{len(self.current_sell_signals)} 个卖出位置")

                        if buy_signals_x:
                            self.buy_scatter_item = pg.ScatterPlotItem(
                                x=buy_signals_x, y=buy_signals_y, symbol='t',
                                pen=pg.mkPen(None), brush=pg.mkBrush(QColor("#4caf50")), 
                                size=12, name=f"{selected_strategy_name_to_draw} 买点"
                            )
                            self.chart_widget.addItem(self.buy_scatter_item)
                            logger.info(f"{selected_strategy_name_to_draw} 买入信号绘制完成: {len(buy_signals_x)} 个点")
                        else:
                            logger.info(f"ChartWidget: 策略 {selected_strategy_name_to_draw} 没有买入信号")
                        
                        if sell_signals_x:
                            self.sell_scatter_item = pg.ScatterPlotItem(
                                x=sell_signals_x, y=sell_signals_y, symbol='t', angle=180,
                                pen=pg.mkPen(None), brush=pg.mkBrush(QColor("#f44336")), 
                                size=12, name=f"{selected_strategy_name_to_draw} 卖点"
                            )
                            self.chart_widget.addItem(self.sell_scatter_item)
                            logger.info(f"{selected_strategy_name_to_draw} 卖出信号绘制完成: {len(sell_signals_x)} 个点")
                        else:
                            logger.info(f"ChartWidget: 策略 {selected_strategy_name_to_draw} 没有卖出信号")

                        # 🔥🔥 新增：分析最终信号状态并发射策略分析完成信号
                        final_signal, signal_index = self.interpret_latest_signal(temp_df_for_signals, selected_strategy_name_to_draw)
                        
                        # 发射策略分析完成信号（用于更新趋势表格）
                        if self.token_data:
                            token_address = self.token_data.get('tokenAddress', '')
                            token_symbol = self.token_data.get('symbol', 'Unknown')
                            logger.info(f"🔥 TEST: ChartWidget ({self.objectName()}) {token_symbol} 策略分析完成")
                            logger.info(f"🔥 TEST: 最终信号: {final_signal}, 信号索引: {signal_index}")
                            logger.info(f"🔥 TEST: 准备发射 strategy_analysis_completed 信号")
                            logger.info(f"🔥 TEST: 信号参数 - token_address: {token_address}, token_symbol: {token_symbol}")
                            
                            self.strategy_analysis_completed.emit(token_address, token_symbol, final_signal, signal_index)
                            logger.info(f"🔥 TEST: 已发射 strategy_analysis_completed 信号 - {token_symbol} -> {final_signal}")
                    else:
                        logger.warning(f"未能获取策略实例: {selected_strategy_name_to_draw}")
                except Exception as e:
                    logger.error(f"绘制策略 {selected_strategy_name_to_draw} 信号时出错: {e}", exc_info=True)
            else:
                logger.info(f"ChartWidget: 不绘制策略信号 (current_strategy_name='{selected_strategy_name_to_draw}')")
            # --- 策略信号绘制结束 ---
        
        # 添加MACD指标
        if all(col in self.df.columns for col in ['macd', 'macd_signal', 'macd_diff']):
            # 设置MACD图表的X轴
            macd_axis = TimeAxisItem(timestamps, orientation='bottom')
            self.macd_widget.setAxisItems({'bottom': macd_axis})
            
            # 应用同样的缩放因子到MACD值（可选，根据实际需要调整缩放比例）
            macd_scale = 10000  # MACD通常比价格小，使用较小的缩放因子
            
            # 绘制MACD线
            macd_pen = pg.mkPen(color='#2196f3', width=1.5)
            self.macd_widget.plot(
                x=range(len(self.df)), 
                y=self.df['macd'].values * macd_scale, 
                pen=macd_pen, 
                name="MACD"
            )
            
            # 绘制信号线
            signal_pen = pg.mkPen(color='#ff9800', width=1.5)
            self.macd_widget.plot(
                x=range(len(self.df)), 
                y=self.df['macd_signal'].values * macd_scale, 
                pen=signal_pen, 
                name="Signal"
            )
            
            # 优化：批量绘制MACD柱状图
            if 'macd_diff' in self.df.columns:
                macd_x_coords = np.arange(len(self.df))
                # Ensure macd_heights are calculated correctly, possibly handling NaNs if any
                macd_heights = self.df['macd_diff'].fillna(0).values * macd_scale 
                macd_brushes = []
                # Iterate over the same values used for macd_heights to ensure consistency
                for diff_val_scaled in macd_heights: 
                    if diff_val_scaled >= 0:
                        macd_brushes.append(pg.mkColor(CANDLE_COLORS['up']))
                    else:
                        macd_brushes.append(pg.mkColor(CANDLE_COLORS['down']))
                
                macd_bars = pg.BarGraphItem(
                    x=macd_x_coords,
                    height=macd_heights,
                    width=0.8, 
                    brushes=macd_brushes 
                )
                self.macd_widget.addItem(macd_bars)
            # --- MACD柱状图优化结束 ---
            logger.info("MACD指标绘制完成")
        
        # 添加RSI指标
        if 'rsi' in self.df.columns:
            # 设置RSI图表的X轴
            rsi_axis = TimeAxisItem(timestamps, orientation='bottom')
            self.rsi_widget.setAxisItems({'bottom': rsi_axis})
            
            # 绘制RSI线
            rsi_pen = pg.mkPen(color='#9c27b0', width=1.5)
            self.rsi_widget.plot(
                x=range(len(self.df)), 
                y=self.df['rsi'].values, 
                pen=rsi_pen, 
                name="RSI"
            )
            
            # 绘制超买超卖线
            overbought_pen = pg.mkPen(color='#f44336', width=1, style=Qt.DashLine)
            self.rsi_widget.addLine(y=70, pen=overbought_pen)
            
            oversold_pen = pg.mkPen(color='#4caf50', width=1, style=Qt.DashLine)
            self.rsi_widget.addLine(y=30, pen=oversold_pen)
            
            # 设置Y轴范围
            self.rsi_widget.setYRange(0, 100)
            
            logger.info("RSI指标绘制完成")
        
        # 添加成交量指标
        if 'volume' in self.df.columns:
            # 设置成交量图表的X轴
            volume_axis = TimeAxisItem(timestamps, orientation='bottom')
            self.volume_widget.setAxisItems({'bottom': volume_axis})
            
            self.volume_widget.setTitle("成交量")
            self.volume_widget.setLabel('left', '成交量')
            
            volume_max = self.df['volume'].max()
            volume_scale = 1.0
            if volume_max > *********:
                volume_scale = 0.00000001
                self.volume_widget.setLabel('left', '成交量（亿）')
            elif volume_max > 100000:
                volume_scale = 0.00001
                self.volume_widget.setLabel('left', '成交量（10万）')
            
            logger.info(f"成交量缩放系数: {volume_scale}, 最大成交量: {volume_max}")
            
            # --- 优化成交量柱状图绘制 ---
            x_coords = np.arange(len(self.df))
            heights = self.df['volume'].values * volume_scale
            brushes = []
            for i in range(len(self.df)):
                if self.df['close'].iloc[i] >= self.df['open'].iloc[i]:
                    brushes.append(pg.mkColor(CANDLE_COLORS['up']))
                else:
                    brushes.append(pg.mkColor(CANDLE_COLORS['down']))
            
            volume_bars = pg.BarGraphItem(
                x=x_coords,
                height=heights,
                width=0.8,
                brushes=brushes # 使用颜色列表
            )
            self.volume_widget.addItem(volume_bars)
            # --- 优化结束 ---
            
            if 'volume_ma5' in self.df.columns and 'volume_ma10' in self.df.columns:
                # 绘制成交量MA5
                volume_ma5_pen = pg.mkPen(color='#2196f3', width=1.5)
                self.volume_widget.plot(
                    x=range(len(self.df)), 
                    y=self.df['volume_ma5'].values * volume_scale, 
                    pen=volume_ma5_pen, 
                    name="Volume MA5"
                )
                
                # 绘制成交量MA10
                volume_ma10_pen = pg.mkPen(color='#ff9800', width=1.5)
                self.volume_widget.plot(
                    x=range(len(self.df)), 
                    y=self.df['volume_ma10'].values * volume_scale, 
                    pen=volume_ma10_pen, 
                    name="Volume MA10"
                )
                
            logger.info("成交量指标绘制完成")
        
        # 为MACD图表添加标题和图例
        self.macd_widget.setTitle("MACD指标")
        macd_legend = self.macd_widget.addLegend()
        # macd_legend.addItem(pg.PlotDataItem(pen=pg.mkPen(color='#2196f3', width=1.5)), "MACD") # 这些是示例，实际应引用plot的item
        # macd_legend.addItem(pg.PlotDataItem(pen=pg.mkPen(color='#ff9800', width=1.5)), "Signal")
        
        # 为RSI图表添加标题和图例
        self.rsi_widget.setTitle("RSI指标 (14)")
        # self.rsi_widget.addLegend() 会自动为已命名的plot items创建图例项
        # 在 plot RSI 线时已经指定了 name="RSI"
        if 'rsi' in self.df.columns: # 确保只有在绘制了RSI时才尝试添加图例
            self.rsi_widget.addLegend() 
        
        # 启用Y轴自动范围调整
        self.chart_widget.enableAutoRange(axis='y', enable=True)
        logger.info("Enabled Y-axis auto-ranging.")

        # 设置主图表的X轴范围，技术指标图表会通过信号自动同步
        self.chart_widget.setXRange(0, len(self.df) - 1, padding=0.02)
        logger.info("主图表X轴范围已设置，技术指标图表将自动同步")
    
    def on_mouse_moved(self, pos):
        """
        处理鼠标移动事件
        
        参数:
            pos (QPoint): 鼠标位置
        """
        if self.df is None or self.df.empty:
            return
            
        # 获取鼠标在图表中的位置
        view_pos = self.chart_widget.getViewBox().mapSceneToView(pos)
        x, y = view_pos.x(), view_pos.y()
        
        # 更新十字光标位置
        self.crosshair_v.setPos(x)
        self.crosshair_h.setPos(y)
        
        # 获取最接近的数据点
        x_index = int(x + 0.5)
        if 0 <= x_index < len(self.df):
            # 获取该点的OHLCV数据
            row = self.df.iloc[x_index]
            
            # 更新价格标签 - 显示原始价格 (不乘以scaling_factor)
            time_str = datetime.fromtimestamp(row['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            price_info = (
                f"时间: {time_str}\n"
                f"开盘: ${row['open']:.8f}\n"
                f"最高: ${row['high']:.8f}\n"
                f"最低: ${row['low']:.8f}\n"
                f"收盘: ${row['close']:.8f}\n"
                f"成交量: {row['volume']:.2f}"
            )
            
            self.price_label.setText(price_info)
            self.price_label.setPos(x_index, self.chart_widget.getViewBox().viewRange()[1][1])
    
    def on_timeframe_changed(self, timeframe):
        """
        处理时间周期变更事件
        
        参数:
            timeframe (str): 新的时间周期
        """
        self.timeframe = timeframe
        self.refresh_data()
    
    def on_days_changed(self, days):
        """
        处理天数变更事件
        
        参数:
            days (int): 新的天数
        """
        self.days = days
        self.refresh_data()
    
    def on_auto_refresh_changed(self, state):
        """
        处理自动刷新选项变更事件
        
        参数:
            state (int): 复选框状态
        """
        self.auto_refresh = state == Qt.Checked
        
        if self.auto_refresh:
            # 启动定时器
            self.refresh_timer.start(CHART_REFRESH_INTERVAL)
            logger.info(f"ChartWidget: Auto-refresh timer started for front-end component.")
        else:
            # 停止定时器
            self.refresh_timer.stop()
    
    def calculate_indicators(self, df):
        """
        优化的指标计算方法，计算所有需要的技术指标
        
        参数:
            df (pd.DataFrame): 原始OHLCV数据
            
        返回:
            pd.DataFrame: 添加了指标的DataFrame
        """
        if df.empty:
            return df

        df_with_indicators = TechnicalIndicators.add_all_indicators(df.copy()) # 使用副本确保原始df不被修改，除非明确需要
        

        return df_with_indicators # 返回带有所有指标的DataFrame
        
    def on_strategy_changed(self, strategy_name: str):
        """处理策略选择变化事件"""
        logger.info(f"ChartWidget: 内部策略选择变更为: {strategy_name}")
        self.current_strategy_name = strategy_name # 更新当前策略名
        
        # 🔥 新增：检查新策略是否需要辅助时间周期数据
        if strategy_name and strategy_name != "不显示" and self.token_data:
            try:
                strategy_instance = StrategyFactory.get_strategy_by_name(strategy_name)
                if strategy_instance:
                    primary_timeframe = strategy_instance.get_primary_timeframe()
                    aux_requirements = strategy_instance.get_auxiliary_timeframes_and_indicators()
                    
                    # 🔥 如果当前图表时间周期与策略主周期不匹配，提示用户
                    if self.timeframe != primary_timeframe:
                        logger.info(f"ChartWidget: 策略 '{strategy_name}' 建议使用 {primary_timeframe} 时间周期")
                        self.update_status(f"💡 提示：策略 '{strategy_name}' 建议使用 {primary_timeframe} 时间周期")
                    
                    # 🔥 如果策略需要辅助时间周期，预先触发数据获取
                    if aux_requirements and self.api_service:
                        token_address = self.token_data.get('tokenAddress')
                        token_source = self.token_data.get('source', 'unknown')
                        
                        logger.info(f"ChartWidget: 策略 '{strategy_name}' 需要辅助数据: {list(aux_requirements.keys())}")
                        
                        # 异步获取所有需要的辅助时间周期数据
                        for aux_tf in aux_requirements.keys():
                            # 先检查缓存
                            cached_aux_data = self.api_service.get_cached_ohlcv_data(token_address, aux_tf)
                            if not cached_aux_data:
                                logger.info(f"ChartWidget: 预加载 {aux_tf} 时间周期数据")
                                self.api_service.get_ohlcv_data_async(
                                    token_address=token_address,
                                    timeframe=aux_tf,
                                    days=self.days,
                                    source=token_source
                                )
                            else:
                                logger.info(f"ChartWidget: {aux_tf} 数据已在缓存中")
                        
                        if not any(self.api_service.get_cached_ohlcv_data(token_address, aux_tf) 
                                  for aux_tf in aux_requirements.keys()):
                            self.update_status(f"正在预加载策略所需的多时间周期数据...")
            except Exception as e:
                logger.error(f"ChartWidget: 处理策略变化时出错: {e}")
        
        if self.df is not None and not self.df.empty:
            self.update_chart() 
        else:
            logger.info("ChartWidget: 数据尚未加载，策略变更暂不更新图表。")

    def update_status(self, message):
        """
        更新状态消息
        
        参数:
            message (str): 状态消息
        """
        self.status_label.setText(message)

    def set_context_data(self, token_list: List[Dict], current_index: int):
        self.context_token_list = token_list
        self.current_token_index_in_context = current_index
        self._update_navigation_buttons_state() # 更新按钮状态

    def _update_navigation_buttons_state(self):
        if not self.context_token_list or len(self.context_token_list) <= 1:
            self.prev_button.setEnabled(False)
            self.next_button.setEnabled(False)
            return

        self.prev_button.setEnabled(self.current_token_index_in_context > 0)
        self.next_button.setEnabled(self.current_token_index_in_context < len(self.context_token_list) - 1)

    def _navigate_token(self, direction: int):
        if not self.context_token_list: 
            return
        
        new_index = self.current_token_index_in_context + direction
        
        if 0 <= new_index < len(self.context_token_list):
            self.current_token_index_in_context = new_index
            new_token_data = self.context_token_list[self.current_token_index_in_context]
            # 需要确保 new_token_data 包含 'source' 字段，以便 set_token 正确工作
            # 如果 context_list 中的项可能没有 source，需要从当前 ChartWidget 的 token_data 或 MainWindow 获取
            # 假设传递过来的 context_list 中的字典与 self.token_data 结构一致，包含 source
            if 'source' not in new_token_data and self.token_data and 'source' in self.token_data:
                new_token_data['source'] = self.token_data.get('source') # 尝试从当前token继承source
            
            self.set_token(new_token_data)
            self._update_navigation_buttons_state()
        else:
            logger.info("Navigation out of bounds by keyboard/button.")

    def show_previous_token(self):
        self._navigate_token(-1)

    def show_next_token(self):
        self._navigate_token(1)
    
    def keyPressEvent(self, event: QKeyEvent): # Add type hint for event
        """处理键盘事件"""
        if event.key() == Qt.Key_Left:
            self.show_previous_token()
        elif event.key() == Qt.Key_Right:
            self.show_next_token()
        else:
            super().keyPressEvent(event)

    def highlight_signal_point_by_index(self, chart_index: int, signal_type: str, price: float, timestamp: int):
        """使用精确索引开始闪烁指定的信号点 - 直接使用索引，避免时间戳查找的误差"""
        try:
            logger.info(f"🔥 highlight_signal_point_by_index 调用: 精确索引={chart_index}, 类型={signal_type}")
            
            # 🔥 直接使用传入的精确索引，不再进行时间戳查找！
            if self.df is None or self.df.empty:
                logger.warning("数据框为空，无法闪烁信号点")
                self.update_status("⚠️ 图表数据未加载")
                return
            
            # 验证索引的有效性
            if chart_index < 0 or chart_index >= len(self.df):
                logger.error(f"索引超出范围: {chart_index}, 数据长度: {len(self.df)}")
                self.update_status(f"❌ 索引无效: {chart_index}")
                return
            
            # 停止之前的闪烁
            self.stop_signal_highlight()
            
            # 设置闪烁参数
            self.highlight_timestamp = timestamp
            self.highlight_signal_type = signal_type
            self.highlight_price = price
            self.highlight_active = True
            self.highlight_visible = True
            
            # 🔥 调试：验证高亮索引是否正确
            if chart_index < len(self.df):
                actual_close = self.df['close'].iloc[chart_index]
                actual_high = self.df['high'].iloc[chart_index]
                actual_low = self.df['low'].iloc[chart_index]
                logger.info(f"🔍 高亮调试: 索引{chart_index}, 传入价格={price}")
                logger.info(f"   └─ 实际K线数据: close={actual_close}, high={actual_high}, low={actual_low}")
                logger.info(f"   └─ 价格匹配度: close差值={abs(actual_close-price)}, 在high-low范围内={actual_low <= price <= actual_high}")
            
            # 🔥 直接使用精确索引创建闪烁点
            self.create_highlight_scatter(chart_index, signal_type, price)
            
            # 开始闪烁定时器
            self.highlight_timer.start(800)
            
            # 自动调整视图以显示信号点
            self.center_view_on_signal(chart_index)
            
            # 记录成功信息
            signal_type_zh = "买入" if signal_type == 'buy' else "卖出"
            timestamp_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
            logger.info(f"✅ 使用精确索引闪烁{signal_type_zh}信号点: 索引{chart_index} (时间: {timestamp_str})")
            
            # 更新状态显示
            self.update_status(f"🎯 正在闪烁显示 {signal_type_zh}信号 (精确索引: {chart_index}, 时间: {timestamp_str})")
            
        except Exception as e:
            logger.error(f"使用精确索引闪烁信号点失败: {e}")
            self.update_status(f"❌ 精确闪烁显示失败: {str(e)}")

    def highlight_signal_point(self, timestamp: int, signal_type: str, price: float):
        """开始闪烁指定的信号点 - 使用精确的信号位置数据"""
        try:
            # 停止之前的闪烁
            self.stop_signal_highlight()
            
            # 🔥 优先使用存储的精确信号位置进行匹配
            exact_match_found = False
            target_index = None
            
            # 根据信号类型选择对应的信号列表
            signal_list = self.current_buy_signals if signal_type == 'buy' else self.current_sell_signals
            signal_type_zh = "买入" if signal_type == 'buy' else "卖出"
            
            logger.info(f"🔍 查找{signal_type_zh}信号: 目标时间戳={timestamp}, 可用信号数量={len(signal_list)}")
            
            # 在存储的信号中查找精确匹配
            for stored_index, stored_price, stored_timestamp in signal_list:
                if stored_timestamp == timestamp:
                    # 找到精确匹配！
                    target_index = stored_index
                    exact_match_found = True
                    logger.info(f"✅ 找到精确匹配的{signal_type_zh}信号: 索引={target_index}, 价格={stored_price}")
                    break
            
            # 如果没有找到精确匹配，查找最接近的时间戳
            if not exact_match_found and signal_list:
                logger.info(f"⚠️ 未找到精确匹配，查找最接近的{signal_type_zh}信号...")
                min_diff = float('inf')
                best_match = None
                
                for stored_index, stored_price, stored_timestamp in signal_list:
                    time_diff = abs(stored_timestamp - timestamp)
                    if time_diff < min_diff:
                        min_diff = time_diff
                        best_match = (stored_index, stored_price, stored_timestamp)
                
                if best_match and min_diff <= 300:  # 容差5分钟
                    target_index = best_match[0]
                    logger.info(f"📍 使用最接近的{signal_type_zh}信号: 索引={target_index}, 时间差={min_diff}秒")
                else:
                    logger.warning(f"❌ 没有找到匹配的{signal_type_zh}信号 (最小时间差: {min_diff}秒)")
            
            # 设置闪烁参数
            self.highlight_timestamp = timestamp
            self.highlight_signal_type = signal_type
            self.highlight_price = price
            self.highlight_active = True
            self.highlight_visible = True
            
            if target_index is not None and self.df is not None and not self.df.empty:
                # 使用找到的精确索引创建闪烁点
                self.create_highlight_scatter(target_index, signal_type, price)
                
                # 开始闪烁定时器
                self.highlight_timer.start(800)
                
                # 自动调整视图以显示信号点
                self.center_view_on_signal(target_index)
                
                match_type = "精确匹配" if exact_match_found else "最近匹配"
                logger.info(f"✅ 开始闪烁{signal_type_zh}信号点: 索引{target_index} ({match_type})")
                
                # 更新状态显示
                timestamp_str = datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
                self.update_status(f"🔴 正在闪烁显示 {signal_type_zh}信号 ({match_type}, 索引: {target_index}, 时间: {timestamp_str})")
            else:
                # 没有找到匹配的信号点
                logger.warning(f"⚠️ 未找到匹配的{signal_type_zh}信号点")
                self.update_status(f"⚠️ 无法定位{signal_type_zh}信号点")
                
        except Exception as e:
            logger.error(f"闪烁{signal_type_zh}信号点失败: {e}")
            self.update_status(f"❌ 闪烁显示失败: {str(e)}")



    def center_view_on_signal(self, signal_index: int):
        """将视图居中到信号点位置，并智能调整显示范围"""
        try:
            if self.df is None or self.df.empty:
                return
                
            total_candles = len(self.df)
            
            # 🔥 智能计算视图宽度：根据不同时间周期和数据量动态调整
            timeframe_view_settings = {
                '1m': {'default': 60, 'min': 40, 'max': 120},    # 1分钟：显示1-2小时数据
                '5m': {'default': 48, 'min': 30, 'max': 96},     # 5分钟：显示4-8小时数据  
                '15m': {'default': 32, 'min': 24, 'max': 64},    # 15分钟：显示6-16小时数据
                '1h': {'default': 24, 'min': 18, 'max': 48},     # 1小时：显示18-48小时数据
                '4h': {'default': 18, 'min': 12, 'max': 36},     # 4小时：显示2-6天数据
                '1d': {'default': 15, 'min': 10, 'max': 30}      # 1天：显示10-30天数据
            }
            
            # 获取当前时间周期的设置，默认使用5分钟设置
            view_config = timeframe_view_settings.get(self.timeframe, timeframe_view_settings['5m'])
            
            # 🔥 根据数据量调整视图宽度
            if total_candles < view_config['min']:
                # 数据量很少，显示全部
                view_width = total_candles
            elif total_candles <= view_config['default'] * 2:
                # 数据量适中，使用默认设置
                view_width = view_config['default']
            else:
                # 数据量很多，使用较大的显示范围以便分析
                view_width = min(view_config['max'], total_candles // 3)
            
            # 🔥 优化信号点位置：让信号点稍微偏左显示，右侧留更多空间观察后续走势
            left_ratio = 0.4   # 信号点位于40%位置，左侧40%，右侧60%
            left_width = int(view_width * left_ratio)
            right_width = view_width - left_width
            
            # 计算起始和结束索引
            start_index = max(0, signal_index - left_width)
            end_index = min(total_candles - 1, signal_index + right_width)
            
            # 🔥 边界处理：确保视图宽度保持一致
            actual_width = end_index - start_index + 1
            if actual_width < view_width:
                # 如果因为边界限制导致宽度不足，尝试调整
                if start_index == 0:
                    # 已经到达左边界，尝试向右扩展
                    end_index = min(total_candles - 1, start_index + view_width - 1)
                elif end_index == total_candles - 1:
                    # 已经到达右边界，尝试向左扩展
                    start_index = max(0, end_index - view_width + 1)
            
            # 🔥 计算合适的Y轴范围：确保信号点及其周围价格清晰可见
            y_range = self.calculate_optimal_y_range(start_index, end_index, signal_index)
            
            # 设置X轴范围（添加少量padding让边界更美观）
            x_padding = view_width * 0.02  # 2%的padding
            self.chart_widget.setXRange(
                start_index - x_padding, 
                end_index + x_padding, 
                padding=0
            )
            
            # 🔥 设置Y轴范围，确保信号点价格区域清晰可见
            if y_range:
                y_padding = (y_range[1] - y_range[0]) * 0.1  # 10%的Y轴padding
                self.chart_widget.setYRange(
                    y_range[0] - y_padding,
                    y_range[1] + y_padding,
                    padding=0
                )
            
            # 🔥 同步技术指标图表的X轴
            self.sync_indicator_charts_to_range(start_index - x_padding, end_index + x_padding)
            
            # 记录调整信息
            signal_time = datetime.fromtimestamp(self.df.iloc[signal_index]['timestamp']).strftime('%H:%M:%S')
            logger.info(f"📊 智能缩放到信号点: 索引{signal_index} ({signal_time})")
            logger.info(f"   └─ 时间周期: {self.timeframe}, 显示范围: {start_index}-{end_index} ({actual_width}根K线)")
            logger.info(f"   └─ 信号位置: {left_ratio*100:.0f}% (左侧{left_width}根，右侧{right_width}根)")
            if y_range:
                logger.info(f"   └─ 价格范围: ${y_range[0]:.6f} - ${y_range[1]:.6f}")
            
            # 更新状态显示
            self.update_status(f"🎯 已缩放到信号点 ({signal_time}) - 显示{actual_width}根{self.timeframe}K线")
            
        except Exception as e:
            logger.error(f"智能缩放到信号点失败: {e}")
            self.update_status(f"❌ 缩放失败: {str(e)}")

    def calculate_optimal_y_range(self, start_index: int, end_index: int, signal_index: int) -> Optional[tuple]:
        """计算最优的Y轴显示范围，确保信号点区域清晰可见"""
        try:
            if self.df is None or self.df.empty:
                return None
            
            # 确保索引在有效范围内
            start_index = max(0, start_index)
            end_index = min(len(self.df) - 1, end_index)
            
            if start_index >= end_index:
                return None
            
            # 获取显示范围内的价格数据
            visible_data = self.df.iloc[start_index:end_index + 1]
            
            if visible_data.empty:
                return None
            
            # 🔥 获取高低价范围
            price_high = visible_data['high'].max()
            price_low = visible_data['low'].min()
            
            # 🔥 获取信号点的价格，确保它在可见范围内
            if 0 <= signal_index < len(self.df):
                signal_candle = self.df.iloc[signal_index]
                signal_high = signal_candle['high']
                signal_low = signal_candle['low']
                
                # 扩展范围以确保信号点清晰可见
                price_high = max(price_high, signal_high)
                price_low = min(price_low, signal_low)
            
            # 🔥 考虑技术指标的显示需求
            # 如果有布林带，确保上下轨都能显示
            if 'bb_upper' in visible_data.columns and 'bb_lower' in visible_data.columns:
                bb_upper_max = visible_data['bb_upper'].max()
                bb_lower_min = visible_data['bb_lower'].min()
                if pd.notna(bb_upper_max) and pd.notna(bb_lower_min):
                    price_high = max(price_high, bb_upper_max)
                    price_low = min(price_low, bb_lower_min)
            
            # 🔥 考虑VWAP线
            if 'vwap' in visible_data.columns:
                vwap_max = visible_data['vwap'].max()
                vwap_min = visible_data['vwap'].min()
                if pd.notna(vwap_max) and pd.notna(vwap_min):
                    price_high = max(price_high, vwap_max)
                    price_low = min(price_low, vwap_min)
            
            # 🔥 应用缩放因子（与图表绘制保持一致）
            scaling_factor = 1_000_000_000
            y_high = price_high * scaling_factor
            y_low = price_low * scaling_factor
            
            return (y_low, y_high)
            
        except Exception as e:
            logger.error(f"计算最优Y轴范围失败: {e}")
            return None

    def sync_indicator_charts_to_range(self, x_start: float, x_end: float):
        """将技术指标图表同步到指定的X轴范围"""
        try:
            # 同步所有技术指标图表的X轴范围
            self.macd_widget.setXRange(x_start, x_end, padding=0)
            self.rsi_widget.setXRange(x_start, x_end, padding=0)
            self.volume_widget.setXRange(x_start, x_end, padding=0)
            
            logger.debug(f"已同步技术指标图表X轴范围: {x_start:.2f} - {x_end:.2f}")
            
        except Exception as e:
            logger.error(f"同步技术指标图表X轴失败: {e}")

    def find_candle_index_by_timestamp(self, target_timestamp: int) -> Optional[int]:
        """根据时间戳查找对应的K线索引"""
        try:
            if self.df is None or self.df.empty or 'timestamp' not in self.df.columns:
                logger.warning("find_candle_index_by_timestamp: DataFrame为空或缺少timestamp列")
                return None
            
            # 获取时间戳数组
            timestamps = self.df['timestamp'].values
            
            # 添加调试信息
            target_time_str = datetime.fromtimestamp(target_timestamp).strftime('%Y-%m-%d %H:%M:%S')
            first_time_str = datetime.fromtimestamp(timestamps[0]).strftime('%Y-%m-%d %H:%M:%S')
            last_time_str = datetime.fromtimestamp(timestamps[-1]).strftime('%Y-%m-%d %H:%M:%S')
            
            logger.info(f"查找时间戳匹配: 目标时间={target_time_str} ({target_timestamp})")
            logger.info(f"K线数据时间范围: {first_time_str} ({timestamps[0]}) 到 {last_time_str} ({timestamps[-1]})")
            logger.info(f"数据总量: {len(timestamps)} 条")
            
            # 使用numpy的searchsorted进行二分查找，这比循环更高效
            import numpy as np
            
            # 找到最接近的索引
            insert_index = np.searchsorted(timestamps, target_timestamp)
            
            # 检查边界情况
            if insert_index == 0:
                closest_index = 0
            elif insert_index == len(timestamps):
                closest_index = len(timestamps) - 1
            else:
                # 比较前后两个时间戳，选择更接近的
                left_diff = abs(timestamps[insert_index - 1] - target_timestamp)
                right_diff = abs(timestamps[insert_index] - target_timestamp)
                closest_index = insert_index - 1 if left_diff <= right_diff else insert_index
            
            # 计算时间差
            min_diff = abs(timestamps[closest_index] - target_timestamp)
            matched_time_str = datetime.fromtimestamp(timestamps[closest_index]).strftime('%Y-%m-%d %H:%M:%S')
            
            logger.info(f"找到最接近的K线: 索引={closest_index}, 时间={matched_time_str}, 时间差={min_diff}秒")
            
            # 根据时间周期调整容差
            timeframe_tolerance = {
                '1m': 120,     # 2分钟容差（放宽一些）
                '5m': 600,     # 10分钟容差
                '15m': 1800,   # 30分钟容差
                '1h': 7200,    # 2小时容差
                '4h': 28800,   # 8小时容差
                '1d': 172800   # 2天容差
            }
            
            tolerance = timeframe_tolerance.get(self.timeframe, 600)  # 默认10分钟
            
            if min_diff <= tolerance:
                logger.info(f"时间差在容差范围内({tolerance}秒)，匹配成功")
                return closest_index
            else:
                logger.warning(f"时间差({min_diff}秒)超出容差范围({tolerance}秒)")
                # 即使超出容差，也返回最接近的索引，但记录警告
                logger.warning(f"使用最接近的索引: {closest_index}")
                return closest_index
            
        except Exception as e:
            logger.error(f"查找K线索引失败: {e}", exc_info=True)
            return None

    def create_highlight_scatter(self, index: int, signal_type: str, price: float):
        """创建闪烁的散点图项"""
        try:
            # 移除之前的闪烁项
            if self.highlight_scatter_item:
                self.chart_widget.removeItem(self.highlight_scatter_item)
                self.highlight_scatter_item = None
            
            # 获取与三角买卖点相同的缩放因子
            scaling_factor = 1_000_000_000  # 与CandlestickItem中的scaling_factor保持一致
            
            # 添加调试信息
            logger.info(f"创建闪烁散点: index={index}, signal_type={signal_type}, price={price}")
            logger.info(f"K线数据长度: {len(self.df)}, 索引范围: 0-{len(self.df)-1}")
            
            # 检查索引有效性
            if index < 0 or index >= len(self.df):
                logger.error(f"索引超出范围: {index}, 数据长度: {len(self.df)}")
                return
            
            # 获取该索引处的K线数据
            candle_data = self.df.iloc[index]
            logger.info(f"索引{index}的K线数据: open={candle_data['open']}, high={candle_data['high']}, low={candle_data['low']}, close={candle_data['close']}")
            
            # 确定颜色和位置，使用与三角买卖点完全相同的计算方式
            if signal_type == 'buy':
                color = '#00ff88'  # 亮绿色 - 买入信号
                symbol = 'o'  # 圆形
                # 使用与三角买入点相同的位置计算：low * scaling_factor * 0.995
                y_position = candle_data['low'] * scaling_factor * 0.995  # 稍微调低一点，避免重叠
                logger.info(f"买入信号: 绿色圆点, Y位置={y_position} (low={candle_data['low']} * {scaling_factor} * 0.995)")
            else:  # sell
                color = '#ff4444'  # 亮红色 - 卖出信号
                symbol = 'o'  # 圆形
                # 使用与三角卖出点相同的位置计算：high * scaling_factor * 1.005
                y_position = candle_data['high'] * scaling_factor * 1.005  # 稍微调高一点，避免重叠
                logger.info(f"卖出信号: 红色圆点, Y位置={y_position} (high={candle_data['high']} * {scaling_factor} * 1.005)")
            
            # 创建更醒目的散点图项
            self.highlight_scatter_item = pg.ScatterPlotItem(
                x=[index],
                y=[y_position],
                size=25,  # 增大尺寸
                pen=pg.mkPen(color='white', width=2),  # 白色边框
                brush=pg.mkBrush(color),
                symbol=symbol
            )
            
            # 添加到图表
            self.chart_widget.addItem(self.highlight_scatter_item)
            
            logger.info(f"✅ 闪烁散点创建成功: X={index}, Y={y_position}, 颜色={color}")
            
        except Exception as e:
            logger.error(f"创建闪烁散点图失败: {e}", exc_info=True)

    def toggle_highlight(self):
        """切换闪烁状态"""
        try:
            if not self.highlight_active or not self.highlight_scatter_item:
                return
            
            # 切换可见性
            self.highlight_visible = not self.highlight_visible
            
            if self.highlight_visible:
                # 显示闪烁点 - 完全不透明，略微放大
                self.highlight_scatter_item.setOpacity(1.0)
                # 稍微改变大小来增强闪烁效果
                self.highlight_scatter_item.setData(size=28)
            else:
                # 隐藏闪烁点 - 降低透明度，恢复原始大小
                self.highlight_scatter_item.setOpacity(0.4)
                self.highlight_scatter_item.setData(size=22)
                
        except Exception as e:
            logger.error(f"切换闪烁状态失败: {e}")

    def stop_signal_highlight(self):
        """停止信号点闪烁"""
        try:
            was_active = self.highlight_active
            
            # 停止定时器
            if self.highlight_timer.isActive():
                self.highlight_timer.stop()
            
            # 移除闪烁项
            if self.highlight_scatter_item:
                self.chart_widget.removeItem(self.highlight_scatter_item)
                self.highlight_scatter_item = None
            
            # 重置状态
            self.highlight_active = False
            self.highlight_visible = False
            self.highlight_timestamp = None
            self.highlight_signal_type = None
            self.highlight_price = None
            
            if was_active:
                logger.info("⏹️ 停止信号点闪烁")
                self.update_status("已停止信号点闪烁显示")
            
        except Exception as e:
            logger.error(f"停止信号点闪烁失败: {e}")

    def sync_indicator_charts(self):
        """同步技术指标图表的X轴范围与主图表保持一致"""
        try:
            # 如果正在进行信号点闪烁，不执行自动同步（避免与智能缩放冲突）
            if self.highlight_active:
                logger.debug("正在闪烁信号点，跳过自动同步以避免冲突")
                return
                
            # 获取主图表的X轴视图范围
            view_box = self.chart_widget.getViewBox()
            if view_box and self.df is not None and not self.df.empty:
                x_range, _ = view_box.viewRange()
                
                # 同步所有技术指标图表的X轴范围
                self.macd_widget.setXRange(x_range[0], x_range[1], padding=0)
                self.rsi_widget.setXRange(x_range[0], x_range[1], padding=0)
                self.volume_widget.setXRange(x_range[0], x_range[1], padding=0)
                
                logger.debug(f"同步技术指标图表X轴范围: {x_range[0]:.2f} - {x_range[1]:.2f}")
            
        except Exception as e:
            logger.error(f"同步技术指标图表失败: {e}")

    def get_current_token_data(self) -> Optional[Dict]:
        """返回当前图表正在显示的代币数据"""
        return self.token_data

    def get_current_timeframe(self) -> str:
        """获取当前时间周期"""
        return self.timeframe

    def display_provided_ohlcv(self, ohlcv_data: list, timeframe: str, source_description: str):
        """显示外部提供的OHLCV数据"""
        log_prefix = f"ChartWidget ({self.token_data.get('symbol', 'N/A') if self.token_data else 'Unset'} for {timeframe})"
        # logger.info(f"{log_prefix}: Received provided OHLCV data from '{source_description}'. Points: {len(ohlcv_data) if ohlcv_data else 0} at {timestamp_received:.2f}")

        # 🔥🔥 修改可见性检查：后台并行组件即使不可见也要处理数据
        if not self.isVisible() and not self.is_background_parallel: 
            logger.debug(f"{log_prefix}: Widget not visible and not a background parallel component. Ignoring provided OHLCV data.")
            return

        if not self.token_data:
            logger.warning(f"{log_prefix}: No token set in ChartWidget. Cannot display provided OHLCV data.")
            return

        try:
            if not ohlcv_data or len(ohlcv_data) == 0:
                logger.warning(f"{log_prefix}: Provided OHLCV data list is empty.")
                self.df = pd.DataFrame()
                self.update_chart()
                return

            # 转换为DataFrame并计算指标
            self.df = pd.DataFrame(ohlcv_data)
            if not self.df.empty:
                self.df = self.calculate_indicators(self.df)
                self.update_chart()
                
                # 更新显示信息
                current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.last_update_label.setText(f"上次更新: {current_time_str} (来自{source_description})")
                self.update_status(f"已加载 {len(self.df)} 条K线数据 (来源: {source_description})")

            # 🔥🔥 修改：对于后台并行组件，即使提供了外部数据，也要保持自动刷新
            # 对于非后台组件，停止定时器（保持原有逻辑）
            if hasattr(self, 'is_background_parallel') and self.is_background_parallel:
                # 后台并行组件：保持定时器运行，确保持续更新
                if self.auto_refresh and not self.refresh_timer.isActive():
                    logger.info(f"{log_prefix}: Starting auto-refresh timer for background parallel component.")
                    self.refresh_timer.start(CHART_REFRESH_INTERVAL)
            else:
                # 前台组件：停止定时器（原有逻辑）
                if self.refresh_timer.isActive():
                    logger.info(f"{log_prefix}: Stopping auto-refresh timer as data was provided externally.")
                    self.refresh_timer.stop()

        except Exception as e:
            logger.error(f"{log_prefix}: Error processing provided K-line data: {str(e)}", exc_info=True)
            self.update_status(f"处理提供K线数据时出错: {str(e)}")
            self.df = pd.DataFrame()
            self.update_chart() # 这会清空图表或显示错误
    
    def try_get_cached_ohlcv_data(self, token_address: str, timeframe: str) -> Optional[List[Dict]]:
        """尝试从 APIService 获取缓存的 OHLCV 数据"""
        try:
            # 🔥 尝试使用 APIService 实例的缓存
            if self.api_service and hasattr(self.api_service, 'get_cached_ohlcv_data'):
                logger.info(f"ChartWidget: 尝试从 APIService 实例获取缓存数据")
                cached_data = self.api_service.get_cached_ohlcv_data(token_address, timeframe)
                if cached_data:
                    logger.info(f"ChartWidget: 从 APIService 实例成功获取缓存数据，长度: {len(cached_data)}")
                    return cached_data
                else:
                    logger.info(f"ChartWidget: APIService 实例中没有缓存数据")
            else:
                logger.info(f"ChartWidget: APIService 实例不可用或没有缓存方法")
            
            return None
                    
        except Exception as e:
            logger.error(f"ChartWidget: 获取缓存数据失败: {e}")
            return None
    
    def interpret_latest_signal(self, df: pd.DataFrame, strategy_name: str) -> tuple:
        """
        解释最新的策略信号状态
        
        参数:
            df (pd.DataFrame): 包含信号列的数据框
            strategy_name (str): 策略名称
            
        返回:
            tuple: (最终信号状态, 信号在K线中的位置索引)
        """
        try:
            token_symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            
            if df is None or df.empty:
                logger.debug(f"🔍 {token_symbol}: DataFrame为空，返回观察")
                return ("观察", -1)
                
            if 'signal' not in df.columns:
                logger.debug(f"🔍 {token_symbol}: DataFrame缺少signal列，返回观察")
                return ("观察", -1)
            
            # 获取最近的信号
            signals = df['signal'].values
            if len(signals) == 0:
                logger.debug(f"🔍 {token_symbol}: signal数组为空，返回观察")
                return ("观察", -1)
            
            # 🔥 找到所有非零信号的位置
            buy_positions = []
            sell_positions = []
            
            for i in range(len(signals)):
                if signals[i] == 1:  # 买入信号
                    buy_positions.append(i)
                elif signals[i] == -1:  # 卖出信号
                    sell_positions.append(i)
            
            logger.debug(f"🔍 {token_symbol}: 信号统计 - 总数据点: {len(signals)}, 买入信号: {len(buy_positions)}, 卖出信号: {len(sell_positions)}")
            
            # 如果没有任何信号
            if not buy_positions and not sell_positions:
                logger.debug(f"🔍 {token_symbol}: 没有买入或卖出信号，返回观察")
                return ("观察", -1)
            
            # 确定最后一个信号
            last_buy_pos = buy_positions[-1] if buy_positions else -1
            last_sell_pos = sell_positions[-1] if sell_positions else -1
            
            logger.debug(f"🔍 {token_symbol}: 最后买入位置: {last_buy_pos}, 最后卖出位置: {last_sell_pos}")
            
            if last_buy_pos > last_sell_pos:
                # 最后一个信号是买入
                logger.info(f"📊 {token_symbol}: 最终信号 -> 买入 (最近的买入信号) @ 位置 {last_buy_pos}")
                return ("买入", last_buy_pos)
            elif last_sell_pos > last_buy_pos:
                # 最后一个信号是卖出
                logger.info(f"📊 {token_symbol}: 最终信号 -> 卖出 (最近的卖出信号) @ 位置 {last_sell_pos}")
                return ("卖出", last_sell_pos)
            else:
                # 两个位置相等（不应该发生）或都是-1
                logger.debug(f"🔍 {token_symbol}: 买入卖出位置相等或都为-1，返回观察")
                return ("错误", -1)
                
        except Exception as e:
            token_symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            logger.error(f"🔍 {token_symbol}: 解释信号状态时出错: {e}")
            return ("观察", -1)
    
    def clear_data(self):
        """
        Clears all data and resets the chart to a clean state.
        Ready to load a new token.
        """
        logger.info(f"ChartWidget ({self.objectName()}): Clearing data for token {self.token_data.get('symbol') if self.token_data else 'N/A'})")
        
        # 1. Stop timers
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        if hasattr(self, 'highlight_timer'):
            self.highlight_timer.stop()
            self.highlight_active = False
            if self.highlight_scatter_item:
                try:
                    if self.price_plot: # Assuming price_plot is the main PlotItem
                        self.price_plot.removeItem(self.highlight_scatter_item)
                except Exception as e:
                    logger.warning(f"ChartWidget ({self.objectName()}): Error removing highlight_scatter_item: {e}")
                self.highlight_scatter_item = None

        # 2. Clear data stores
        self.token_data = None
        self.ohlcv_data = None
        self.df = None
        self.current_buy_signals.clear()
        self.current_sell_signals.clear()

        # 3. Clear plotted items (assuming pyqtgraph structure)
        # These depend heavily on how items are stored and added in init_ui/plot_data
        
        # Main price plot items
        if hasattr(self, 'price_plot') and self.price_plot:
            # Candlestick
            if hasattr(self, 'candlestick_item') and self.candlestick_item:
                self.price_plot.removeItem(self.candlestick_item)
                self.candlestick_item = None
            # Moving averages
            for sma_item in getattr(self, 'sma_items', []):
                self.price_plot.removeItem(sma_item)
            self.sma_items = []
            # VWAP
            if hasattr(self, 'vwap_item') and self.vwap_item:
                self.price_plot.removeItem(self.vwap_item)
                self.vwap_item = None
            # Bollinger Bands
            if hasattr(self, 'bb_upper_item') and self.bb_upper_item:
                self.price_plot.removeItem(self.bb_upper_item)
                self.bb_upper_item = None
            if hasattr(self, 'bb_lower_item') and self.bb_lower_item:
                self.price_plot.removeItem(self.bb_lower_item)
                self.bb_lower_item = None
            if hasattr(self, 'bb_fill_item') and self.bb_fill_item: # Fill between
                self.price_plot.removeItem(self.bb_fill_item)
                self.bb_fill_item = None
            # Buy/Sell signal scatters
            if hasattr(self, 'buy_scatter') and self.buy_scatter:
                self.price_plot.removeItem(self.buy_scatter) # Assuming it's added to price_plot
                # Re-create or clear points if it's a persistent scatter plot item
                # For simplicity here, we'll assume it needs to be re-created or its data cleared
                # self.buy_scatter.clear() # If it has a clear method
                self.buy_scatter = None 
            if hasattr(self, 'sell_scatter') and self.sell_scatter:
                self.price_plot.removeItem(self.sell_scatter)
                # self.sell_scatter.clear()
                self.sell_scatter = None

        # Volume plot items
        if hasattr(self, 'volume_plot') and self.volume_plot:
            if hasattr(self, 'volume_item') and self.volume_item:
                self.volume_plot.removeItem(self.volume_item)
                self.volume_item = None

        # RSI plot items
        if hasattr(self, 'rsi_plot') and self.rsi_plot:
            if hasattr(self, 'rsi_item') and self.rsi_item:
                self.rsi_plot.removeItem(self.rsi_item)
                self.rsi_item = None
            if hasattr(self, 'rsi_overbought_line') and self.rsi_overbought_line:
                self.rsi_plot.removeItem(self.rsi_overbought_line)
                self.rsi_overbought_line = None
            if hasattr(self, 'rsi_oversold_line') and self.rsi_oversold_line:
                self.rsi_plot.removeItem(self.rsi_oversold_line)
                self.rsi_oversold_line = None
            if hasattr(self, 'rsi_middle_line') and self.rsi_middle_line:
                self.rsi_plot.removeItem(self.rsi_middle_line)
                self.rsi_middle_line = None


        # MACD plot items
        if hasattr(self, 'macd_plot') and self.macd_plot:
            if hasattr(self, 'macd_line_item') and self.macd_line_item:
                self.macd_plot.removeItem(self.macd_line_item)
                self.macd_line_item = None
            if hasattr(self, 'signal_line_item') and self.signal_line_item:
                self.macd_plot.removeItem(self.signal_line_item)
                self.signal_line_item = None
            if hasattr(self, 'histogram_item') and self.histogram_item:
                self.macd_plot.removeItem(self.histogram_item)
                self.histogram_item = None
        
        # Clear any legend
        if hasattr(self, 'price_plot_legend') and self.price_plot_legend: # Added colon here
            self.price_plot_legend.clear() # Or self.price_plot.legend.clear() if legend is part of PlotItem
            # If legend is added manually, might need self.price_plot.scene().removeItem(self.price_plot_legend) and self.price_plot_legend.deleteLater()
            # For pyqtgraph's built-in legend, PlotItem.clear() might also clear it, or need specific handling.

        # 4. Reset UI elements
        if hasattr(self, 'status_label'):
            self.update_status("Chart cleared. Select a token.")
        if hasattr(self, 'current_price_label'):
            self.current_price_label.setText("Price: N/A")
        if hasattr(self, 'title_label'): # Assuming a title label for token symbol
            self.title_label.setText("No Token Selected")

        # 5. Explicitly call update to refresh the view if necessary, though removing items usually does this.
        if hasattr(self, 'plot_widget') and self.plot_widget:
            self.plot_widget.update() 
            # Or specific plots if they are separate widgets: self.price_plot.update() etc.

        logger.info(f"ChartWidget ({self.objectName()}): Data cleared successfully.")

    def stop_activity(self):
        """Stops all timers and prepares for deletion if necessary."""
        try:
            # 安全地尝试获取对象名称
            object_name = getattr(self, 'objectName', lambda: 'ChartWidget')()
            logger.info(f"ChartWidget ({object_name}): Stopping activity.")
        except (RuntimeError, AttributeError):
            logger.info(f"ChartWidget: Stopping activity (object partially destroyed).")
        
        # 安全地停止定时器
        try:
            if hasattr(self, 'refresh_timer') and self.refresh_timer:
                self.refresh_timer.stop()
        except (RuntimeError, AttributeError):
            pass
            
        try:
            if hasattr(self, 'highlight_timer') and self.highlight_timer:
                self.highlight_timer.stop()
        except (RuntimeError, AttributeError):
            pass
        # Any other cleanup before widget might be deleted by parent.

    # Ensure __del__ or closeEvent also calls stop_activity if managing resources not handled by Qt's parent/child
    def __del__(self):
        # 🔥 修复：更安全的__del__实现，避免访问已删除的C++对象
        try:
            # 简化的cleanup，避免调用可能失败的Qt方法
            if hasattr(self, 'refresh_timer'):
                try:
                    self.refresh_timer.stop()
                except (RuntimeError, AttributeError):
                    pass
            if hasattr(self, 'highlight_timer'):
                try:
                    self.highlight_timer.stop()
                except (RuntimeError, AttributeError):
                    pass
        except Exception:
            # 完全忽略任何异常，因为对象正在被销毁
            pass
    