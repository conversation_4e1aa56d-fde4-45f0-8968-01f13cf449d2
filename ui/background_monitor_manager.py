"""
后台监控管理器 - 统一管理后台图表监控，减少重复API请求
"""

import logging
import time
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

from config import BACKGROUND_MONITORING_CONFIG

logger = logging.getLogger('background_monitor_manager')


class BackgroundMonitorManager(QObject):
    """后台监控管理器 - 统一协调所有后台图表的数据请求"""
    
    # 信号
    data_batch_ready = pyqtSignal(dict)  # 批量数据准备就绪
    
    def __init__(self, api_service, parent=None):
        super().__init__(parent)
        self.api_service = api_service
        
        # 配置
        self.config = BACKGROUND_MONITORING_CONFIG
        self.cache_config = self.config.get('data_caching', {})
        self.batch_config = self.config.get('batch_processing', {})
        
        # 数据缓存
        self.data_cache: Dict[str, Dict] = {}  # {cache_key: {data, timestamp, token_info}}
        self.pending_requests: Set[str] = set()  # 正在请求的缓存键
        
        # 批处理队列
        self.request_queue: List[Dict] = []  # 待处理的请求队列
        self.processing_batch = False
        
        # 定时器
        self.batch_timer = QTimer()
        self.batch_timer.timeout.connect(self.process_request_batch)
        self.batch_timer.setSingleShot(True)
        
        # 缓存清理定时器
        self.cache_cleanup_timer = QTimer()
        self.cache_cleanup_timer.timeout.connect(self.cleanup_expired_cache)
        self.cache_cleanup_timer.start(60000)  # 每分钟清理一次过期缓存
        
        logger.info("BackgroundMonitorManager 初始化完成")
    
    def request_token_data(self, token_info: Dict, strategy_name: str, timeframe: str) -> bool:
        """
        请求代币数据，使用统一缓存和批处理
        
        参数:
            token_info: 代币信息
            strategy_name: 策略名称
            timeframe: 时间周期
            
        返回:
            bool: 是否有缓存数据可立即使用
        """
        token_address = token_info.get('tokenAddress', '')
        if not token_address:
            return False
        
        # 生成缓存键
        cache_key = f"{token_address}_{timeframe}"
        
        # 检查缓存
        cached_data = self.get_cached_data(cache_key)
        if cached_data:
            logger.debug(f"使用缓存数据: {token_info.get('symbol', 'Unknown')}")
            # 🔥🔥 记录缓存命中到性能监控器
            if hasattr(self.parent(), 'performance_monitor'):
                self.parent().performance_monitor.record_cache_hit()
            # 立即发送缓存数据
            self.emit_data_for_token(token_info, cached_data['data'], strategy_name)
            return True
        
        # 检查是否正在请求
        if cache_key in self.pending_requests:
            logger.debug(f"请求已在队列中: {token_info.get('symbol', 'Unknown')}")
            return False
        
        # 添加到请求队列
        request_item = {
            'token_info': token_info,
            'strategy_name': strategy_name,
            'timeframe': timeframe,
            'cache_key': cache_key,
            'timestamp': time.time()
        }
        
        self.request_queue.append(request_item)
        self.pending_requests.add(cache_key)
        
        # 启动批处理
        if not self.batch_timer.isActive():
            delay = self.batch_config.get('batch_delay', 2000)
            self.batch_timer.start(delay)
        
        return False
    
    def get_cached_data(self, cache_key: str) -> Optional[Dict]:
        """获取缓存数据"""
        if not self.cache_config.get('enabled', True):
            return None
        
        if cache_key not in self.data_cache:
            return None
        
        cache_item = self.data_cache[cache_key]
        cache_duration = self.cache_config.get('cache_duration', 180000)  # 3分钟
        
        # 检查是否过期
        if time.time() - cache_item['timestamp'] > cache_duration / 1000:
            del self.data_cache[cache_key]
            return None
        
        return cache_item
    
    def process_request_batch(self):
        """处理一批请求"""
        if self.processing_batch or not self.request_queue:
            return
        
        self.processing_batch = True
        batch_size = self.batch_config.get('batch_size', 3)
        
        # 取出一批请求
        current_batch = self.request_queue[:batch_size]
        self.request_queue = self.request_queue[batch_size:]
        
        logger.info(f"处理请求批次: {len(current_batch)} 个代币")
        
        # 处理当前批次
        for i, request_item in enumerate(current_batch):
            # 延迟处理每个请求
            item_delay = self.batch_config.get('item_delay', 1000)
            delay_ms = i * item_delay
            
            QTimer.singleShot(delay_ms, lambda r=request_item: self.process_single_request(r))
        
        # 如果还有请求，继续处理下一批
        if self.request_queue:
            next_batch_delay = self.batch_config.get('batch_delay', 2000)
            QTimer.singleShot(next_batch_delay + len(current_batch) * item_delay, 
                            self.schedule_next_batch)
        else:
            self.processing_batch = False
    
    def schedule_next_batch(self):
        """安排下一批处理"""
        self.processing_batch = False
        if self.request_queue:
            self.process_request_batch()
    
    def process_single_request(self, request_item: Dict):
        """处理单个请求"""
        try:
            token_info = request_item['token_info']
            cache_key = request_item['cache_key']
            
            # 连接APIService信号（如果尚未连接）
            if not hasattr(self, '_api_connected'):
                self.api_service.ohlcv_data_ready.connect(self.on_api_data_received)
                self.api_service.ohlcv_data_error.connect(self.on_api_error)
                self._api_connected = True
            
            # 🔥🔥 记录API请求到性能监控器
            if hasattr(self.parent(), 'performance_monitor'):
                self.parent().performance_monitor.record_api_request()
            
            # 发起API请求
            token_address = token_info.get('tokenAddress', '')
            timeframe = request_item['timeframe']
            source = token_info.get('source', 'trend')
            
            if source == 'holdings':
                source = 'trend'  # 统一使用trend API
            
            logger.debug(f"发起API请求: {token_info.get('symbol', 'Unknown')} - {timeframe}")
            
            self.api_service.get_ohlcv_data_async(
                token_address=token_address,
                timeframe=timeframe,
                days=1,
                source=source
            )
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            # 移除pending状态
            cache_key = request_item.get('cache_key', '')
            if cache_key in self.pending_requests:
                self.pending_requests.remove(cache_key)
    
    def on_api_data_received(self, token_address: str, timeframe: str, ohlcv_data: List):
        """API数据接收回调"""
        cache_key = f"{token_address}_{timeframe}"
        
        # 移除pending状态
        if cache_key in self.pending_requests:
            self.pending_requests.remove(cache_key)
        
        # 缓存数据
        if self.cache_config.get('enabled', True):
            self.data_cache[cache_key] = {
                'data': ohlcv_data,
                'timestamp': time.time(),
                'token_address': token_address,
                'timeframe': timeframe
            }
        
        # 查找并通知相关的请求
        self.notify_waiting_widgets(token_address, timeframe, ohlcv_data)
        
        logger.debug(f"API数据已缓存: {token_address} - {timeframe}")
    
    def on_api_error(self, error_msg: str):
        """API错误回调"""
        logger.error(f"API请求失败: {error_msg}")
        
        # 清理所有pending状态（简化处理）
        self.pending_requests.clear()
    
    def notify_waiting_widgets(self, token_address: str, timeframe: str, ohlcv_data: List):
        """通知等待数据的组件"""
        # 发送批量数据信号
        data_batch = {
            'token_address': token_address,
            'timeframe': timeframe,
            'ohlcv_data': ohlcv_data,
            'timestamp': time.time()
        }
        
        self.data_batch_ready.emit(data_batch)
    
    def emit_data_for_token(self, token_info: Dict, ohlcv_data: List, strategy_name: str):
        """为特定代币发送数据"""
        data_batch = {
            'token_address': token_info.get('tokenAddress', ''),
            'timeframe': token_info.get('timeframe', '1m'),
            'ohlcv_data': ohlcv_data,
            'token_info': token_info,
            'strategy_name': strategy_name,
            'timestamp': time.time(),
            'from_cache': True
        }
        
        self.data_batch_ready.emit(data_batch)
    
    def cleanup_expired_cache(self):
        """清理过期缓存"""
        if not self.cache_config.get('enabled', True):
            return
        
        cache_duration = self.cache_config.get('cache_duration', 180000) / 1000
        current_time = time.time()
        
        expired_keys = []
        for cache_key, cache_item in self.data_cache.items():
            if current_time - cache_item['timestamp'] > cache_duration:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            del self.data_cache[key]
        
        if expired_keys:
            logger.debug(f"清理过期缓存: {len(expired_keys)} 个条目")
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.data_cache),
            'pending_requests': len(self.pending_requests),
            'request_queue_size': len(self.request_queue),
            'processing_batch': self.processing_batch
        }
    
    def clear_cache(self):
        """清理所有缓存"""
        self.data_cache.clear()
        self.pending_requests.clear()
        self.request_queue.clear()
        logger.info("后台监控缓存已清理") 