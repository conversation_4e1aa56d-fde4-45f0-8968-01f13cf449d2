# ui/trend_widget.py

import os
import time
import json
import logging
import threading # 尽管 QThread 更推荐，但这里为了兼容保持导入
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QProgressBar, QMessageBox, QAbstractItemView,
    QFrame, QSplitter
)
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QSize, QUrl, QMetaObject, Q_ARG, pyqtSlot # 修正：添加 pyqtSlot
)
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QDesktopServices

# 导入 APIService (现在它是一个 QObject)
from api_service import APIService
from config import TREND_REFRESH_INTERVAL

# 配置日志
logger = logging.getLogger('trend_widget')

class TrendWidget(QWidget):
    """趋势榜单组件类"""
    
    # 自定义信号
    token_selected = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        """
        初始化趋势榜单组件
        
        参数:
            parent (QWidget, optional): 父组件
        """
        super().__init__(parent)
        
        # 实例化 APIService (它现在是一个 QObject，并在其内部管理线程)
        self.api_service = APIService(self) # 将 self 作为父对象，管理生命周期

        # 连接 APIService 的信号到 TrendWidget 的槽函数
        self.api_service.trending_tokens_ready.connect(self.on_trending_data_ready)
        self.api_service.trending_tokens_error.connect(self.on_trending_error)
        
        # 初始化UI
        self.init_ui()
        
        # 存储数据
        self.tokens_data = []
        
        # 创建定时器，定时刷新数据
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(TREND_REFRESH_INTERVAL)  # 每分钟刷新一次
        
        # 关键修改：在初始化时立即触发一次异步数据加载
        # 这确保了组件一显示就能开始加载数据，而不需要等待定时器或外部触发
        self.refresh_data() 
        
        # 显示加载提示
        self.status_label.setText("正在加载趋势榜单数据...")
    
    def init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题和刷新按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Meme币趋势榜单")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setFixedWidth(100)
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)
        
        main_layout.addLayout(header_layout)
        
        # 创建状态栏
        status_bar = QHBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        status_bar.addWidget(self.status_label)
        
        status_bar.addStretch()
        
        # 最后更新时间标签
        self.last_update_label = QLabel("")
        self.last_update_label.setStyleSheet("color: #888888;")
        status_bar.addWidget(self.last_update_label)
        
        main_layout.addLayout(status_bar)
        
        # 创建趋势榜单表格
        self.tokens_table = QTableWidget()
        self.tokens_table.setColumnCount(9)
        self.tokens_table.setHorizontalHeaderLabels([
            "排名", "代币", "符号", "价格 (USD)", "24h涨跌幅", "市值", "24h成交量", 
            "持有人数", "推文数"
        ])
        
        # 设置表格属性
        self.tokens_table.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑
        self.tokens_table.setSelectionBehavior(QAbstractItemView.SelectRows)  # 选择整行
        self.tokens_table.setSelectionMode(QAbstractItemView.SingleSelection)  # 单选
        self.tokens_table.verticalHeader().setVisible(False)  # 隐藏垂直表头
        self.tokens_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)  # 列宽自适应
        self.tokens_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 排名列宽自适应内容
        self.tokens_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 代币列宽自适应内容
        
        # 连接表格选择信号
        self.tokens_table.itemSelectionChanged.connect(self.on_token_selected)
        
        main_layout.addWidget(self.tokens_table)
        
        # 添加说明文本
        info_label = QLabel("说明: 此榜单显示Solana链上热门Meme币，每分钟自动更新一次。点击代币行可查看详情，点击查看K线按钮可查看价格走势图。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #888; font-size: 11px;")
        main_layout.addWidget(info_label)
    
    @pyqtSlot(list) # 接收 list 类型
    def on_trending_data_ready(self, tokens_data: List[Dict]):
        """
        处理 APIService 发送的趋势数据就绪信号
        这个槽函数在主线程中执行
        """
        try:
            logger.info(f"主线程: 处理趋势数据回调，获取到 {len(tokens_data) if tokens_data else 0} 个趋势代币")
            logger.debug(f"当前线程: {threading.current_thread().name}") # 确认在主线程
            
            if not tokens_data:
                logger.warning("未获取到代币数据")
                self.status_label.setText("未获取到代币数据")
                self.refresh_button.setEnabled(True)
                return
            
            self.tokens_data = tokens_data
            self.update_table() # 在主线程中直接调用 update_table

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.last_update_label.setText(f"上次更新: {current_time}")
            self.status_label.setText(f"已加载 {len(tokens_data)} 个代币")
            
        except Exception as e:
            error_msg = f"处理趋势数据时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self.status_label.setText(error_msg)
        finally:
            self.refresh_button.setEnabled(True)

    @pyqtSlot(str) # 接收 str 类型
    def on_trending_error(self, error_msg: str):
        """
        处理 APIService 发送的趋势数据错误信号
        这个槽函数在主线程中执行
        """
        logger.error(f"主线程: 异步获取趋势榜单数据失败: {error_msg}")
        self.status_label.setText(f"获取数据失败: {error_msg}")
        self.refresh_button.setEnabled(True)

    def refresh_data(self):
        """
        刷新趋势榜单数据 (通过 APIService 触发异步请求)
        """
        self.status_label.setText("正在刷新数据...")
        self.refresh_button.setEnabled(False)
        
        # 调用 APIService 的异步方法
        self.api_service.get_trending_tokens_async()
    
    @pyqtSlot()
    def update_table(self):
        """更新表格内容"""
        self.tokens_table.setRowCount(0)
        
        if not self.tokens_data:
            logger.debug("tokens_data为空，没有数据可显示")
            return

        for i, token in enumerate(self.tokens_data):
            row_position = self.tokens_table.rowCount()
            self.tokens_table.insertRow(row_position)
            
            # 排名
            rank_item = QTableWidgetItem(str(i + 1))
            rank_item.setTextAlignment(Qt.AlignCenter)
            self.tokens_table.setItem(row_position, 0, rank_item)
            
            # 代币名称
            name_item = QTableWidgetItem(token.get('name', 'Unknown'))
            self.tokens_table.setItem(row_position, 1, name_item)
            
            # 代币符号
            symbol_item = QTableWidgetItem(token.get('symbol', 'Unknown'))
            self.tokens_table.setItem(row_position, 2, symbol_item)
            
            # 价格
            price = token.get('price', 0.0)
            if price == 0:
                price_text = "$0.00"
            elif price < 0.0000001:
                price_text = f"${price:.10f}"
            elif price < 0.0001:
                price_text = f"${price:.8f}"
            elif price < 0.01:
                price_text = f"${price:.6f}"
            elif price < 1:
                price_text = f"${price:.4f}"
            else:
                price_text = f"${price:.2f}"
            price_item = QTableWidgetItem(price_text)
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.tokens_table.setItem(row_position, 3, price_item)
            
            # 24h涨跌幅
            price_change = (
                token.get('priceChange5m') or
                token.get('change5m') or
                token.get('percentChange5m')
            )
            if price_change is None:
                price_change = 0.0
            
            try:
                price_change_float = float(price_change)
                change_text = f"$$$$" # f"  {price_change_float:.0f}%"
                change_item = QTableWidgetItem(change_text)
                change_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                if price_change_float > 0:
                    change_item.setForeground(QColor("#4caf50"))
                    change_item.setText(f"dd")
                elif price_change_float < 0:
                    change_item.setForeground(QColor("#f44336"))
                else:
                    change_item.setForeground(QColor("#666666"))
                    
                self.tokens_table.setItem(row_position, 4, change_item)
            except (ValueError, TypeError) as e:
                logger.error(f"解析价格变化失败: {e}, 原始值: {price_change}")
                change_item = QTableWidgetItem("N/A")
                change_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.tokens_table.setItem(row_position, 4, change_item)
            
            # 市值
            market_cap = token.get('marketCap', 0.0)
            market_cap_text = f"${self.format_number(market_cap)}"
            market_cap_item = QTableWidgetItem(market_cap_text)
            market_cap_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.tokens_table.setItem(row_position, 5, market_cap_item)
            
            # 24h成交量
            volume = token.get('volume24h', 0.0)
            volume_text = f"${self.format_number(volume)}"
            volume_item = QTableWidgetItem(volume_text)
            volume_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.tokens_table.setItem(row_position, 6, volume_item)
            
            # 持有人数
            holders = token.get('holders', 0)
            holders_item = QTableWidgetItem(str(int(holders)))
            holders_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.tokens_table.setItem(row_position, 7, holders_item)
            
            # 推文数
            tweets = token.get('tweetCount', 0)
            tweets_item = QTableWidgetItem(str(int(tweets)))
            tweets_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.tokens_table.setItem(row_position, 8, tweets_item)
    
        self.tokens_table.resizeColumnsToContents()
        self.tokens_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tokens_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.tokens_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.tokens_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)

    def on_token_selected(self):
        """处理表格选择变化事件"""
        selected_items = self.tokens_table.selectedItems()
        
        if selected_items:
            selected_row_view = self.tokens_table.currentRow()

            # 从表格视图中获取关键信息，例如 tokenAddress (如果存在于列中)
            # 假设 tokenAddress 存储在某一列，或者我们可以从已有的列中获取足够信息来查找
            # 对于 TrendWidget，其数据源直接有 'tokenAddress'
            # 我们需要一种方式从视图行号映射回 self.tokens_data 中的正确条目
            # 最好的方式是，在填充表格时，将 tokenAddress (或其他唯一ID) 存入 QTableWidgetItem 的 data (e.g., Qt.UserRole)
            # 或者，如果某一列明确是 tokenAddress，我们可以直接读取它。
            # 查看 update_table，目前没有直接将 tokenAddress 设为单元格文本，但 self.tokens_data[row] 包含它。
            
            # 策略：从视图行获取'代币符号'(symbol)和'名称'(name)，然后尝试在 self.tokens_data 中匹配
            # 这不如直接用 address 稳妥，但如果 address 没有直接显示在表格中，这是一个备选方案
            # 更优的方案是，在填充表格时，将 address 作为 Qt.UserRole 数据存储在每个 item 中
            # 或者，如果原始顺序未变，可以直接使用 self.tokens_data[selected_row_view]
            # 但题目是"排序后"，所以原始顺序会变。

            # 修改：从表格中读取某一列作为唯一标识符，例如 address
            # 在 TrendWidget 的表格中，没有直接显示 token address 的列，但 symbol 或 name 可以用作查找
            # 更好的做法是，在填充表格时，将 tokenAddress 存储到表格项的数据中。
            # 为了简单起见，并且假设 '代币' (name) 或 '符号' (symbol) 在当前视图中是相对唯一的
            # 我们将尝试通过这些信息来查找。Symbol (第2列) 应该更可靠。

            symbol_item = self.tokens_table.item(selected_row_view, 2) # '符号' 在第 2 列
            name_item = self.tokens_table.item(selected_row_view, 1) # '代币' 在第 1 列
            
            token_data = None
            if symbol_item:
                selected_symbol = symbol_item.text()
                selected_name = name_item.text() if name_item else ""
                # 在 self.tokens_data 中查找
                for td in self.tokens_data:
                    # tokenAddress 是最可靠的，但它不在表格的可见列中
                    # 我们需要确保在 populate_table 时，将 tokenAddress 存储起来，或者通过其他唯一标识查找
                    # 假设这里的 self.tokens_data 中的每个字典都有 'tokenAddress'
                    # 我们现在要基于表格可见内容反向查找
                    if td.get("symbol") == selected_symbol and td.get("name") == selected_name:
                        token_data = td
                        break
            
            if token_data:
                selected_token_info = {
                    "name": token_data.get("name", "N/A"),
                    "symbol": token_data.get("symbol", "N/A"),
                    "address": token_data.get("tokenAddress"), 
                    "tokenAddress": token_data.get("tokenAddress"), 
                    "price": token_data.get("price"),
                    "source": "trend"
                }
                self.token_selected.emit(selected_token_info)
                logger.info(f"TrendWidget: Token selected - {selected_token_info.get('symbol')}")
            else:
                self.token_selected.emit({}) 
                logger.warning(f"TrendWidget: Could not find matching token in self.tokens_data for selected row: {selected_row_view} (Symbol: {symbol_item.text() if symbol_item else 'N/A'})")
        else:
            self.token_selected.emit({}) 

    @staticmethod
    def format_number(number):
        if number is None:
            return "N/A"
        try:
            num = float(number)
        except (ValueError, TypeError):
            return "N/A"

        if num >= 1_000_000_000:
            return f"{num / 1_000_000_000:.2f}B"
        elif num >= 1_000_000:
            return f"{num / 1_000_000:.2f}M"
        elif num >= 1_000:
            return f"{num / 1_000:.2f}K"
        else:
            return f"{num:.2f}"