"""
回测组件 - 对选定代币进行策略回测
"""

import os
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import pandas as pd
import numpy as np
import pyqtgraph as pg
from pyqtgraph import PlotWidget, AxisItem

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QGroupBox, QFormLayout, QCheckBox, QSpinBox, QDoubleSpinBox,
    QSplitter, QFrame, QTabWidget, QGridLayout, QScrollArea, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize, QThread, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor

from api_service import APIService
from indicators import TechnicalIndicators
from strategies import StrategyFactory
from config import (
    DEFAULT_TIMEFRAME, AVAILABLE_TIMEFRAMES, DEFAULT_BACKTEST_DAYS,
    INDICATOR_PARAMS, CANDLE_COLORS
)

# 配置日志
logger = logging.getLogger('backtest_widget')

class CandlestickItem(pg.GraphicsObject):
    """自定义K线图项目类"""
    
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.picture = None
        self.scaling_factor = 1_000_000_000
        logger.info(f"Backtest CandlestickItem: __init__ called with data of length {len(data) if data is not None else 'None'}")
        self.generatePicture()
    
    def generatePicture(self):
        self.picture = pg.QtGui.QPicture()
        painter = pg.QtGui.QPainter(self.picture)
        w = 0.8
        if self.data is None or self.data.empty:
            painter.end()
            return
        for i in range(len(self.data)):
            t = i
            open_price = self.data['open'].iloc[i] * self.scaling_factor
            high_price = self.data['high'].iloc[i] * self.scaling_factor
            low_price = self.data['low'].iloc[i] * self.scaling_factor
            close_price = self.data['close'].iloc[i] * self.scaling_factor
            
            if close_price >= open_price:
                color_config = CANDLE_COLORS['up']
                color = pg.mkColor(color_config)
                painter.setPen(pg.mkPen(color))
                painter.setBrush(pg.mkBrush(color))
                painter.drawRect(pg.QtCore.QRectF(t-w/2, open_price, w, close_price-open_price))
            else:
                color_config = CANDLE_COLORS['down']
                color = pg.mkColor(color_config)
                painter.setPen(pg.mkPen(color))
                painter.setBrush(pg.mkBrush(color))
                painter.drawRect(pg.QtCore.QRectF(t-w/2, close_price, w, open_price-close_price))
            
            painter.setPen(pg.mkPen(color))
            if high_price > max(open_price, close_price):
                painter.drawLine(
                    pg.QtCore.QPointF(t, max(open_price, close_price)),
                    pg.QtCore.QPointF(t, high_price)
                )
            if low_price < min(open_price, close_price):
                painter.drawLine(
                    pg.QtCore.QPointF(t, min(open_price, close_price)),
                    pg.QtCore.QPointF(t, low_price)
                )
        painter.end()
    
    def paint(self, painter, option, widget):
        if self.picture:
            painter.drawPicture(0, 0, self.picture)
    
    def boundingRect(self):
        return pg.QtCore.QRectF(self.picture.boundingRect())

class TimeAxisItem(pg.AxisItem):
    """自定义时间轴项目类"""
    def __init__(self, timestamps, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timestamps = timestamps
    
    def tickStrings(self, values, scale, spacing):
        result = []
        for value in values:
            try:
                index = int(value)
                if 0 <= index < len(self.timestamps):
                    timestamp = self.timestamps[index]
                    dt = datetime.fromtimestamp(timestamp)
                    result.append(dt.strftime('%H:%M\\n%m-%d'))
                else:
                    result.append('')
            except (ValueError, IndexError):
                result.append('')
        return result

class BacktestThread(QThread):
    """回测线程类，用于在后台执行回测"""
    
    # 自定义信号
    progress_updated = pyqtSignal(int)
    backtest_completed = pyqtSignal(dict)
    backtest_error = pyqtSignal(str)
    
    def __init__(self, api_service: APIService, token_data, timeframe, strategy_name, initial_capital):
        """
        初始化回测线程
        
        参数:
            api_service (APIService): APIService的实例，用于获取数据。
            token_data (Dict): 代币数据
            timeframe (str): 时间周期 (应固定为1m或从historical token数据中获取)
            strategy_name (str): 策略名称
            initial_capital (float): 初始资金
        """
        super().__init__()
        self.api_service = api_service
        self.token_data = token_data
        self.timeframe = timeframe # 通常是 '1m' 对于此策略
        # self.days = 1 # Days 不再是主要参数，API会返回所有可用数据
        self.strategy_name = strategy_name
        self.initial_capital = initial_capital
    
    def run(self):
        """执行回测"""
        try:
            # 使用token_data中的source来确定API调用方式
            # token_source = self.token_data.get('source', 'historical') # 默认为historical
            token_source = 'historical' # 强制所有回测使用此源以调用 ohlcv-sync API
            logger.info(f"BacktestThread: run() started. Token: {self.token_data.get('symbol')}, Timeframe: {self.timeframe}, Strategy: {self.strategy_name}, Forced Source: {token_source}, Initial Capital: {self.initial_capital}")
            self.progress_updated.emit(10)
            
            token_address = self.token_data.get('tokenAddress')
            if not token_address:
                self.backtest_error.emit("无效的代币地址")
                return
            
            self.progress_updated.emit(20)
            # Days参数对于ohlcv-sync API不重要，它会返回所有可用数据
            # 对于Birdeye，days=1仍有意义，但我们现在主要针对historical
            days_param_for_api = 1 # 可以设为1，因为historical API会忽略它，而如果意外用到Birdeye，则取1天
            logger.info(f"BacktestThread: Fetching OHLCV data using source: {token_source} (days_param_for_api: {days_param_for_api})")
            ohlcv_data = self.api_service.get_ohlcv_data(
                token_address=token_address,
                timeframe=self.timeframe, 
                days=days_param_for_api, 
                source=token_source 
            )
            
            if not ohlcv_data:
                self.backtest_error.emit("获取K线数据失败，请稍后重试")
                logger.warning("BacktestThread: ohlcv_data is empty or None after API call.")
                return
            
            logger.info(f"BacktestThread: Received {len(ohlcv_data)} OHLCV records.")
            if ohlcv_data:
                first_ts = ohlcv_data[0].get('timestamp')
                last_ts = ohlcv_data[-1].get('timestamp')
                logger.info(f"BacktestThread: Data from {datetime.fromtimestamp(first_ts)} to {datetime.fromtimestamp(last_ts)}")

            # 转换为DataFrame
            self.progress_updated.emit(40)
            df = pd.DataFrame(ohlcv_data)
            
            # --- BEGIN MODIFICATION: Resample data based on strategy's primary_timeframe ---
            strategy = StrategyFactory.get_strategy_by_name(self.strategy_name)
            if not strategy:
                self.backtest_error.emit(f"未找到策略: {self.strategy_name}")
                return

            strategy_primary_timeframe = strategy.get_primary_timeframe()
            logger.info(f"BacktestThread: UI timeframe is '{self.timeframe}', Strategy primary_timeframe is '{strategy_primary_timeframe}'.")

            # 数据清洗：确保 'timestamp' 列存在且为数值类型，去除重复，设置索引
            if 'timestamp' not in df.columns:
                self.backtest_error.emit("K线数据缺少 'timestamp' 列")
                logger.error("BacktestThread: OHLCV data is missing 'timestamp' column.")
                return
            try:
                df['timestamp'] = pd.to_numeric(df['timestamp'])
            except ValueError as e:
                self.backtest_error.emit(f"无法将 'timestamp' 列转换为数值类型: {e}")
                logger.error(f"BacktestThread: Error converting 'timestamp' to numeric: {e}")
                return

            df = df.drop_duplicates(subset=['timestamp'])
            df['datetime_pd'] = pd.to_datetime(df['timestamp'], unit='s')
            df = df.set_index('datetime_pd').sort_index()
            
            # 如果获取的是1m数据，且策略要求的时间周期不是1m，则进行重采样
            current_data_timeframe = self.timeframe # e.g., "1m"
            df_for_strategy = df.copy()

            if strategy_primary_timeframe != current_data_timeframe and strategy_primary_timeframe != '1m': # Avoid resampling 1m to 1m
                logger.info(f"BacktestThread: Resampling '{current_data_timeframe}' data to strategy's primary timeframe '{strategy_primary_timeframe}'.")
                
                # 将策略中的时间周期字符串转换为Pandas兼容的offset alias
                pd_resample_rule = strategy_primary_timeframe
                if pd_resample_rule.endswith('m') and not pd_resample_rule.startswith('M'): # e.g., "5m", "15m"
                    pd_resample_rule = pd_resample_rule.replace('m', 'T') 
                elif pd_resample_rule.endswith('h'): # e.g., "1h", "4h"
                    pd_resample_rule = pd_resample_rule.replace('h', 'H')
                elif pd_resample_rule.endswith('d'): # e.g., "1d"
                    pd_resample_rule = pd_resample_rule.replace('d', 'D')
                # Add more rules if necessary (W, ME, etc.)

                try:
                    df_resampled = df_for_strategy.resample(pd_resample_rule).agg({
                        'open': 'first',
                        'high': 'max',
                        'low': 'min',
                        'close': 'last',
                        'volume': 'sum'
                    }).dropna() # dropna() to remove periods with no trades after resampling

                    if not df_resampled.empty:
                        df_resampled['timestamp'] = df_resampled.index.astype(np.int64) // 10**9
                        df_resampled['datetime'] = df_resampled.index.strftime('%Y-%m-%d %H:%M:%S')
                        # Ensure 'ohlcv_str' and 'token_address' columns are not lost if they existed and were needed by indicators/strategy
                        # However, standard OHLCV usually doesn't have them.
                        df_for_strategy = df_resampled.reset_index() # To make datetime_pd a column again if needed, or keep as index
                        logger.info(f"BacktestThread: Resampled data to {len(df_for_strategy)} records for {strategy_primary_timeframe}.")
                    else:
                        logger.warning(f"BacktestThread: Resampling to {strategy_primary_timeframe} resulted in an empty DataFrame. Using original {current_data_timeframe} data.")
                        # Fallback to original data if resampling fails or results in empty df, but this should be an error.
                        # For now, to avoid crashing, we'll log and proceed with un-resampled data, but this is not ideal.
                        # A better approach might be to emit an error.
                        # For safety, ensure df_for_strategy (which is still 'df.copy()' here) has the string 'datetime' column
                        if 'datetime' not in df_for_strategy.columns and isinstance(df_for_strategy.index, pd.DatetimeIndex):
                             df_for_strategy['datetime'] = df_for_strategy.index.strftime('%Y-%m-%d %H:%M:%S')
                        df_for_strategy = df_for_strategy.reset_index() # ensure datetime_pd is a column

                except Exception as e:
                    logger.error(f"BacktestThread: Error during resampling to {strategy_primary_timeframe}: {e}. Using original {current_data_timeframe} data.")
                    # Fallback logic as above
                    if 'datetime' not in df_for_strategy.columns and isinstance(df_for_strategy.index, pd.DatetimeIndex):
                         df_for_strategy['datetime'] = df_for_strategy.index.strftime('%Y-%m-%d %H:%M:%S')
                    df_for_strategy = df_for_strategy.reset_index() # ensure datetime_pd is a column
            else:
                logger.info(f"BacktestThread: Using '{current_data_timeframe}' data directly as strategy requires '{strategy_primary_timeframe}'.")
                # Ensure 'datetime' string column exists if using original data
                if 'datetime' not in df_for_strategy.columns and isinstance(df_for_strategy.index, pd.DatetimeIndex):
                    df_for_strategy['datetime'] = df_for_strategy.index.strftime('%Y-%m-%d %H:%M:%S')
                df_for_strategy = df_for_strategy.reset_index() # ensure datetime_pd is a column
            
            # Ensure df_for_strategy has the necessary columns for add_all_indicators
            # Typically: 'open', 'high', 'low', 'close', 'volume'
            # And it should have 'datetime_pd' as index for some indicator functions or be converted.
            # For simplicity, let's re-set 'datetime_pd' as index before indicators if it became a column.
            if 'datetime_pd' in df_for_strategy.columns:
                df_for_strategy = df_for_strategy.set_index('datetime_pd').sort_index()

            # --- END MODIFICATION ---

            # 添加技术指标
            self.progress_updated.emit(60)
            # df = TechnicalIndicators.add_all_indicators(df) # Original line
            df_with_indicators = TechnicalIndicators.add_all_indicators(df_for_strategy.copy()) # Use df_for_strategy
            
            # 获取策略 - already done above
            # strategy = StrategyFactory.get_strategy_by_name(self.strategy_name)
            
            # if not strategy:
            #     self.backtest_error.emit(f"未找到策略: {self.strategy_name}")
            #     return
            
            # 生成交易信号
            self.progress_updated.emit(70)
            # df = strategy.generate_signals(df) # Original line
            df_with_signals = strategy.generate_signals(df_with_indicators.copy())
            
            # 执行回测
            self.progress_updated.emit(80)
            # result_dict = strategy.backtest(df, self.initial_capital) # Original line
            result_dict = strategy.backtest(df_with_signals.copy(), self.initial_capital)

            logger.info(f"BacktestThread: strategy.backtest() returned initial_capital: {result_dict.get('initial_capital')}")
            
            # 准备传递给UI的数据，确保包含 ohlcv_df 和 trades_df (来自 result_dict['trades'])
            # The ohlcv_df for UI should be the one that strategy actually used and is in result_dict['ohlcv_df']
            # BaseStrategy.backtest returns the input df (df_with_signals) as 'ohlcv_df' in its result_dict.
            ui_ohlcv_df_from_result = result_dict.get('ohlcv_df')
            if ui_ohlcv_df_from_result is None or ui_ohlcv_df_from_result.empty:
                logger.warning("BacktestThread: 'ohlcv_df' from strategy.backtest() is empty or None. Plotting might be affected.")
                # Fallback to df_with_signals if needed, but it should be in result
                ui_ohlcv_df_from_result = df_with_signals # This df_with_signals is correctly timeframed

            # Ensure the DataFrame for plotting has 'datetime_pd' as index if CandlestickItem expects it,
            # or 'timestamp' as a column if TimeAxisItem expects it from a list.
            # CandlestickItem uses iloc and column names. TimeAxisItem uses a list of timestamps.
            # The 'ohlcv_df' from result_dict should be fine as BaseStrategy returns the df it worked on.

            ui_result = {
                'summary': result_dict, 
                'ohlcv_df': ui_ohlcv_df_from_result, # This DataFrame should be correctly timeframed
                'trades_df': pd.DataFrame(result_dict.get('trades', [])) 
            }
            
            # 发送完成信号
            self.progress_updated.emit(100)
            self.backtest_completed.emit(ui_result)
            
        except Exception as e:
            logger.error(f"回测时出错: {str(e)}")
            self.backtest_error.emit(f"回测时出错: {str(e)}")


class BacktestWidget(QWidget):
    """回测组件类"""
    
    def __init__(self, api_service: APIService, parent=None):
        """
        初始化回测组件
        
        参数:
            api_service (APIService): APIService的实例，用于获取数据。
            parent (QWidget, optional): 父组件
        """
        super().__init__(parent)
        self.api_service = api_service
        
        # 初始化变量
        self.token_data = None
        self.backtest_result = None
        self.backtest_thread = None
        
        # 初始化UI
        self.init_ui()
    
    def init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建标题
        title_label = QLabel("策略回测")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        main_layout.addWidget(title_label)
        
        # 创建状态标签
        self.status_label = QLabel("请从趋势榜单中选择一个代币或等待K线数据加载")
        main_layout.addWidget(self.status_label)
        
        # 创建分割器，左侧是设置，右侧是结果和图表
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 创建设置区域
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(10)
        
        # 代币信息组
        token_group = QGroupBox("代币信息")
        token_layout = QFormLayout(token_group)
        
        self.token_name_label = QLabel("未选择")
        token_layout.addRow("代币名称:", self.token_name_label)
        
        self.token_symbol_label = QLabel("未选择")
        token_layout.addRow("代币符号:", self.token_symbol_label)
        
        self.token_price_label = QLabel("未选择")
        token_layout.addRow("当前价格:", self.token_price_label)
        
        self.token_address_label = QLabel("未选择")
        token_layout.addRow("代币地址:", self.token_address_label)
        
        settings_layout.addWidget(token_group)
        
        # 回测参数组
        params_group = QGroupBox("回测参数")
        params_layout = QFormLayout(params_group)
        
        # 时间周期选择 (对于打新策略，通常固定为1m，可以考虑隐藏或锁定)
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItem("1m") # 固定为1m
        # for tf in AVAILABLE_TIMEFRAMES:
        #     self.timeframe_combo.addItem(tf)
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.setEnabled(False) # 锁定为1m
        params_layout.addRow("时间周期:", self.timeframe_combo)
        
        # # 回测天数 - 已移除
        # self.days_spin = QSpinBox()
        # self.days_spin.setRange(1, 365)
        # self.days_spin.setValue(DEFAULT_BACKTEST_DAYS)
        # params_layout.addRow("回测天数:", self.days_spin)
        
        # 策略选择
        self.strategy_combo = QComboBox()
        try:
            for strategy in StrategyFactory.get_all_strategies():
                self.strategy_combo.addItem(strategy.name)
        except Exception as e:
            logger.error(f"加载策略列表失败: {e}")
            self.strategy_combo.addItem("错误:无法加载策略")
        params_layout.addRow("选择策略:", self.strategy_combo)
        
        self.initial_capital_spin = QDoubleSpinBox()
        self.initial_capital_spin.setRange(1.0, 1_000_000_000.0)
        self.initial_capital_spin.setValue(10000.0)
        self.initial_capital_spin.setDecimals(2)
        self.initial_capital_spin.setSuffix(" $")
        params_layout.addRow("初始资金:", self.initial_capital_spin)
        
        settings_layout.addWidget(params_group)
        
        # 回测按钮
        self.start_button = QPushButton("开始回测")
        self.start_button.setFixedHeight(40)
        self.start_button.clicked.connect(self.start_backtest)
        self.start_button.setEnabled(False)  # 初始禁用
        settings_layout.addWidget(self.start_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        settings_layout.addWidget(self.progress_bar)
        
        settings_layout.addStretch()
        
        main_splitter.addWidget(settings_widget)
        
        # 创建结果区域
        results_and_chart_widget = QWidget()
        results_and_chart_layout = QVBoxLayout(results_and_chart_widget) 
        results_and_chart_widget.setLayout(results_and_chart_layout)      
        
        # 创建结果显示区域 (TabWidget)
        self.results_tabs = QTabWidget()
        
        # --- 统计摘要标签页 --- 
        self.summary_tab = QWidget()
        self.summary_layout = QFormLayout(self.summary_tab) # Set layout for the tab itself
        # self.summary_tab.setLayout(self.summary_layout) # QFormLayout constructor already sets parent

        # 创建并添加摘要标签
        self.strategy_name_label = QLabel("-")
        self.summary_layout.addRow("策略名称:", self.strategy_name_label)
        
        self.initial_capital_label = QLabel("-")
        self.summary_layout.addRow("初始资金:", self.initial_capital_label)
        
        self.final_capital_label = QLabel("-")
        self.summary_layout.addRow("最终资金:", self.final_capital_label)
        
        self.total_profit_label = QLabel("-")
        self.summary_layout.addRow("总收益:", self.total_profit_label)
        
        self.profit_percentage_label = QLabel("-")
        self.summary_layout.addRow("收益率:", self.profit_percentage_label)
        
        self.trade_count_label = QLabel("-")
        self.summary_layout.addRow("交易次数:", self.trade_count_label)
        
        self.win_rate_label = QLabel("-")
        self.summary_layout.addRow("胜率:", self.win_rate_label)
        
        self.avg_profit_label = QLabel("-")
        self.summary_layout.addRow("平均盈利:", self.avg_profit_label)
        
        self.avg_loss_label = QLabel("-")
        self.summary_layout.addRow("平均亏损:", self.avg_loss_label)
        
        self.profit_loss_ratio_label = QLabel("-")
        self.summary_layout.addRow("盈亏比:", self.profit_loss_ratio_label)
        
        self.max_drawdown_label = QLabel("-")
        self.summary_layout.addRow("最大回撤:", self.max_drawdown_label)
        
        self.sharpe_ratio_label = QLabel("-")
        self.summary_layout.addRow("夏普比率:", self.sharpe_ratio_label)
        
        self.results_tabs.addTab(self.summary_tab, "回测摘要")
        # --- 统计摘要标签页结束 ---
        
        # 交易列表标签页
        self.trades_tab = QWidget()
        trades_layout = QVBoxLayout(self.trades_tab)
        self.trades_table = QTableWidget()
        trades_layout.addWidget(self.trades_table)
        self.results_tabs.addTab(self.trades_tab, "交易记录")
        
        # 资金曲线标签页 (暂时保留，如果K线图能满足需求，可以考虑移除)
        self.equity_chart_tab = QWidget()
        equity_layout = QVBoxLayout(self.equity_chart_tab)
        self.equity_plot_widget = PlotWidget()
        equity_layout.addWidget(self.equity_plot_widget)
        self.results_tabs.addTab(self.equity_chart_tab, "资金曲线")

        results_and_chart_layout.addWidget(self.results_tabs) # Add to the layout, not the widget directly

        # 创建K线和信号图表区域
        self.kline_signal_chart_widget = PlotWidget()
        self.kline_signal_chart_widget.setBackground('w')
        self.kline_signal_chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.kline_signal_chart_widget.setLabel('left', '价格 (每十亿代币)', units='$')
        self.kline_signal_chart_widget.setLabel('bottom', '时间')
        # Add legend
        self.kline_signal_chart_legend = self.kline_signal_chart_widget.addLegend(offset=(-10,10))

        results_and_chart_layout.addWidget(self.kline_signal_chart_widget) # Add to the layout
        
        # Set relative sizes for tabs and kline chart
        results_and_chart_layout.setStretchFactor(self.results_tabs, 1) # Tabs take 1 part
        results_and_chart_layout.setStretchFactor(self.kline_signal_chart_widget, 2) # Kline chart takes 2 parts

        main_splitter.addWidget(results_and_chart_widget)
        
        # 设置分割器初始大小
        total_width = self.width() if self.width() > 0 else 800 # Estimate if not yet shown
        main_splitter.setSizes([int(total_width * 0.25), int(total_width * 0.75)])

        main_layout.addWidget(main_splitter)
    
    def set_token(self, token_data):
        """
        设置当前显示的代币
        
        参数:
            token_data (Dict): 代币数据
        """
        self.token_data = token_data
        
        # 更新UI
        self.token_name_label.setText(token_data.get('name', 'Unknown'))
        self.token_symbol_label.setText(token_data.get('symbol', 'Unknown'))
        
        price = token_data.get('price', 0)
        price_text = f"${price:.8f}" if price < 0.00001 else f"${price:.6f}"
        self.token_price_label.setText(price_text)
        
        self.token_address_label.setText(token_data.get('tokenAddress', 'Unknown'))
        
        # 启用回测按钮
        self.start_button.setEnabled(True)
        
        # 更新状态
        self.status_label.setText(f"已选择代币: {token_data.get('name')} ({token_data.get('symbol')})")
    
    def start_backtest(self):
        """开始回测"""
        if not self.token_data:
            QMessageBox.warning(self, "回测错误", "请先选择一个代币")
            return
        
        # 获取回测参数
        timeframe = self.timeframe_combo.currentText() # 应为 '1m'
        # days = self.days_spin.value() # Days 不再从UI获取
        strategy_name = self.strategy_combo.currentText()
        initial_capital = self.initial_capital_spin.value()
        logger.info(f"BacktestWidget: Initial capital from spinbox: {initial_capital}")

        logger.info(f"BacktestWidget: start_backtest() called. Timeframe: {timeframe}, Strategy: {strategy_name}, Capital: {initial_capital}")
        # logger.info(f"BacktestWidget: start_backtest() called. Timeframe: {timeframe}, Days from UI: {days}, Strategy: {strategy_name}, Capital: {initial_capital}")

        # 更新状态并禁用按钮
        self.status_label.setText("正在准备回测...")
        self.start_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建并启动回测线程
        self.backtest_thread = BacktestThread(
            api_service=self.api_service,
            token_data=self.token_data,
            timeframe=timeframe, # Pass timeframe from UI (locked to 1m)
            # days=days, # Days no longer passed from UI
            strategy_name=strategy_name,
            initial_capital=initial_capital
        )
        
        # 连接信号
        self.backtest_thread.progress_updated.connect(self.update_progress)
        self.backtest_thread.backtest_completed.connect(self.on_backtest_completed)
        self.backtest_thread.backtest_error.connect(self.on_backtest_error)
        
        # 启动线程
        self.backtest_thread.start()
    
    @pyqtSlot(int)
    def update_progress(self, value):
        """
        更新进度条
        
        参数:
            value (int): 进度值
        """
        self.progress_bar.setValue(value)
    
    @pyqtSlot(dict)
    def on_backtest_completed(self, result_payload: dict):
        """处理回测完成信号"""
        logger.info(f"BacktestWidget: on_backtest_completed received initial_capital in summary: {result_payload.get('summary', {}).get('initial_capital')}")
        self.status_label.setText("回测完成！")
        self.start_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.backtest_result = result_payload.get('summary') 
        ohlcv_df = result_payload.get('ohlcv_df')
        trades_df = result_payload.get('trades_df') # trades_df is already a DataFrame here

        self.update_result_ui() # This will use self.backtest_result for summary labels
                                # and self.backtest_result (which contains 'equity_curve') for equity chart.
                                # update_trades_table will now be called with trades_df directly.

        if trades_df is not None:
            self.update_trades_table(trades_df) # Pass trades_df to the updated method
        else:
            self.trades_table.setRowCount(0) # Clear table if no trades_df
            logger.warning("Trades DataFrame is None, clearing trades table.")

        if ohlcv_df is not None and not ohlcv_df.empty and trades_df is not None:
            self.display_kline_with_signals(ohlcv_df, trades_df)
        else:
            logger.warning("回测结果中缺少 'ohlcv_df' 或 'trades_df'，或它们为空，无法绘制K线信号图。")
            self.kline_signal_chart_widget.clear()
            if hasattr(self, 'kline_signal_chart_legend') and self.kline_signal_chart_legend:
                self.kline_signal_chart_legend.clear()
    
    @pyqtSlot(str)
    def on_backtest_error(self, error_message):
        """
        处理回测错误事件
        
        参数:
            error_message (str): 错误消息
        """
        # 显示错误消息
        QMessageBox.critical(self, "回测错误", error_message)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 启用回测按钮
        self.start_button.setEnabled(True)
        
        # 更新状态
        self.status_label.setText(f"回测失败: {error_message}")
    
    def update_result_ui(self):
        """更新结果UI"""
        if self.backtest_result is None:
            logger.error("BacktestWidget: update_result_ui called but self.backtest_result is None.")
            labels_to_clear = [
                self.strategy_name_label, self.initial_capital_label, self.final_capital_label,
                self.total_profit_label, self.profit_percentage_label, self.trade_count_label,
                self.win_rate_label, self.avg_profit_label, self.avg_loss_label,
                self.profit_loss_ratio_label, self.max_drawdown_label, self.sharpe_ratio_label
            ]
            for label in labels_to_clear:
                if hasattr(self, label_name_from_object(label)) and label: # Check if attribute exists and label is not None
                    label.setText("-")
            
            self.equity_plot_widget.clear()
            self.trades_table.setRowCount(0)
            self.kline_signal_chart_widget.clear()
            if hasattr(self, 'kline_signal_chart_legend') and self.kline_signal_chart_legend:
                self.kline_signal_chart_legend.clear()
            return

        logger.info(f"BacktestWidget: update_result_ui using initial_capital from self.backtest_result: {self.backtest_result.get('initial_capital')}")
        self.strategy_name_label.setText(self.backtest_result.get('strategy_name', '-'))
        
        initial_capital = self.backtest_result.get('initial_capital', 0)
        self.initial_capital_label.setText(f"${initial_capital:.2f}")
        
        final_capital = self.backtest_result.get('final_capital', 0)
        self.final_capital_label.setText(f"${final_capital:.2f}")
        
        total_profit = self.backtest_result.get('total_profit', 0)
        profit_color = "#4caf50" if total_profit >= 0 else "#f44336"
        self.total_profit_label.setText(f"<span style='color:{profit_color}'>${total_profit:.2f}</span>")
        
        profit_percentage = self.backtest_result.get('profit_percentage', 0)
        percentage_color = "#4caf50" if profit_percentage >= 0 else "#f44336"
        trade_count = self.backtest_result.get('trade_count', 0)
        self.profit_percentage_label.setText(f"<span style='color:{percentage_color}'>{profit_percentage:.2f}% ({trade_count}笔)</span>")
        
        self.trade_count_label.setText(str(self.backtest_result.get('trade_count', 0)))
        
        win_rate = self.backtest_result.get('win_rate', 0) * 100
        self.win_rate_label.setText(f"{win_rate:.2f}%")
        
        avg_profit = self.backtest_result.get('avg_profit', 0)
        self.avg_profit_label.setText(f"${avg_profit:.2f}")
        
        avg_loss = self.backtest_result.get('avg_loss', 0)
        self.avg_loss_label.setText(f"${avg_loss:.2f}")
        
        profit_loss_ratio = self.backtest_result.get('profit_loss_ratio', 0)
        self.profit_loss_ratio_label.setText(f"{profit_loss_ratio:.2f}")
        
        max_drawdown = self.backtest_result.get('max_drawdown', 0)
        max_drawdown_percentage = self.backtest_result.get('max_drawdown_percentage', 0)
        self.max_drawdown_label.setText(f"${max_drawdown:.2f} ({max_drawdown_percentage:.2f}%)")
        
        sharpe_ratio = self.backtest_result.get('sharpe_ratio', 0)
        self.sharpe_ratio_label.setText(f"{sharpe_ratio:.2f}")
        
        # 更新权益曲线图 (equity_curve is in self.backtest_result['summary'])
        if self.backtest_result and 'equity_curve' in self.backtest_result:
            self.update_equity_chart(self.backtest_result['equity_curve'])
        else:
            self.equity_plot_widget.clear()
            logger.warning("Equity curve data not found in backtest_result summary or backtest_result is None.")
        
        # 更新交易记录表 - 现在由 on_backtest_completed 直接调用，传递 trades_df
        # self.update_trades_table()
    
    def update_equity_chart(self, equity_curve_data: List[Dict]): # Modified to accept data
        """更新权益曲线图"""
        # if not self.backtest_result or 'equity_curve' not in self.backtest_result:
        #     return
        
        self.equity_plot_widget.clear()
        
        # equity_curve = self.backtest_result.get('equity_curve', [])
        if not equity_curve_data:
            logger.warning("Equity curve data is empty or None.")
            return
        
        timestamps = []
        equity_values = []
        
        for point in equity_curve_data:
            timestamps.append(point.get('timestamp', 0))
            equity_values.append(point.get('equity', 0))
        
        # 创建X轴
        x_values = list(range(len(equity_values)))
        
        # 绘制权益曲线
        pen = pg.mkPen(color='#2196f3', width=2)
        self.equity_plot_widget.plot(x_values, equity_values, pen=pen)
        
        # 添加初始资金线
        initial_capital = self.backtest_result.get('initial_capital', 0)
        initial_pen = pg.mkPen(color='#888888', width=1, style=Qt.DashLine)
        self.equity_plot_widget.addLine(y=initial_capital, pen=initial_pen)
    
    def update_trades_table(self, trades_df: pd.DataFrame): # Modified to accept DataFrame
        """更新交易记录表"""
        # if not self.backtest_result or 'trades' not in self.backtest_result:
        #     self.trades_table.setRowCount(0)
        #     return
        
        # trades_df = pd.DataFrame(self.backtest_result.get('trades', [])) # Convert list of dicts to DataFrame

        if trades_df is None or trades_df.empty:
            self.trades_table.setRowCount(0)
            logger.warning("Trades DataFrame is empty or None, clearing trades table.")
            return

        # 设置列名 (如果还没设置的话)
        if self.trades_table.columnCount() == 0: # 只在第一次设置
            headers = ["类型", "时间", "价格", "数量", "成本/收益", "单笔利润", "账户资金"]
            self.trades_table.setColumnCount(len(headers))
            self.trades_table.setHorizontalHeaderLabels(headers)
            self.trades_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            self.trades_table.setEditTriggers(QTableWidget.NoEditTriggers)

        self.trades_table.setRowCount(0) # 清空旧数据
        
        for index, trade_row in trades_df.iterrows(): # Iterate over DataFrame rows
            row_position = self.trades_table.rowCount()
            self.trades_table.insertRow(row_position)
            
            trade_type = trade_row.get('type', '')
            type_item = QTableWidgetItem(trade_type.capitalize())
            type_item.setTextAlignment(Qt.AlignCenter)
            if trade_type == 'buy':
                type_item.setForeground(QColor("#4caf50"))
            elif trade_type == 'sell' or trade_type == 'sell_at_end':
                type_item.setForeground(QColor("#f44336"))
            self.trades_table.setItem(row_position, 0, type_item)
            
            datetime_str = trade_row.get('datetime', '')
            time_item = QTableWidgetItem(datetime_str)
            self.trades_table.setItem(row_position, 1, time_item)
            
            price = trade_row.get('price', 0)
            price_item = QTableWidgetItem(f"${price:.8f}")
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.trades_table.setItem(row_position, 2, price_item)
            
            units = trade_row.get('units', 0)
            units_item = QTableWidgetItem(f"{units:.4f}")
            units_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.trades_table.setItem(row_position, 3, units_item)

            amount_str = "-"
            if trade_type == 'buy':
                amount = trade_row.get('cost', 0)
                amount_str = f"${amount:.2f}"
            elif trade_type == 'sell' or trade_type == 'sell_at_end':
                amount = trade_row.get('proceeds', 0)
                amount_str = f"${amount:.2f}"
            amount_item = QTableWidgetItem(amount_str)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.trades_table.setItem(row_position, 4, amount_item)

            profit_this_trade_val = trade_row.get('profit_this_trade', None) # Default to None to check if key exists
            profit_str = "-"
            profit_item = QTableWidgetItem()
            if profit_this_trade_val is not None: # Only for sell trades basically, where it's calculated
                profit_str = f"${profit_this_trade_val:.2f}"
                if profit_this_trade_val > 0:
                    profit_item.setForeground(QColor("#4caf50"))
                elif profit_this_trade_val < 0:
                    profit_item.setForeground(QColor("#f44336"))
            profit_item.setText(profit_str)
            profit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.trades_table.setItem(row_position, 5, profit_item)
            
            capital_after = trade_row.get('capital_after_trade', 0)
            capital_item = QTableWidgetItem(f"${capital_after:.2f}")
            capital_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.trades_table.setItem(row_position, 6, capital_item)

    def display_kline_with_signals(self, ohlcv_df: pd.DataFrame, trades_df: pd.DataFrame):
        """在PlotWidget上绘制K线和买卖信号"""
        logger.info(f"BacktestWidget: Displaying K-line with {len(trades_df) if trades_df is not None else '0'} trade signals.")
        chart = self.kline_signal_chart_widget
        legend = self.kline_signal_chart_legend
        
        chart.clear()
        if legend:
            legend.clear() 

        if ohlcv_df is None or ohlcv_df.empty:
            logger.warning("OHLCV DataFrame为空，无法绘制K线。")
            return

        timestamps = ohlcv_df['timestamp'].tolist()
        axis = TimeAxisItem(timestamps, orientation='bottom')
        chart.setAxisItems({'bottom': axis})
        
        candlestick_item = CandlestickItem(ohlcv_df)
        chart.addItem(candlestick_item)
        chart.setLabel('left', '价格 (每十亿代币)', units='$')
        scaling_factor = candlestick_item.scaling_factor

        # --- 绘制技术指标 ---
        # MA5 和 MA10
        if 'ma5' in ohlcv_df.columns and 'ma10' in ohlcv_df.columns:
            ma5_pen = pg.mkPen(color='#2196f3', width=1.5)
            ma5_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['ma5'].values * scaling_factor, pen=ma5_pen, name="MA5")
            if legend: legend.addItem(ma5_item, "MA5")
            
            ma10_pen = pg.mkPen(color='#ff9800', width=1.5)
            ma10_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['ma10'].values * scaling_factor, pen=ma10_pen, name="MA10")
            if legend: legend.addItem(ma10_item, "MA10")
            logger.info("BacktestWidget: MA5 and MA10 plotted.")

        # 布林带 (Bollinger Bands)
        if all(col in ohlcv_df.columns for col in ['bb_upper', 'bb_middle', 'bb_lower']):
            bb_upper_pen = pg.mkPen(color='#9c27b0', width=1.5, style=Qt.DashLine)
            bb_middle_pen = pg.mkPen(color='#9c27b0', width=1)
            bb_lower_pen = pg.mkPen(color='#9c27b0', width=1.5, style=Qt.DashLine)
            
            bb_upper_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['bb_upper'].values * scaling_factor, pen=bb_upper_pen, name="BB Upper")
            bb_middle_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['bb_middle'].values * scaling_factor, pen=bb_middle_pen, name="BB Middle")
            bb_lower_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['bb_lower'].values * scaling_factor, pen=bb_lower_pen, name="BB Lower")
            
            if legend: 
                legend.addItem(bb_upper_item, "BB Upper")
                legend.addItem(bb_middle_item, "BB Middle") # Typically middle band is not added to legend, but can be.
                legend.addItem(bb_lower_item, "BB Lower")
            logger.info("BacktestWidget: Bollinger Bands plotted.")

        # VWAP
        if 'vwap' in ohlcv_df.columns:
            vwap_pen = pg.mkPen(color='#FFA500', width=1.8, style=Qt.DotLine) # Orange, dotted line
            vwap_item = chart.plot(x=np.arange(len(ohlcv_df)), y=ohlcv_df['vwap'].values * scaling_factor, pen=vwap_pen, name="VWAP")
            if legend: legend.addItem(vwap_item, "VWAP")
            logger.info("BacktestWidget: VWAP plotted.")

        # --- 技术指标绘制结束 ---

        # 绘制买卖信号
        buy_signals_x, buy_signals_y = [], []
        sell_signals_x, sell_signals_y = [], []

        if trades_df is not None and not trades_df.empty:
            # 创建一个时间戳到索引的映射
            timestamp_to_idx = {ts: i for i, ts in enumerate(ohlcv_df['timestamp'])}

            for _, trade in trades_df.iterrows():
                entry_idx = timestamp_to_idx.get(trade.get('timestamp')) # 假设 trades_df 的时间戳列名为 'timestamp'
                # 如果BaseStrategy中的trade记录用的是 'timestamp'，这里也用 'timestamp'
                # 如果BaseStrategy中的trade记录用的是 'entry_time' 和 'exit_time'，则需要调整
                # 从 BaseStrategy.backtest 的 trades.append 来看，它用 'timestamp' 记录了交易发生的时间点
                # 并且 'type' 可以是 'buy' 或 'sell' (或 'sell_at_end')

                if entry_idx is not None:
                    trade_type = trade.get('type')
                    trade_price = trade.get('price')
                    ohlcv_row_at_trade = ohlcv_df.iloc[entry_idx]

                    if trade_type == 'buy':
                        buy_signals_x.append(entry_idx)
                        buy_signals_y.append(ohlcv_row_at_trade['low'] * 0.998 * candlestick_item.scaling_factor)
                    elif trade_type == 'sell' or trade_type == 'sell_at_end': 
                        sell_signals_x.append(entry_idx)
                        sell_signals_y.append(ohlcv_row_at_trade['high'] * 1.002 * candlestick_item.scaling_factor)

        if buy_signals_x:
            buy_item = pg.ScatterPlotItem(
                x=buy_signals_x, y=buy_signals_y, symbol='t', # Triangle up
                pen=pg.mkPen(None), brush=pg.mkBrush(QColor("#4caf50")), 
                size=10, name="买入信号"
            )
            chart.addItem(buy_item)
            if legend: legend.addItem(buy_item, "买入信号")

        if sell_signals_x:
            sell_item = pg.ScatterPlotItem(
                x=sell_signals_x, y=sell_signals_y, symbol='t', angle=180, # Triangle down
                pen=pg.mkPen(None), brush=pg.mkBrush(QColor("#f44336")), 
                size=10, name="卖出信号"
            )
            chart.addItem(sell_item)
            if legend: legend.addItem(sell_item, "卖出信号")

        chart.setXRange(0, len(ohlcv_df)-1, padding=0.02)
        chart.enableAutoRange(axis='y')
        logger.info("K线和信号图绘制完成。")

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)

# Helper function to get attribute name string (optional, for cleaner code in None check)
def label_name_from_object(obj):
    # This is a bit tricky without more context on how labels are named if not direct attributes.
    # Assuming they are direct attributes for now.
    for name, value in BacktestWidget.__dict__.items(): # Iterate over class attributes if they were static
        if value is obj: return name
    for name, value in obj.__class__.__dict__.items(): # Check instance's class
        if value is obj: return name
    return None # Fallback
