"""
Webhook管理器 - 处理交易信号的Webhook通知功能
"""

import json
import time
import threading
import requests
import os
from typing import Dict, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QLineEdit, QTextEdit, QCheckBox, QSpinBox, QMessageBox
)
from PyQt5.QtCore import QObject, pyqtSignal


class WebhookManager(QObject):
    """Webhook管理器类"""
    
    # 信号定义
    status_updated = pyqtSignal(str, str)  # 状态文本, 样式类型(success/error/normal)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        
        # Webhook配置属性
        self.webhook_url = ""
        self.webhook_enabled = False
        self.webhook_timeout = 10  # 超时时间（秒）
        self.webhook_headers = {"Content-Type": "application/json"}
        
        # 加载设置
        self.load_webhook_settings()
    
    def show_webhook_settings_dialog(self):
        """显示Webhook设置对话框"""
        dialog = QDialog(self.parent_widget)
        dialog.setWindowTitle("🔔 信号Webhook设置")
        dialog.setModal(True)
        dialog.resize(500, 400)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QLabel {
                color: #ecf0f1;
                font-size: 11px;
            }
            QLineEdit {
                background-color: #34495e;
                border: 1px solid #1a252f;
                border-radius: 3px;
                padding: 6px;
                color: #ecf0f1;
                font-size: 10px;
            }
            QTextEdit {
                background-color: #34495e;
                border: 1px solid #1a252f;
                border-radius: 3px;
                padding: 6px;
                color: #ecf0f1;
                font-size: 10px;
            }
            QCheckBox {
                color: #ecf0f1;
                font-size: 11px;
            }
            QSpinBox {
                background-color: #34495e;
                border: 1px solid #1a252f;
                border-radius: 3px;
                padding: 3px;
                color: #ecf0f1;
            }
            QPushButton {
                background-color: #3498db;
                color: #ecf0f1;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        title_label = QLabel("📡 新信号Webhook通知设置")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #f39c12; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 启用复选框
        self.webhook_enabled_checkbox = QCheckBox("启用Webhook通知")
        self.webhook_enabled_checkbox.setChecked(self.webhook_enabled)
        layout.addWidget(self.webhook_enabled_checkbox)
        
        # URL输入
        url_label = QLabel("Webhook URL:")
        layout.addWidget(url_label)
        
        self.webhook_url_input = QLineEdit()
        self.webhook_url_input.setText(self.webhook_url)
        self.webhook_url_input.setPlaceholderText("例如: http://localhost:5001/webhook")
        layout.addWidget(self.webhook_url_input)
        
        # 超时设置
        timeout_layout = QHBoxLayout()
        timeout_label = QLabel("请求超时:")
        timeout_layout.addWidget(timeout_label)
        
        self.webhook_timeout_input = QSpinBox()
        self.webhook_timeout_input.setRange(1, 60)
        self.webhook_timeout_input.setValue(self.webhook_timeout)
        self.webhook_timeout_input.setSuffix(" 秒")
        timeout_layout.addWidget(self.webhook_timeout_input)
        timeout_layout.addStretch()
        
        layout.addLayout(timeout_layout)
        
        # 请求头设置
        headers_label = QLabel("自定义请求头 (JSON格式):")
        layout.addWidget(headers_label)
        
        self.webhook_headers_input = QTextEdit()
        self.webhook_headers_input.setMaximumHeight(80)
        self.webhook_headers_input.setPlainText(json.dumps(self.webhook_headers, indent=2))
        layout.addWidget(self.webhook_headers_input)
        
        # 说明文本
        info_label = QLabel("""
📋 说明:
• 启用后，每当有新的交易信号时会自动发送POST请求到指定URL
• 请求体包含: token_symbol, signal_type, price, timestamp, strategy_name等信息
• 请求头默认为 application/json 格式
• 可以自定义额外的请求头（如API密钥等）
        """)
        info_label.setStyleSheet("color: #bdc3c7; font-size: 9px; margin-top: 10px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 测试按钮
        test_button = QPushButton("🧪 测试Webhook")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        test_button.clicked.connect(lambda: self.test_webhook_connection(dialog))
        layout.addWidget(test_button)
        
        # 按钮栏
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 保存")
        save_button.clicked.connect(lambda: self.save_webhook_settings(dialog))
        
        cancel_button = QPushButton("❌ 取消")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(dialog.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        dialog.setLayout(layout)
        dialog.exec_()
    
    def test_webhook_connection(self, parent_dialog):
        """测试Webhook连接"""
        url = self.webhook_url_input.text().strip()
        if not url:
            QMessageBox.warning(parent_dialog, "警告", "请先输入Webhook URL")
            return
        
        # 准备测试数据
        test_data = {
            "test": True,
            "message": "这是一个测试信号",
            "timestamp": int(time.time()),
            "token_symbol": "TEST",
            "signal_type": "buy",
            "price": 1.234567,
            "strategy_name": "测试策略",
            "source": "manual_test"
        }
        
        # 获取请求头
        try:
            headers_text = self.webhook_headers_input.toPlainText().strip()
            if headers_text:
                headers = json.loads(headers_text)
            else:
                headers = self.webhook_headers
        except json.JSONDecodeError:
            QMessageBox.warning(parent_dialog, "错误", "请求头JSON格式无效")
            return
        
        # 发送测试请求
        self.send_webhook_async(url, test_data, headers, self.webhook_timeout_input.value(), is_test=True)
        
        QMessageBox.information(parent_dialog, "测试", "测试请求已发送！请检查您的服务器日志。")
    
    def save_webhook_settings(self, dialog):
        """保存Webhook设置"""
        try:
            # 获取设置值
            self.webhook_enabled = self.webhook_enabled_checkbox.isChecked()
            self.webhook_url = self.webhook_url_input.text().strip()
            self.webhook_timeout = self.webhook_timeout_input.value()
            
            # 验证和保存请求头
            headers_text = self.webhook_headers_input.toPlainText().strip()
            if headers_text:
                self.webhook_headers = json.loads(headers_text)
            
            # 保存到文件
            self.save_webhook_settings_to_file()
            
            # 发射状态更新信号
            self.status_updated.emit(self.get_status_text(), "success" if self.webhook_enabled else "normal")
            
            QMessageBox.information(dialog, "成功", "Webhook设置已保存！")
            dialog.accept()
            
        except json.JSONDecodeError:
            QMessageBox.warning(dialog, "错误", "请求头JSON格式无效")
        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"保存设置失败: {str(e)}")
    
    def load_webhook_settings(self):
        """从文件加载Webhook设置"""
        try:
            settings_file = "webhook_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                self.webhook_enabled = settings.get('enabled', False)
                self.webhook_url = settings.get('url', '')
                self.webhook_timeout = settings.get('timeout', 10)
                self.webhook_headers = settings.get('headers', {"Content-Type": "application/json"})
                
                print(f"✅ 已加载Webhook设置: 启用={self.webhook_enabled}, URL={self.webhook_url}")
        except Exception as e:
            print(f"❌ 加载Webhook设置失败: {e}")
            # 使用默认设置
            self.webhook_enabled = False
            self.webhook_url = ""
            self.webhook_timeout = 10
            self.webhook_headers = {"Content-Type": "application/json"}
    
    def save_webhook_settings_to_file(self):
        """保存Webhook设置到文件"""
        try:
            settings = {
                'enabled': self.webhook_enabled,
                'url': self.webhook_url,
                'timeout': self.webhook_timeout,
                'headers': self.webhook_headers
            }
            
            with open("webhook_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Webhook设置已保存到文件")
        except Exception as e:
            print(f"❌ 保存Webhook设置失败: {e}")
    
    def get_status_text(self) -> str:
        """获取状态文本"""
        if self.webhook_enabled and self.webhook_url:
            return "📡 Webhook: 已启用"
        else:
            return "📡 Webhook: 未配置"
    
    def get_status_style(self) -> str:
        """获取状态样式"""
        if self.webhook_enabled and self.webhook_url:
            return """
                QLabel {
                    color: #27ae60;
                    font-size: 8px;
                    padding: 2px 4px;
                }
            """
        else:
            return """
                QLabel {
                    color: #bdc3c7;
                    font-size: 8px;
                    padding: 2px 4px;
                }
            """
    
    def send_webhook_async(self, url: str, data: Dict[str, Any], headers: Dict[str, str], timeout: int, is_test: bool = False):
        """异步发送Webhook请求"""
        def send_request():
            try:
                response = requests.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=timeout
                )
                
                if is_test:
                    print(f"🧪 Webhook测试请求结果: {response.status_code} - {response.text[:100]}")
                else:
                    if response.status_code == 200:
                        print(f"✅ Webhook发送成功: {data.get('token_symbol', 'Unknown')} - {data.get('signal_type', 'Unknown')}")
                    else:
                        print(f"⚠️ Webhook响应异常: {response.status_code} - {response.text[:100]}")
                
            except requests.exceptions.Timeout:
                print(f"❌ Webhook请求超时: {url}")
            except requests.exceptions.RequestException as e:
                print(f"❌ Webhook请求失败: {e}")
            except Exception as e:
                print(f"❌ Webhook发送异常: {e}")
        
        # 在后台线程中发送请求
        thread = threading.Thread(target=send_request)
        thread.daemon = True
        thread.start()
    
    def send_signal_webhook(self, signal_data: Dict[str, Any]):
        """发送信号到Webhook"""
        if not self.webhook_enabled or not self.webhook_url:
            return
        
        try:
            # 准备发送的数据
            webhook_data = {
                "timestamp": signal_data.get('timestamp', int(time.time())),
                "token_symbol": signal_data.get('token_symbol', 'Unknown'),
                "token_address": signal_data.get('token_address', ''),
                "signal_type": signal_data.get('signal_type', 'Unknown'),
                "price": signal_data.get('price', 0.0),
                "strategy_name": signal_data.get('strategy_name', 'Unknown'),
                "source": signal_data.get('source', 'Unknown'),
                "chart_index": signal_data.get('chart_index', -1),
                "unique_key": signal_data.get('unique_key', ''),
                "test": False
            }
            
            # 异步发送
            self.send_webhook_async(self.webhook_url, webhook_data, self.webhook_headers, self.webhook_timeout)
            
        except Exception as e:
            print(f"❌ 准备Webhook数据失败: {e}")
    
    def is_enabled(self) -> bool:
        """检查Webhook是否启用"""
        return self.webhook_enabled and bool(self.webhook_url.strip()) 