"""
Portfolio 管理器 - 管理钱包持仓代币的基本信息
负责代币筛选、数据管理和基本状态报告
"""

import logging
from typing import Dict, List, Optional

from PyQt5.QtCore import QObject, pyqtSignal

from api_service import APIService
from config import PORTFOLIO_CONFIG

logger = logging.getLogger('portfolio_monitor')


class PortfolioMonitor(QObject):
    """Portfolio管理器 - 管理钱包持仓代币的基本信息"""

    # 基本状态信号
    status_changed = pyqtSignal(str, int, int)  # status_message, eligible_count, total_count
    
    def __init__(self, api_service: APIService, parent=None):
        """
        初始化Portfolio管理器

        参数:
            api_service (APIService): APIService实例
            parent (QObject, optional): 父对象
        """
        super().__init__(parent)
        self.api_service = api_service

        # 代币数据管理
        self.managed_tokens: Dict[str, Dict] = {}  # token_address -> token_data

        # 配置参数
        self.min_token_value = PORTFOLIO_CONFIG.get("min_monitor_value", 1.0)  # 最小管理价值（美元）

        # 状态
        self.is_active = False
        self.total_tokens = 0
        self.eligible_tokens = 0

        logger.info(f"PortfolioMonitor: 初始化完成，最小管理价值: ${self.min_token_value}")
    
    def update_portfolio(self, token_balances: List[Dict]):
        """
        更新钱包持仓代币数据

        参数:
            token_balances (List[Dict]): 代币余额列表
        """
        try:
            logger.info(f"PortfolioMonitor: 开始分析持仓代币...")

            # 发射开始状态
            self.status_changed.emit("正在分析持仓代币...", 0, 0)

            # 筛选符合条件的代币
            eligible_tokens = self.filter_eligible_tokens(token_balances)

            if not eligible_tokens:
                logger.info(f"PortfolioMonitor: 没有符合条件的代币（价值需大于${self.min_token_value}）")
                self.stop_management()
                # 发射无代币状态
                self.status_changed.emit(f"无符合条件的代币（需>${self.min_token_value}美元）", 0, 0)
                return

            logger.info(f"PortfolioMonitor: 找到 {len(eligible_tokens)} 个符合条件的代币")

            # 更新代币数据
            self.managed_tokens.clear()
            for token in eligible_tokens:
                token_address = token['tokenAddress']
                self.managed_tokens[token_address] = token

            self.is_active = True
            self.total_tokens = len(token_balances)
            self.eligible_tokens = len(eligible_tokens)

            # 发射最终状态
            self.status_changed.emit(f"管理 {self.eligible_tokens} 个代币", self.eligible_tokens, self.total_tokens)

            logger.info(f"PortfolioMonitor: 数据更新完成，管理 {self.eligible_tokens} 个代币")

        except Exception as e:
            logger.error(f"PortfolioMonitor: 更新数据时出错: {e}", exc_info=True)
            # 发射错误状态
            self.status_changed.emit("数据更新失败", 0, 0)
    
    def filter_eligible_tokens(self, token_balances: List[Dict]) -> List[Dict]:
        """
        筛选符合条件的代币

        参数:
            token_balances (List[Dict]): 代币余额列表

        返回:
            List[Dict]: 符合条件的代币列表
        """
        eligible_tokens = []

        for token in token_balances:
            try:
                # 获取代币基本信息
                symbol = token.get('symbol', '')
                token_address = token.get('tokenContractAddress', '')
                balance = float(token.get('balance', 0))
                price = float(token.get('tokenPrice', 0))
                value = balance * price
                is_risk_token = token.get('isRiskToken', False)

                # 跳过无效数据
                if not token_address or not symbol:
                    continue

                # 检查价值条件
                if value < self.min_token_value:
                    logger.debug(f"PortfolioMonitor: {symbol} 价值 ${value:.6f} 低于最小管理值 ${self.min_token_value}")
                    continue

                # 跳过风险代币（可配置）
                if is_risk_token and not PORTFOLIO_CONFIG.get("monitor_risk_tokens", False):
                    logger.debug(f"PortfolioMonitor: {symbol} 是风险代币，跳过管理")
                    continue

                # 跳过主要稳定币（可配置）
                if symbol in ['USDC', 'USDT', 'BUSD'] and not PORTFOLIO_CONFIG.get("monitor_stablecoins", False):
                    logger.debug(f"PortfolioMonitor: {symbol} 是稳定币，跳过管理")
                    continue

                # 构建代币数据
                token_data = {
                    'symbol': symbol,
                    'name': token.get('tokenName', symbol),
                    'tokenAddress': token_address,
                    'address': token_address,  # 为兼容性添加address字段
                    'price': price,
                    'balance': balance,
                    'calculated_value': value,
                    'is_risk_token': is_risk_token
                }

                eligible_tokens.append(token_data)
                logger.info(f"PortfolioMonitor: 符合条件 - {symbol} (${value:.2f})")

            except (ValueError, TypeError) as e:
                logger.warning(f"PortfolioMonitor: 处理代币数据时出错: {e}")
                continue

        # 按价值排序，优先管理高价值代币
        eligible_tokens.sort(key=lambda x: x['calculated_value'], reverse=True)

        # 限制管理数量（避免过多数据影响性能）
        max_manage_count = PORTFOLIO_CONFIG.get("max_monitor_count", 20)
        if len(eligible_tokens) > max_manage_count:
            logger.info(f"PortfolioMonitor: 代币数量超过限制，只管理价值最高的 {max_manage_count} 个")
            eligible_tokens = eligible_tokens[:max_manage_count]

        return eligible_tokens
    
    def get_managed_tokens(self) -> Dict[str, Dict]:
        """
        获取当前管理的代币数据

        返回:
            Dict[str, Dict]: token_address -> token_data
        """
        return self.managed_tokens.copy()

    def get_token_by_address(self, token_address: str) -> Optional[Dict]:
        """
        根据地址获取代币数据

        参数:
            token_address (str): 代币地址

        返回:
            Optional[Dict]: 代币数据，如果不存在则返回None
        """
        return self.managed_tokens.get(token_address)

    def get_tokens_by_value_range(self, min_value: float = 0, max_value: float = float('inf')) -> List[Dict]:
        """
        根据价值范围获取代币列表

        参数:
            min_value (float): 最小价值
            max_value (float): 最大价值

        返回:
            List[Dict]: 符合条件的代币列表
        """
        result = []
        for token_data in self.managed_tokens.values():
            value = token_data.get('calculated_value', 0)
            if min_value <= value <= max_value:
                result.append(token_data)

        # 按价值排序
        result.sort(key=lambda x: x.get('calculated_value', 0), reverse=True)
        return result
    
    def stop_management(self):
        """停止管理"""
        try:
            logger.info(f"PortfolioMonitor: 停止管理...")

            # 清空数据
            self.managed_tokens.clear()

            # 重置状态
            self.is_active = False
            self.total_tokens = 0
            self.eligible_tokens = 0

            # 发射状态更新
            self.status_changed.emit("Portfolio管理已停止", 0, 0)

            logger.info(f"PortfolioMonitor: 管理已停止")

        except Exception as e:
            logger.error(f"PortfolioMonitor: 停止管理时出错: {e}")
    
    def get_summary(self) -> Dict:
        """
        获取管理摘要信息

        返回:
            Dict: 管理摘要
        """
        try:
            summary = {
                'is_active': self.is_active,
                'total_tokens': self.total_tokens,
                'eligible_tokens': self.eligible_tokens,
                'min_value': self.min_token_value,
                'tokens': []
            }

            for token_address, token_data in self.managed_tokens.items():
                token_summary = {
                    'symbol': token_data.get('symbol', 'Unknown'),
                    'address': token_address,
                    'value': token_data.get('calculated_value', 0),
                    'is_risk': token_data.get('is_risk_token', False)
                }
                summary['tokens'].append(token_summary)

            return summary

        except Exception as e:
            logger.error(f"PortfolioMonitor: 获取管理摘要时出错: {e}")
            return {
                'is_active': False,
                'total_tokens': 0,
                'eligible_tokens': 0,
                'min_value': 0,
                'tokens': []
            }

    def __del__(self):
        """析构函数"""
        try:
            logger.debug("PortfolioMonitor: 删除实例")
            self.stop_management()
        except Exception as e:
            logger.debug(f"PortfolioMonitor: 删除实例时出错: {e}")