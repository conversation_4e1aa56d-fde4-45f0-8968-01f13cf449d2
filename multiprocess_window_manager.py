"""
多进程窗口管理器
负责管理trending_window和holdings_panel_window的独立进程
"""
import logging
import sys
import os
import time
from typing import Dict, List, Optional, Callable
from multiprocessing import Process, Event
import signal as signal_module
from datetime import datetime
import json

# 确保当前目录在路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Qt应用程序导入
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import QTimer, pyqtSignal, QObject, pyqtSlot, QThread
from PyQt5.QtGui import QFont

# 导入信号聚合器
from signal_aggregator import SignalAggregator, StrategySignal, get_global_aggregator

logger = logging.getLogger(__name__)


class SignalFileWatcher(QObject):
    """信号文件监视器 - 监视文件变化"""
    signal_received = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.aggregated_file = "aggregated_signals.json"
        self.last_modified = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_file_changes)
        self.timer.start(500)  # 每500ms检查一次
    
    def check_file_changes(self):
        """检查文件变化"""
        try:
            if os.path.exists(self.aggregated_file):
                current_modified = os.path.getmtime(self.aggregated_file)
                if current_modified > self.last_modified:
                    self.last_modified = current_modified
                    self.load_and_emit_signals()
        except Exception as e:
            logger.error(f"SignalFileWatcher: 检查文件变化失败: {e}")
    
    def load_and_emit_signals(self):
        """加载并发射信号"""
        try:
            with open(self.aggregated_file, 'r', encoding='utf-8') as f:
                signals_data = json.load(f)
                for token_address, signal_dict in signals_data.items():
                    self.signal_received.emit(signal_dict)
        except Exception as e:
            logger.error(f"SignalFileWatcher: 加载信号文件失败: {e}")
    
    def stop(self):
        """停止文件监视"""
        self.timer.stop()


def run_trending_window_process(stop_event: Event):
    """在独立进程中运行trending窗口"""
    try:
        # 设置进程名称
        import setproctitle
        setproctitle.setproctitle("TrendingWindow")
    except ImportError:
        pass
    
    # 创建Qt应用程序
    app = QApplication([])
    app.setApplicationName("TrendingWindow")
    
    logger.info("TrendingProcess: 启动trending窗口进程")
    
    try:
        # 导入并创建trending窗口
        from trending_window import TrendingWindow
        from api_service import APIService
        
        # 创建API服务
        api_service = APIService()
        
        # 创建trending窗口
        window = TrendingWindow(api_service=api_service)
        
        # 🔥 集成信号聚合器
        aggregator = get_global_aggregator()
        
        # 🔥 修改策略信号处理，发送到聚合器
        original_signal_handler = window.on_strategy_signal_received
        
        def enhanced_signal_handler(token_address: str, symbol: str, signal_type: str, 
                                  price: float, timestamp: int, strategy_name: str):
            # 调用原始处理函数
            original_signal_handler(token_address, symbol, signal_type, price, timestamp, strategy_name)
            
            # 发送到信号聚合器
            try:
                signal = StrategySignal(
                    token_address=token_address,
                    symbol=symbol,
                    signal_type=signal_type,
                    price=price,
                    timestamp=timestamp,
                    strategy_name=strategy_name,
                    source="trending",
                    confidence=0.7  # 默认置信度
                )
                aggregator.send_signal(signal)
                logger.debug(f"TrendingProcess: 发送信号到聚合器 - {symbol} {signal_type}")
            except Exception as e:
                logger.error(f"TrendingProcess: 发送信号到聚合器失败: {e}")
        
        # 替换信号处理函数
        window.multi_thread_manager.strategy_signal_generated.disconnect(original_signal_handler)
        window.multi_thread_manager.strategy_signal_generated.connect(enhanced_signal_handler)
        
        # 创建定时器检查停止事件
        stop_timer = QTimer()
        def check_stop():
            if stop_event.is_set():
                logger.info("TrendingProcess: 收到停止信号")
                app.quit()
        stop_timer.timeout.connect(check_stop)
        stop_timer.start(500)  # 每500ms检查一次
        
        # 显示窗口并开始数据加载
        window.show()
        window.start_data_loading()
        
        logger.info("TrendingProcess: trending窗口已启动")
        
        # 运行Qt事件循环
        result = app.exec_()
        
        logger.info("TrendingProcess: trending窗口进程退出")
        return result
        
    except Exception as e:
        logger.error(f"TrendingProcess: 运行失败: {e}")
        return 1


def run_holdings_window_process(stop_event: Event):
    """在独立进程中运行holdings窗口"""
    try:
        # 设置进程名称
        import setproctitle
        setproctitle.setproctitle("HoldingsWindow")
    except ImportError:
        pass
    
    # 创建Qt应用程序
    app = QApplication([])
    app.setApplicationName("HoldingsWindow")
    
    logger.info("HoldingsProcess: 启动holdings窗口进程")
    
    try:
        # 导入并创建holdings窗口
        from holdings_panel_window import HoldingsWindow
        from api_service import APIService
        
        # 创建API服务
        api_service = APIService()
        
        # 创建holdings窗口
        window = HoldingsWindow(api_service=api_service)
        
        # 🔥 集成信号聚合器
        aggregator = get_global_aggregator()
        
        # 🔥 修改策略信号处理，发送到聚合器
        original_signal_handler = window.on_strategy_signal_received
        
        def enhanced_signal_handler(token_address: str, symbol: str, signal_type: str, 
                                  price: float, timestamp: int, strategy_name: str):
            # 调用原始处理函数
            original_signal_handler(token_address, symbol, signal_type, price, timestamp, strategy_name)
            
            # 发送到信号聚合器
            try:
                signal = StrategySignal(
                    token_address=token_address,
                    symbol=symbol,
                    signal_type=signal_type,
                    price=price,
                    timestamp=timestamp,
                    strategy_name=strategy_name,
                    source="holdings",
                    confidence=0.8  # holdings信号通常更可靠
                )
                aggregator.send_signal(signal)
                logger.debug(f"HoldingsProcess: 发送信号到聚合器 - {symbol} {signal_type}")
            except Exception as e:
                logger.error(f"HoldingsProcess: 发送信号到聚合器失败: {e}")
        
        # 替换信号处理函数
        window.multi_thread_manager.strategy_signal_generated.disconnect(original_signal_handler)
        window.multi_thread_manager.strategy_signal_generated.connect(enhanced_signal_handler)
        
        # 创建定时器检查停止事件
        stop_timer = QTimer()
        def check_stop():
            if stop_event.is_set():
                logger.info("HoldingsProcess: 收到停止信号")
                app.quit()
        stop_timer.timeout.connect(check_stop)
        stop_timer.start(500)  # 每500ms检查一次
        
        # 显示窗口
        window.show()
        
        logger.info("HoldingsProcess: holdings窗口已启动")
        
        # 运行Qt事件循环
        result = app.exec_()
        
        logger.info("HoldingsProcess: holdings窗口进程退出")
        return result
        
    except Exception as e:
        logger.error(f"HoldingsProcess: 运行失败: {e}")
        return 1


class MultiProcessWindowManager(QMainWindow):
    """多进程窗口管理器主界面"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多进程交易策略分析系统")
        self.setGeometry(100, 100, 800, 600)
        
        # 进程管理
        self.processes = {}
        self.stop_events = {}
        
        # 信号聚合器
        self.aggregator = get_global_aggregator()
        
        # 文件监视器
        self.signal_watcher = SignalFileWatcher()
        self.signal_watcher.signal_received.connect(self.on_aggregated_signal_received)
        
        # 聚合信号历史
        self.aggregated_signals_history = []
        
        self._init_ui()
        self._setup_signal_handlers()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次
        
        logger.info("MultiProcessWindowManager: 初始化完成")
    
    def _init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("多进程交易策略分析系统")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(title_label)
        
        # 控制按钮
        buttons_layout = QHBoxLayout()
        
        self.start_trending_btn = QPushButton("启动趋势分析窗口")
        self.start_trending_btn.clicked.connect(self.start_trending_process)
        buttons_layout.addWidget(self.start_trending_btn)
        
        self.start_holdings_btn = QPushButton("启动持仓分析窗口")
        self.start_holdings_btn.clicked.connect(self.start_holdings_process)
        buttons_layout.addWidget(self.start_holdings_btn)
        
        self.stop_all_btn = QPushButton("停止所有进程")
        self.stop_all_btn.clicked.connect(self.stop_all_processes)
        buttons_layout.addWidget(self.stop_all_btn)
        
        layout.addLayout(buttons_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("background-color: #ecf0f1; padding: 10px; border: 1px solid #bdc3c7;")
        layout.addWidget(self.status_label)
        
        # 聚合信号显示
        self.signals_label = QLabel("聚合策略信号:")
        self.signals_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.signals_label)
        
        # 信号历史列表
        self.signals_display = QLabel("暂无信号")
        self.signals_display.setStyleSheet("""
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border: 1px solid #34495e;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        """)
        self.signals_display.setWordWrap(True)
        self.signals_display.setMinimumHeight(300)
        layout.addWidget(self.signals_display)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info("MultiProcessWindowManager: 收到退出信号，正在关闭...")
            self.stop_all_processes()
            self.close()
        
        signal_module.signal(signal_module.SIGINT, signal_handler)
        signal_module.signal(signal_module.SIGTERM, signal_handler)
    
    def start_trending_process(self):
        """启动trending进程"""
        if "trending" in self.processes and self.processes["trending"].is_alive():
            logger.info("MultiProcessWindowManager: trending进程已在运行")
            return
        
        try:
            # 创建进程通信组件
            stop_event = Event()
            
            # 创建进程
            process = Process(
                target=run_trending_window_process,
                args=(stop_event,),
                name="TrendingWindow"
            )
            
            # 启动进程
            process.start()
            
            # 存储进程信息
            self.processes["trending"] = process
            self.stop_events["trending"] = stop_event
            
            logger.info("MultiProcessWindowManager: trending进程已启动")
            self.start_trending_btn.setText("趋势分析进程 (运行中)")
            self.start_trending_btn.setEnabled(False)
            
        except Exception as e:
            logger.error(f"MultiProcessWindowManager: 启动trending进程失败: {e}")
    
    def start_holdings_process(self):
        """启动holdings进程"""
        if "holdings" in self.processes and self.processes["holdings"].is_alive():
            logger.info("MultiProcessWindowManager: holdings进程已在运行")
            return
        
        try:
            # 创建进程通信组件
            stop_event = Event()
            
            # 创建进程
            process = Process(
                target=run_holdings_window_process,
                args=(stop_event,),
                name="HoldingsWindow"
            )
            
            # 启动进程
            process.start()
            
            # 存储进程信息
            self.processes["holdings"] = process
            self.stop_events["holdings"] = stop_event
            
            logger.info("MultiProcessWindowManager: holdings进程已启动")
            self.start_holdings_btn.setText("持仓分析进程 (运行中)")
            self.start_holdings_btn.setEnabled(False)
            
        except Exception as e:
            logger.error(f"MultiProcessWindowManager: 启动holdings进程失败: {e}")
    
    def stop_all_processes(self):
        """停止所有进程"""
        logger.info("MultiProcessWindowManager: 正在停止所有进程...")
        
        # 发送停止信号
        for name, stop_event in self.stop_events.items():
            stop_event.set()
        
        # 等待进程结束
        for name, process in list(self.processes.items()):
            if process.is_alive():
                logger.info(f"MultiProcessWindowManager: 等待 {name} 进程结束...")
                process.join(timeout=5)
                
                if process.is_alive():
                    logger.warning(f"MultiProcessWindowManager: 强制终止 {name} 进程")
                    process.terminate()
                    process.join()
                
                logger.info(f"MultiProcessWindowManager: {name} 进程已结束")
        
        # 清理进程信息
        self.processes.clear()
        self.stop_events.clear()
        
        # 重置按钮状态
        self.start_trending_btn.setText("启动趋势分析窗口")
        self.start_trending_btn.setEnabled(True)
        self.start_holdings_btn.setText("启动持仓分析窗口")
        self.start_holdings_btn.setEnabled(True)
        
        logger.info("MultiProcessWindowManager: 所有进程已停止")
    
    @pyqtSlot(dict)
    def on_aggregated_signal_received(self, signal_data: Dict):
        """接收聚合策略信号"""
        try:
            signal = StrategySignal.from_dict(signal_data)
            
            # 检查是否是新信号（避免重复）
            signal_id = f"{signal.token_address}_{signal.timestamp}"
            if hasattr(self, '_processed_signals') and signal_id in self._processed_signals:
                return
            
            if not hasattr(self, '_processed_signals'):
                self._processed_signals = set()
            self._processed_signals.add(signal_id)
            
            # 保持处理过的信号ID在合理范围内
            if len(self._processed_signals) > 1000:
                # 移除最老的一半
                sorted_ids = sorted(self._processed_signals)
                self._processed_signals = set(sorted_ids[500:])
            
            self.aggregated_signals_history.append(signal)
            
            # 保持历史记录在合理范围内
            if len(self.aggregated_signals_history) > 50:
                self.aggregated_signals_history.pop(0)
            
            logger.info(f"MultiProcessWindowManager: 收到聚合信号 - {signal.symbol} {signal.signal_type} (来源: {signal.source})")
            
            # 更新显示
            self.update_signals_display()
            
        except Exception as e:
            logger.error(f"MultiProcessWindowManager: 处理聚合信号失败: {e}")
    
    def update_signals_display(self):
        """更新信号显示"""
        try:
            if not self.aggregated_signals_history:
                self.signals_display.setText("暂无聚合信号")
                return
            
            # 显示最近的10个信号
            recent_signals = self.aggregated_signals_history[-10:]
            
            display_text = []
            for signal in reversed(recent_signals):
                time_str = datetime.fromtimestamp(signal.timestamp).strftime("%H:%M:%S")
                confidence_str = f"{signal.confidence:.2f}" if signal.confidence > 0 else "N/A"
                
                # 根据信号类型设置颜色
                if signal.signal_type == "buy":
                    color = "#27ae60"  # 绿色
                elif signal.signal_type == "sell":
                    color = "#e74c3c"  # 红色
                elif signal.signal_type == "hold":
                    color = "#f39c12"  # 橙色
                else:
                    color = "#95a5a6"  # 灰色
                
                sources = ", ".join(signal.metadata.get('original_sources', [signal.source]))
                
                display_text.append(
                    f"<div style='margin-bottom: 8px; padding: 5px; border-left: 3px solid {color};'>"
                    f"<b>{signal.symbol}</b> | "
                    f"<span style='color: {color}; font-weight: bold;'>{signal.signal_type.upper()}</span> | "
                    f"${signal.price:.6f} | {time_str}<br/>"
                    f"<small>来源: {sources} | 置信度: {confidence_str} | {signal.strategy_name}</small>"
                    f"</div>"
                )
            
            self.signals_display.setText("".join(display_text))
            
        except Exception as e:
            logger.error(f"MultiProcessWindowManager: 更新信号显示失败: {e}")
    
    def update_status(self):
        """更新状态显示"""
        try:
            active_processes = []
            for name, process in self.processes.items():
                if process.is_alive():
                    active_processes.append(name)
            
            # 获取聚合器统计
            stats = self.aggregator.get_signal_statistics()
            
            if active_processes:
                status_text = f"运行中: {', '.join(active_processes)} | "
                status_text += f"总信号: {stats.get('total_signals', 0)} | "
                status_text += f"代币数: {stats.get('unique_tokens', 0)} | "
                status_text += f"聚合信号: {len(self.aggregated_signals_history)}"
            else:
                status_text = "状态: 所有进程已停止"
            
            self.status_label.setText(status_text)
            
        except Exception as e:
            logger.error(f"MultiProcessWindowManager: 更新状态失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        logger.info("MultiProcessWindowManager: 正在关闭...")
        
        # 停止文件监视器
        if hasattr(self, 'signal_watcher'):
            self.signal_watcher.stop()
        
        # 停止所有进程
        self.stop_all_processes()
        
        # 停止状态定时器
        if hasattr(self, 'status_timer'):
            self.status_timer.stop()
        
        super().closeEvent(event)


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # 创建Qt应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("MultiProcessTradingSystem")
    
    # 创建主窗口
    manager = MultiProcessWindowManager()
    manager.show()
    
    # 运行应用程序
    try:
        result = app.exec_()
        logger.info("MultiProcessWindowManager: 应用程序正常退出")
        return result
    except KeyboardInterrupt:
        logger.info("MultiProcessWindowManager: 收到键盘中断信号")
        return 0
    finally:
        # 关闭全局聚合器
        from signal_aggregator import shutdown_global_aggregator
        shutdown_global_aggregator()


if __name__ == '__main__':
    sys.exit(main()) 