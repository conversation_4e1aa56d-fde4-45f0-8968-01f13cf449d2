# Portfolio 子模块 PRD - Solana 代币资产管理组件

## 1. 模块概述

### 1.1 模块名称
Portfolio Widget Module - Solana代币资产管理组件

### 1.2 模块定位
作为更大的加密货币交易系统的一个子模块，专门负责Solana链上代币资产的展示、管理和快速交易功能。

### 1.3 在系统中的位置
- **父级系统**：BT-GUI 加密货币交易终端
- **模块路径**：`ui/portfolio_widget.py`
- **依赖关系**：
  - `okx_dex_client.py` - API客户端
  - `config.py` - 配置管理
  - PyQt5 - UI框架

### 1.4 目标用户
- Solana生态的加密货币投资者
- DeFi交易者
- 需要批量管理代币资产的用户
- 空投代币接收者

### 1.5 核心价值
- **实时监控**：实时查看钱包代币余额和价值
- **智能交易**：一键卖出功能，支持智能滑点和批量操作
- **风险管理**：自动识别和标记风险代币
- **专业界面**：提供专业的交易员界面体验

## 2. 功能需求

### 2.1 钱包连接与管理

#### 2.1.1 钱包地址输入
- 支持手动输入Solana钱包地址
- 地址格式验证（44位Base58编码）
- 记住上次使用的钱包地址

#### 2.1.2 链选择
- 支持多链切换（当前主要为Solana）
- 链ID映射管理

### 2.2 资产展示

#### 2.2.1 总资产概览
- 显示钱包总价值（美元）
- 实时价格更新
- API连接状态指示
- 最后更新时间显示

#### 2.2.2 代币列表
- 表格形式展示所有代币：
  - 代币名称
  - 符号
  - 余额
  - 单价
  - 总价值
  - 合约地址
  - 操作按钮
- 支持按价值排序
- 智能过滤低价值代币（<$0.01）

### 2.3 风险管理

#### 2.3.1 风险代币识别
- 自动标记风险代币（空投币、貔貅盘等）
- 视觉警告标识（⚠️图标和红色高亮）
- 可选择隐藏/显示风险代币

#### 2.3.2 风险提示
- 卖出风险代币时额外确认
- 显示风险类型说明

### 2.4 交易功能

#### 2.4.1 一键卖出
- 支持多种卖出比例：
  - 预设比例：10%、25%、50%、75%、99%、100%
  - 低价值代币测试卖出（1%）
- 目标代币选择：
  - SOL（默认）
  - USDC

#### 2.4.2 智能卖出
- 根据代币价值自动推荐卖出比例：
  - 高价值(>$10)：建议25%
  - 中等价值($1-10)：建议50%
  - 低价值($0.1-1)：建议75%
  - 极低价值(<$0.1)：建议10%

#### 2.4.3 滑点管理
- 自适应滑点计算：
  - 根据代币价值动态调整
  - 根据卖出比例微调
  - 范围：1%-20%
- 滑点过大时自动重试机制

#### 2.4.4 交易确认
- 卖出前显示确认对话框：
  - 代币信息
  - 卖出数量和比例
  - 预估收到金额
  - 预计滑点
- 交易后显示结果：
  - 交易哈希
  - 实际收到金额

### 2.5 自动化功能

#### 2.5.1 自动刷新
- 可配置刷新间隔（默认30秒）
- 手动/自动模式切换
- 刷新状态指示

#### 2.5.2 批量操作
- 支持快速连续卖出多个代币
- 操作队列管理

## 3. 技术架构

### 3.1 前端技术栈
- **框架**：PyQt5
- **样式**：自定义QSS样式表
- **图表**：QTableWidget专业表格组件

### 3.2 后端集成
- **API**：OKX DEX API
- **区块链**：Solana (Chain ID: 501)
- **数据获取**：
  - 总价值查询
  - 代币余额查询
  - 交易报价
  - 交易执行

### 3.3 核心模块
```
├── portfolio_widget.py      # 主界面组件
├── okx_dex_client.py       # API客户端
├── config.py               # 配置管理
└── data_thread.py          # 异步数据获取
```

### 3.4 数据流程
```mermaid
graph TD
    A[用户输入钱包地址] --> B[API请求]
    B --> C[获取总价值]
    B --> D[获取代币列表]
    C --> E[更新UI显示]
    D --> E
    E --> F[用户选择卖出]
    F --> G[获取交易报价]
    G --> H[确认交易]
    H --> I[执行交易]
    I --> J[更新余额]
```

## 4. 用户界面设计

### 4.1 整体布局
- **顶部**：钱包设置栏（地址、链选择、过滤选项、操作按钮）
- **左侧**：总资产面板（280px宽）
- **右侧**：代币列表（自适应宽度）
- **底部**：状态栏

### 4.2 视觉设计
- **主题**：深色专业交易界面
- **配色**：
  - 背景：#2c3e50（深蓝灰）
  - 强调：#3498db（亮蓝）
  - 成功：#27ae60（绿色）
  - 警告：#e74c3c（红色）
- **字体**：
  - 标题：Arial Bold 12px
  - 正文：Arial 11px
  - 数值：等宽字体

### 4.3 交互设计
- 鼠标悬停效果
- 按钮点击反馈
- 加载状态指示
- 错误提示弹窗

## 5. 性能优化

### 5.1 数据加载优化
- 批量处理代币数据（每10个一批）
- 限制显示数量（最多100个）
- 异步数据获取
- UI更新节流

### 5.2 交易优化
- 智能滑点计算减少失败率
- 失败自动重试
- 交易状态实时反馈

## 6. 安全考虑

### 6.1 数据安全
- 只读钱包地址
- 无私钥存储
- API请求加密

### 6.2 交易安全
- 交易前多重确认
- 风险代币警告
- 保留最小SOL余额提醒

## 7. API接口规范

### 7.1 健康检查
```python
GET /health
Response: {
    "success": true,
    "message": "Service is healthy"
}
```

### 7.2 获取总价值
```python
POST /total-value
Request: {
    "address": "wallet_address",
    "chains": "501"
}
Response: {
    "success": true,
    "data": {
        "totalValue": "1234.56"
    }
}
```

### 7.3 获取代币余额
```python
POST /token-balances
Request: {
    "address": "wallet_address",
    "chains": "501",
    "exclude_risk_token": "0"
}
Response: {
    "success": true,
    "data": [
        {
            "symbol": "SOL",
            "balance": "10.5",
            "tokenPrice": "100.25",
            "calculated_value": 1052.625,
            "isRiskToken": false
        }
    ]
}
```

## 8. 错误处理

### 8.1 常见错误类型
- **地址格式错误**：提示正确的地址格式要求
- **API限流**：提示稍后重试
- **网络错误**：提示检查网络连接
- **交易失败**：提供详细错误原因和解决方案

### 8.2 错误恢复机制
- 自动重试（最多3次）
- 降级处理
- 错误日志记录

## 9. 未来规划

### 9.1 功能扩展
- [ ] 支持更多链（ETH、BSC等）
- [ ] 批量卖出功能
- [ ] 价格提醒
- [ ] 历史记录
- [ ] 数据导出

### 9.2 体验优化
- [ ] 多语言支持
- [ ] 自定义主题
- [ ] 快捷键支持
- [ ] 移动端适配

### 9.3 高级功能
- [ ] DeFi收益计算
- [ ] 自动化交易策略
- [ ] 多钱包管理
- [ ] 社区功能

## 10. 项目里程碑

### Phase 1 - MVP（已完成）
- ✅ 基础钱包连接
- ✅ 代币余额展示
- ✅ 一键卖出功能
- ✅ 风险代币识别

### Phase 2 - 优化（进行中）
- ✅ 智能卖出
- ✅ 自适应滑点
- ✅ 性能优化
- ⏳ 用户体验提升

### Phase 3 - 扩展（计划中）
- ⏳ 多链支持
- ⏳ 高级交易功能
- ⏳ 数据分析功能

## 11. 成功指标

### 11.1 用户指标
- 日活跃用户数(DAU)
- 月活跃用户数(MAU)
- 用户留存率

### 11.2 业务指标
- 交易成功率
- 平均交易金额
- 支持的代币种类

### 11.3 技术指标
- API响应时间
- 界面加载速度
- 错误率

## 12. 竞品分析

### 12.1 主要竞品
- **Phantom钱包**：功能全面但缺乏批量管理
- **Jupiter聚合器**：专注交易但缺乏资产管理
- **Step Finance**：数据分析强但操作复杂

### 12.2 竞争优势
- 专注于批量代币管理
- 一键卖出简化操作
- 智能风险识别
- 专业交易界面

## 13. 用户反馈收集

### 13.1 反馈渠道
- 应用内反馈按钮
- Discord社区
- Twitter
- Github Issues

### 13.2 重点关注
- 交易成功率
- 界面易用性
- 功能需求
- 性能问题

## 14. 模块接口与集成

### 14.1 模块对外接口
```python
class PortfolioWidget(QWidget):
    """Portfolio主组件 - 可被主窗口直接使用"""
    
    def __init__(self, parent=None):
        """初始化组件，parent为父窗口"""
        
    def refresh_portfolio(self):
        """手动刷新资产数据"""
        
    def toggle_auto_refresh(self):
        """切换自动刷新模式"""
```

### 14.2 与主系统集成方式
```python
# 在主窗口中使用
from ui.portfolio_widget import PortfolioWidget

# 创建实例
self.portfolio_widget = PortfolioWidget(self)

# 添加到布局
main_layout.addWidget(self.portfolio_widget)
```

### 14.3 配置集成
模块使用独立的配置字典 `PORTFOLIO_CONFIG`：
```python
PORTFOLIO_CONFIG = {
    "okx_dex_api_url": "https://www.okx.com/api/v5/dex/aggregator",
    "default_wallet_address": "",
    "refresh_interval": 30000,
    "supported_chains": {
        "501": "Solana"
    }
}
```

### 14.4 事件通信
模块内部使用PyQt信号进行事件通信：
- `total_value_updated` - 总价值更新
- `token_balances_updated` - 代币余额更新
- `api_status_updated` - API状态更新
- `error_occurred` - 错误发生

### 14.5 模块依赖
- **必需依赖**：
  - PyQt5 >= 5.15
  - requests >= 2.25
  - Python >= 3.8
- **API依赖**：
  - OKX DEX API (外部服务)

---

**文档版本**: 1.0  
**更新日期**: 2024-01-20  
**模块作者**: Portfolio Module Team
**主项目**: BT-GUI Trading Terminal 