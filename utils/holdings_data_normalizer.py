"""
持仓数据标准化工具模块

将 holdings_list 数据格式转换为 update_background_charts 方法期望的 display_tokens 格式。
确保所有必需字段都存在，缺失的数据用合理的默认值或空值填充。
"""

from typing import List, Dict, Any, Optional
import logging
import time

logger = logging.getLogger(__name__)


class HoldingsDataNormalizer:
    """持仓数据标准化器"""
    
    # update_background_charts 期望的字段映射和默认值
    REQUIRED_FIELDS = {
        'tokenAddress': str,  # 必需字段，用作唯一标识符
        'symbol': str,        # 代币符号
        'name': str,          # 代币名称
        'price': float,       # 当前价格
        'marketCap': float,   # 市值
        'volume24h': float,   # 24小时交易量
        'holders': int,       # 持有者数量
        'tweetCount': int,    # 推文数量
        'totalTweets': int,   # 总推文数
        'smartBuyStats': int, # 聪明钱统计
        'vmRatio': float,     # V/M 比率
        'priceChange5m': float, # 5分钟价格变化
        'tokenImage': str,    # 代币图标URL
    }
    
    # 默认值配置
    DEFAULT_VALUES = {
        'tokenAddress': "",
        'symbol': "UNKNOWN",
        'name': "Unknown Token",
        'price': 0.0,
        'marketCap': 0.0,
        'volume24h': 0.0,
        'holders': 0,
        'tweetCount': 0,
        'totalTweets': 0,
        'smartBuyStats': 0,
        'vmRatio': 0.0,
        'priceChange5m': 0.0,
        'tokenImage': "",
    }
    
    # holdings_list 到 display_tokens 的字段映射
    FIELD_MAPPING = {
        # holdings_list 字段 -> display_tokens 字段
        'address': 'tokenAddress',      # 代币地址映射
        'symbol': 'symbol',             # 代币符号保持一致
        'name': 'name',                 # 代币名称保持一致
        'current_price': 'price',       # 当前价格映射
        'image': 'tokenImage',          # 图标URL映射
        'market_cap': 'marketCap',      # 市值映射
        'total_volume': 'volume24h',    # 交易量映射
    }
    
    @staticmethod
    def normalize_holdings_to_display_tokens(holdings_list: List[Dict]) -> List[Dict]:
        """
        将持仓数据标准化为 display_tokens 格式
        
        Args:
            holdings_list: 原始持仓数据列表
            
        Returns:
            List[Dict]: 标准化后的 display_tokens 格式数据
        """
        if not holdings_list:
            logger.info("HoldingsDataNormalizer: 输入的 holdings_list 为空")
            return []
        
        logger.info(f"HoldingsDataNormalizer: 开始标准化 {len(holdings_list)} 个持仓数据")
        
        normalized_tokens = []
        
        for idx, holding_data in enumerate(holdings_list):
            try:
                normalized_token = HoldingsDataNormalizer._normalize_single_holding(holding_data, idx)
                if normalized_token:
                    normalized_tokens.append(normalized_token)
            except Exception as e:
                logger.error(f"HoldingsDataNormalizer: 标准化第 {idx} 个持仓数据失败: {e}")
                continue
        
        logger.info(f"HoldingsDataNormalizer: 成功标准化 {len(normalized_tokens)} 个代币数据")
        return normalized_tokens
    
    @staticmethod
    def _normalize_single_holding(holding_data: Dict, index: int) -> Optional[Dict]:
        """
        标准化单个持仓数据
        
        Args:
            holding_data: 单个持仓数据字典
            index: 数据索引（用于日志）
            
        Returns:
            Optional[Dict]: 标准化后的单个代币数据，失败返回 None
        """
        if not isinstance(holding_data, dict):
            logger.warning(f"HoldingsDataNormalizer: 第 {index} 个数据不是字典格式: {type(holding_data)}")
            return None
        
        # 创建标准化后的代币数据，先使用默认值填充
        normalized_token = HoldingsDataNormalizer.DEFAULT_VALUES.copy()
        
        # 应用字段映射
        for holdings_field, display_field in HoldingsDataNormalizer.FIELD_MAPPING.items():
            if holdings_field in holding_data:
                raw_value = holding_data[holdings_field]
                normalized_value = HoldingsDataNormalizer._convert_field_value(
                    raw_value, display_field, holdings_field
                )
                normalized_token[display_field] = normalized_value
        
        # 特殊处理：从多个可能的字段中获取代币地址
        token_address = HoldingsDataNormalizer._extract_token_address(holding_data)
        if token_address:
            normalized_token['tokenAddress'] = token_address
        else:
            logger.warning(f"HoldingsDataNormalizer: 第 {index} 个数据缺少有效的代币地址")
            return None
        
        # 特殊处理：从持仓数据中估算价格（如果没有直接的价格字段）
        estimated_price = HoldingsDataNormalizer._estimate_price_from_holdings(holding_data)
        if estimated_price > 0:
            normalized_token['price'] = estimated_price
        
        # 特殊处理：设置合理的符号和名称
        symbol = HoldingsDataNormalizer._extract_symbol(holding_data)
        if symbol:
            normalized_token['symbol'] = symbol
        
        name = HoldingsDataNormalizer._extract_name(holding_data)
        if name:
            normalized_token['name'] = name
        
        # 添加来源标识，便于调试
        normalized_token['_source'] = 'holdings'
        normalized_token['_original_index'] = index
        
        logger.debug(f"HoldingsDataNormalizer: 标准化完成 - {normalized_token['symbol']} ({normalized_token['tokenAddress'][:10]}...)")
        
        return normalized_token
    
    @staticmethod
    def _extract_token_address(holding_data: Dict) -> str:
        """从持仓数据中提取代币地址"""
        # 尝试多个可能的地址字段
        address_fields = ['address', 'tokenAddress', 'token_address', 'contractAddress', 'contract_address']
        
        for field in address_fields:
            if field in holding_data and holding_data[field]:
                address = str(holding_data[field]).strip()
                if address and address.lower() != 'unknown':
                    return address
        
        return ""
    
    @staticmethod
    def _extract_symbol(holding_data: Dict) -> str:
        """从持仓数据中提取代币符号"""
        symbol_fields = ['symbol', 'Symbol', 'ticker', 'Ticker']
        
        for field in symbol_fields:
            if field in holding_data and holding_data[field]:
                symbol = str(holding_data[field]).strip()  # 🔥 FIXED: 移除.upper()，保持原始大小写
                if symbol and symbol.upper() != 'UNKNOWN':
                    return symbol
        
        return "UNKNOWN"
    
    @staticmethod
    def _extract_name(holding_data: Dict) -> str:
        """从持仓数据中提取代币名称"""
        name_fields = ['name', 'Name', 'full_name', 'fullName', 'token_name', 'tokenName']
        
        for field in name_fields:
            if field in holding_data and holding_data[field]:
                name = str(holding_data[field]).strip()
                if name and name.lower() != 'unknown':
                    return name
        
        # 如果没有名称，使用符号作为名称
        symbol = HoldingsDataNormalizer._extract_symbol(holding_data)
        if symbol and symbol != 'UNKNOWN':
            return f"{symbol} Token"
        
        return "Unknown Token"
    
    @staticmethod
    def _estimate_price_from_holdings(holding_data: Dict) -> float:
        """从持仓数据中估算代币价格"""
        try:
            # 尝试直接获取价格字段
            price_fields = ['price', 'current_price', 'currentPrice', 'market_price', 'marketPrice']
            for field in price_fields:
                if field in holding_data and holding_data[field] is not None:
                    price = float(holding_data[field])
                    if price > 0:
                        return price
            
            # 如果没有直接价格，尝试通过 value_usd / amount 计算
            value_usd = holding_data.get('value_usd', 0)
            amount = holding_data.get('amount', 0) or holding_data.get('balance', 0)
            
            if value_usd and amount:
                try:
                    value_float = float(value_usd)
                    amount_float = float(amount)
                    if amount_float > 0:
                        estimated_price = value_float / amount_float
                        logger.debug(f"HoldingsDataNormalizer: 估算价格 ${estimated_price:.8f} (value_usd: {value_float}, amount: {amount_float})")
                        return estimated_price
                except (ValueError, ZeroDivisionError, TypeError):
                    pass
            
        except Exception as e:
            logger.debug(f"HoldingsDataNormalizer: 估算价格时出错: {e}")
        
        return 0.0
    
    @staticmethod
    def _convert_field_value(raw_value: Any, target_field: str, source_field: str) -> Any:
        """
        转换字段值为目标类型
        
        Args:
            raw_value: 原始值
            target_field: 目标字段名
            source_field: 源字段名
            
        Returns:
            Any: 转换后的值
        """
        if raw_value is None:
            return HoldingsDataNormalizer.DEFAULT_VALUES.get(target_field, "")
        
        # 获取目标字段的期望类型
        expected_type = HoldingsDataNormalizer.REQUIRED_FIELDS.get(target_field, str)
        
        try:
            if expected_type == str:
                return str(raw_value).strip()
            elif expected_type == float:
                return float(raw_value) if raw_value != "" else 0.0
            elif expected_type == int:
                return int(float(raw_value)) if raw_value != "" else 0
            else:
                return raw_value
        except (ValueError, TypeError) as e:
            logger.debug(f"HoldingsDataNormalizer: 字段 {source_field} -> {target_field} 转换失败: {e}, 使用默认值")
            return HoldingsDataNormalizer.DEFAULT_VALUES.get(target_field, "")
    
    @staticmethod
    def validate_normalized_tokens(normalized_tokens: List[Dict]) -> List[Dict]:
        """
        验证标准化后的代币数据
        
        Args:
            normalized_tokens: 标准化后的代币数据列表
            
        Returns:
            List[Dict]: 验证通过的代币数据列表
        """
        valid_tokens = []
        
        for idx, token in enumerate(normalized_tokens):
            if HoldingsDataNormalizer._is_valid_token(token, idx):
                valid_tokens.append(token)
        
        logger.info(f"HoldingsDataNormalizer: 验证完成，{len(valid_tokens)}/{len(normalized_tokens)} 个代币数据有效")
        return valid_tokens
    
    @staticmethod
    def _is_valid_token(token: Dict, index: int) -> bool:
        """验证单个代币数据是否有效"""
        # 检查必需字段
        if not token.get('tokenAddress'):
            logger.warning(f"HoldingsDataNormalizer: 第 {index} 个代币缺少 tokenAddress")
            return False
        
        if not token.get('symbol') or token['symbol'] == 'UNKNOWN':
            logger.warning(f"HoldingsDataNormalizer: 第 {index} 个代币缺少有效的 symbol")
            return False
        
        # 检查数据类型
        for field, expected_type in HoldingsDataNormalizer.REQUIRED_FIELDS.items():
            if field in token:
                if not isinstance(token[field], expected_type):
                    logger.warning(f"HoldingsDataNormalizer: 第 {index} 个代币的 {field} 字段类型错误: {type(token[field])} != {expected_type}")
                    return False
        
        return True
    
    @staticmethod
    def get_conversion_summary(holdings_list: List[Dict], normalized_tokens: List[Dict]) -> Dict:
        """
        获取转换摘要信息
        
        Args:
            holdings_list: 原始持仓数据
            normalized_tokens: 标准化后的数据
            
        Returns:
            Dict: 转换摘要
        """
        return {
            'input_count': len(holdings_list),
            'output_count': len(normalized_tokens),
            'success_rate': len(normalized_tokens) / len(holdings_list) * 100 if holdings_list else 0,
            'conversion_time': time.time(),
            'source': 'holdings_data_normalizer'
        }


def normalize_holdings_for_background_charts(holdings_list: List[Dict]) -> List[Dict]:
    """
    便捷函数：将持仓数据标准化为可用于 update_background_charts 的格式
    
    Args:
        holdings_list: 原始持仓数据列表
        
    Returns:
        List[Dict]: 标准化并验证后的代币数据列表
    """
    logger.info("开始标准化持仓数据用于后台图表分析")
    
    # 标准化数据
    normalized_tokens = HoldingsDataNormalizer.normalize_holdings_to_display_tokens(holdings_list)
    
    # 验证数据
    valid_tokens = HoldingsDataNormalizer.validate_normalized_tokens(normalized_tokens)
    
    # 生成摘要
    summary = HoldingsDataNormalizer.get_conversion_summary(holdings_list, valid_tokens)
    logger.info(f"持仓数据标准化完成: {summary['input_count']} -> {summary['output_count']} ({summary['success_rate']:.1f}% 成功率)")
    
    return valid_tokens


# 示例用法和测试
if __name__ == "__main__":
    # 模拟持仓数据
    sample_holdings = [
        {
            'address': 'So11111111111111111111111111111111111111112',
            'symbol': 'SOL',
            'name': 'Solana',
            'amount': '1.5',
            'value_usd': '150.75',
            'image': 'https://example.com/sol.png'
        },
        {
            'address': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            'symbol': 'USDC',
            'name': 'USD Coin',
            'amount': '100.0',
            'value_usd': '100.0'
        }
    ]
    
    # 测试标准化
    result = normalize_holdings_for_background_charts(sample_holdings)
    
    print(f"原始数据: {len(sample_holdings)} 个")
    print(f"标准化结果: {len(result)} 个")
    for token in result:
        print(f"  - {token['symbol']}: {token['tokenAddress'][:10]}...") 