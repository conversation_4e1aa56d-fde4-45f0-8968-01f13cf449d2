# 📊 未处理信号表格点击功能

## 🎯 功能概述

新增了在未处理信号表格中点击条目查看K线图表的功能，让用户可以快速切换到信号对应的代币进行分析和交易。

## ✨ 功能特性

### 1. 点击交互
- **表格标题**: 更新为"🔔 最近30分钟未处理信号 (点击查看图表)"
- **鼠标悬停**: 表格行在鼠标悬停时会高亮显示
- **工具提示**: 显示"💡 点击任意行查看该代币的K线图表"

### 2. 自动切换功能
点击信号表格中的任意行后，系统会自动：

- 📊 **切换图表**: 加载选中代币的K线图表
- 🔄 **清理记录**: 清空当前所有交易信号记录
- 🎯 **启用按钮**: 自动启用相关的交易按钮
- 💰 **查询余额**: 自动刷新代币余额信息
- 📝 **记录日志**: 在消息日志中记录选择操作

### 3. 完整的状态管理

#### 按钮状态更新
- ✅ **买入按钮**: 选择代币后自动启用
- ⚙️ **策略执行按钮**: 如果已选择策略则启用
- 💸 **卖出按钮**: 根据代币余额状态更新

#### 数据同步
- 🔄 **交易记录**: 清空并重新显示当前代币记录
- 📈 **统计信息**: 更新交易统计数据
- 💰 **余额信息**: 自动查询并显示代币余额

## 🔧 技术实现

### 事件监听器
```python
# 在create_unprocessed_signals_panel中添加
self.unprocessed_signals_table.cellClicked.connect(self.on_unprocessed_signal_clicked)
```

### 点击处理方法
```python
@pyqtSlot(int, int)
def on_unprocessed_signal_clicked(self, row: int, column: int):
    """未处理信号表格点击事件"""
    # 1. 获取信号数据
    # 2. 构建代币信息对象
    # 3. 加载K线图表
    # 4. 更新按钮状态
    # 5. 刷新余额信息
```

### 数据处理流程

1. **获取信号数据**
   ```python
   all_unprocessed_signals = self.get_unprocessed_signals()
   recent_signals = [过滤最近1小时的信号]
   signal_data = recent_signals[row]
   ```

2. **构建代币对象**
   ```python
   token_info = {
       'symbol': token_symbol,
       'name': token_symbol,
       'price': price,
       'tokenAddress': token_address,
       'source': 'unprocessed_signal'
   }
   ```

3. **状态同步**
   ```python
   self.load_token_chart(token_info)    # 加载图表
   self.update_trade_records_display()  # 更新记录
   self.refresh_token_balance()         # 刷新余额
   ```

## 🎨 界面改进

### 视觉提示
- **悬停效果**: `QTableWidget::item:hover { background-color: #34495e; cursor: pointer; }`
- **选择高亮**: 使用橙色选择背景 `selection-background-color: #e67e22`
- **工具提示**: 明确提示用户可以点击查看图表

### 用户体验
- 🎯 **直观操作**: 点击即可切换，无需额外步骤
- 📝 **状态反馈**: 在日志中记录选择操作
- ⚡ **快速响应**: 自动更新所有相关状态

## 📋 使用步骤

1. **查看信号列表**
   - 在"未处理信号"面板中查看最近30分钟的信号

2. **选择感兴趣的信号**
   - 点击任意信号行

3. **自动切换分析**
   - 系统自动加载该代币的K线图表
   - 显示当前价格和技术指标

4. **进行交易操作**
   - 买入按钮自动启用
   - 可以执行策略分析
   - 查看持仓余额

## 🔄 数据流转

```
未处理信号表格 → 点击事件 → 获取信号数据 → 构建代币信息 → 加载图表 → 更新按钮状态 → 刷新余额
     ↓                ↓              ↓              ↓            ↓           ↓
   显示信号         处理点击       提取代币地址     切换图表     启用交易     查询钱包
```

## ✅ 功能优势

### 1. 提高效率
- 🚀 **一键切换**: 从信号直接跳转到图表分析
- ⏱️ **节省时间**: 无需手动搜索和输入代币地址
- 🎯 **精准定位**: 直接定位到产生信号的代币

### 2. 增强体验
- 🔄 **无缝集成**: 与现有功能完美融合
- 📊 **数据一致**: 保持所有状态同步
- 💡 **直观操作**: 符合用户使用习惯

### 3. 安全可靠
- 🛡️ **异常处理**: 完善的错误捕获和日志记录
- ✅ **数据验证**: 验证行索引和数据完整性
- 📝 **操作记录**: 所有操作都有日志追踪

## 🚀 扩展可能

未来可以考虑的增强功能：

1. **右键菜单**: 添加更多操作选项
2. **信号标记**: 标记已查看或已处理的信号
3. **批量操作**: 支持多选和批量处理
4. **自定义列**: 允许用户自定义显示列
5. **过滤功能**: 按策略、信号类型等过滤

这个功能让信号监控和图表分析之间的切换变得更加流畅和高效！🎉 