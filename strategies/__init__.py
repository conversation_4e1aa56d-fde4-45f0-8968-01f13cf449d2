"""
策略模块的包初始化文件。

这个文件使得 'strategies' 目录可以被当作一个包来对待。
它也通常是放置包级别导出（如工厂类）的好地方。
"""

import logging
from typing import List, Optional

# 从各个策略模块导入具体的策略类
from .base_strategy import BaseStrategy # 虽然工厂本身不直接用，但导入它不会造成伤害，且有时有用
from .macd_strategy import MACDStrategy
from .rsi_strategy import RSIStrategy
from .sar_strategy import SARStrategy
from .combined_strategy import CombinedStrategy
from .vwap_cross_strategy import VWAPCrossStrategy
from .vwap_5m_strategy import VWAP5mStrategy
from .sar_5m_strategy import SAR5mStrategy
from .sar_15m_strategy import SAR15mStrategy

logger = logging.getLogger(__name__)

class StrategyFactory:
    """策略工厂，用于创建和管理交易策略"""
    
    _strategies_list: Optional[List[BaseStrategy]] = None

    @staticmethod
    def get_all_strategies() -> List[BaseStrategy]:
        """
        获取所有可用的交易策略的实例。
        
        返回:
            List[BaseStrategy]: 策略实例列表
        """
        if StrategyFactory._strategies_list is None:
            StrategyFactory._strategies_list = [
                MACDStrategy(),
                RSIStrategy(),
                SARStrategy(),
                CombinedStrategy(),
                VWAPCrossStrategy(),
                VWAP5mStrategy(),
                SAR5mStrategy(),
                SAR15mStrategy()
            ]
            logger.info(f"StrategyFactory: Initialized with {len(StrategyFactory._strategies_list)} strategies.")
        return StrategyFactory._strategies_list
    
    @staticmethod
    def get_strategy_by_name(name: str) -> Optional[BaseStrategy]:
        """
        根据名称获取策略实例。
        
        参数:
            name (str): 策略名称
            
        返回:
            Optional[BaseStrategy]: 找到的策略实例，如果未找到则返回None
        """
        strategies = StrategyFactory.get_all_strategies()
        for strategy in strategies:
            if strategy.name == name:
                return strategy
        logger.warning(f"StrategyFactory: Strategy with name '{name}' not found.")
        return None

# 可以选择性地在这里暴露一些常用的类，以便外部可以直接 from strategies import X
__all__ = [
    'BaseStrategy', 
    'MACDStrategy', 
    'RSIStrategy', 
    'SARStrategy', 
    'CombinedStrategy', 
    'VWAPCrossStrategy',
    'VWAP5mStrategy',
    'SAR5mStrategy',
    'SAR15mStrategy',
    'StrategyFactory'
] 