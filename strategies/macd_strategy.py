import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class MACDStrategy(BaseStrategy):
    """基于MACD指标的交易策略"""
    
    def __init__(self):
        """初始化MACD策略"""
        super().__init__(
            name="MACD交叉策略",
            description="当MACD线上穿信号线时买入，下穿时卖出"
        )
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成MACD交易信号
        
        参数:
            df (pd.DataFrame): 带有MACD指标的价格数据
            
        返回:
            pd.DataFrame: 添加了交易信号的DataFrame
        """
        if df.empty:
            return df
            
        required_columns = ['macd', 'macd_signal']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"数据缺少MACD指标列: {required_columns}")
            df['signal'] = 0
            return df
        
        df['signal'] = 0
        
        df['macd_cross_up'] = (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1))
        df['macd_cross_down'] = (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1))
        
        df.loc[df['macd_cross_up'], 'signal'] = 1
        df.loc[df['macd_cross_down'], 'signal'] = -1
        
        return df 