import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class SAR15mStrategy(BaseStrategy):
    """基于15分钟抛物线SAR指标的交易策略"""
    
    def __init__(self):
        """初始化15分钟SAR策略"""
        super().__init__(
            name="15分钟SAR策略",
            description="当15分钟K线的价格上穿SAR时买入，下穿SAR时卖出"
        )
    
    def get_primary_timeframe(self) -> str:
        """返回此策略主要操作的K线时间周期。"""
        return "15m"

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成15分钟SAR交易信号
        预期 df 已经是15分钟周期的K线数据，并且包含了 'sar' 和 'close' 列
        """
        if df.empty:
            logger.warning(f"{self.name}: DataFrame is empty, cannot generate signals.")
            return df
            
        required_columns = ['sar', 'close']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"{self.name}: 数据缺少SAR指标列或收盘价列: {required_columns}, 现有列: {df.columns.tolist()}")
            df['signal'] = 0
            return df
        
        # 初始化信号列
        df['signal'] = 0
        
        # SAR 默认参数 (如果需要从 indicators.py 或其他地方获取，可以在这里调整)
        # df = TechnicalIndicators.add_sar(df) # 假设SAR列已经由外部计算并传入

        # 核心逻辑：价格上穿SAR买入，下穿SAR卖出
        # 价格在SAR上方
        df['price_above_sar'] = df['close'] > df['sar']
        
        # 上穿信号：当前价格在SAR上方，且前一周期价格在SAR下方或持平
        # 使用 .fillna(False) 处理第一个点可能产生的NaN
        df['sar_cross_up'] = df['price_above_sar'] & ~df['price_above_sar'].shift(1).fillna(False)
        
        # 下穿信号：当前价格在SAR下方，且前一周期价格在SAR上方或持平
        df['sar_cross_down'] = ~df['price_above_sar'] & df['price_above_sar'].shift(1).fillna(False)
        
        df.loc[df['sar_cross_up'], 'signal'] = 1
        df.loc[df['sar_cross_down'], 'signal'] = -1
        
        # 清理辅助列 (可选)
        # df.drop(columns=['price_above_sar', 'sar_cross_up', 'sar_cross_down'], inplace=True)
        
        logger.info(f"{self.name}: Signal generation complete. Buy signals: {(df['signal'] == 1).sum()}, Sell signals: {(df['signal'] == -1).sum()}")
        return df 