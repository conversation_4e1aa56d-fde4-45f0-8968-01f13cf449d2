import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class SARStrategy(BaseStrategy):
    """基于抛物线SAR指标的交易策略"""
    
    def __init__(self):
        """初始化SAR策略"""
        super().__init__(
            name="抛物线SAR策略",
            description="当价格上穿SAR时买入，下穿SAR时卖出"
        )
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成SAR交易信号
        """
        if df.empty:
            return df
            
        required_columns = ['sar', 'close']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"数据缺少SAR指标列或收盘价列: {required_columns}")
            df['signal'] = 0
            return df
        
        df['signal'] = 0
        
        df['price_above_sar'] = df['close'] > df['sar']
        df['sar_cross_up'] = df['price_above_sar'] & ~df['price_above_sar'].shift(1).fillna(False)
        df['sar_cross_down'] = ~df['price_above_sar'] & df['price_above_sar'].shift(1).fillna(False)
        
        df.loc[df['sar_cross_up'], 'signal'] = 1
        df.loc[df['sar_cross_down'], 'signal'] = -1
        
        return df 