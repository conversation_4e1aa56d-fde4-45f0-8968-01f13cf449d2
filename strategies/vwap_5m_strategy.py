import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class VWAP5mStrategy(BaseStrategy):
    """基于5分钟VWAP指标的交易策略"""
    
    def __init__(self):
        """初始化5分钟VWAP策略"""
        super().__init__(
            name="5分钟VWAP策略",
            description="专为meme币设计的5分钟VWAP交叉策略：价格上穿VWAP买入，目标100倍收益或跌破VWAP时卖出"
        )
    
    def get_primary_timeframe(self) -> str:
        """返回此策略主要操作的K线时间周期"""
        return "5m"

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成5分钟VWAP交易信号
        预期 df 已经是5分钟周期的K线数据，并且包含了 'vwap' 和 'close' 列
        """
        if df.empty:
            logger.warning(f"{self.name}: DataFrame为空，无法生成交易信号。")
            return df
            
        required_columns = ['close', 'vwap']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"{self.name}: 数据缺少必要的列：{required_columns}，现有列：{df.columns.tolist()}")
            df['signal'] = 0
            return df

        # 初始化信号列和状态变量
        signals = pd.Series(index=df.index, data=0)
        in_position = False
        entry_price = 0.0
        
        logger.info(f"{self.name}: 开始为{len(df)}根5分钟K线生成交易信号。")

        for i in range(1, len(df)):
            current_close = df['close'].iloc[i]
            current_vwap = df['vwap'].iloc[i]
            previous_close = df['close'].iloc[i-1]
            previous_vwap = df['vwap'].iloc[i-1]

            logger.debug(f"{self.name} 循环 i={i}: 前一收盘价={previous_close:.8f}, 前一VWAP={previous_vwap:.8f}, "
                        f"当前收盘价={current_close:.8f}, 当前VWAP={current_vwap:.8f}, 持仓状态={in_position}")

            # 跳过VWAP为NaN的情况
            if pd.isna(current_vwap) or pd.isna(previous_vwap):
                logger.debug(f"{self.name} 循环 i={i}: 跳过，因为VWAP为NaN (当前: {current_vwap}, 前一: {previous_vwap})")
                continue 

            if not in_position:
                # 买入条件：前一周期价格在VWAP下方或等于，当前周期价格突破VWAP上方
                buy_condition = (previous_close <= previous_vwap) and (current_close > current_vwap)
                logger.debug(f"{self.name} 循环 i={i} (无持仓): 买入条件 (前一收盘价 <= 前一VWAP 且 当前收盘价 > 当前VWAP) = {buy_condition}")
                
                if buy_condition:
                    signals.iloc[i] = 1
                    in_position = True
                    entry_price = current_close
                    logger.info(f"{self.name} 买入信号: 索引={df.index[i]}, 时间={df.get('datetime', pd.Series([''] * len(df))).iloc[i]}, "
                              f"价格={entry_price:.8f}, VWAP={current_vwap:.8f}")
            else:
                # 在持仓状态下，检查卖出条件
                sell_signal = False
                sell_reason = ""
                
                # 盈利目标：100倍的盈利（适合meme币的暴涨特性）
                profit_target_multiplier = 10000.0  # 100000%盈利目标
                target_profit_price = entry_price * profit_target_multiplier
                profit_target_condition = (current_close >= target_profit_price)
                
                logger.debug(f"{self.name} 循环 i={i} (持仓中): 当前价格={current_close:.8f}, 入场价格={entry_price:.8f}, "
                           f"盈利目标价格={target_profit_price:.8f}, 达到盈利目标={profit_target_condition}")
                
                if profit_target_condition:
                    sell_signal = True
                    sell_reason = f"达到盈利目标 {target_profit_price:.8f} (入场价格: {entry_price:.8f})"
                
                # VWAP下穿条件：当前价格跌破VWAP
                vwap_cross_down_condition = (current_close < current_vwap)
                logger.debug(f"{self.name} 循环 i={i} (持仓中): 当前价格={current_close:.8f}, 当前VWAP={current_vwap:.8f}, "
                           f"VWAP下穿条件={vwap_cross_down_condition}")
                
                if not sell_signal and vwap_cross_down_condition:
                    sell_signal = True
                    sell_reason = f"价格跌破VWAP (VWAP: {current_vwap:.8f})"

                if sell_signal:
                    signals.iloc[i] = -1
                    in_position = False
                    profit_loss = ((current_close - entry_price) / entry_price) * 100
                    logger.info(f"{self.name} 卖出信号: 索引={df.index[i]}, 时间={df.get('datetime', pd.Series([''] * len(df))).iloc[i]}, "
                              f"价格={current_close:.8f}, 原因: {sell_reason}, 盈亏: {profit_loss:.2f}%")
                    entry_price = 0.0
        
        df['signal'] = signals
        buy_count = (df['signal'] == 1).sum()
        sell_count = (df['signal'] == -1).sum()
        logger.info(f"{self.name}: 信号生成完成。买入信号: {buy_count}, 卖出信号: {sell_count}")
        
        return df 