import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class CombinedStrategy(BaseStrategy):
    """结合多个指标的综合交易策略"""
    
    def __init__(self):
        """初始化综合策略"""
        super().__init__(
            name="多指标综合策略",
            description="结合MACD、RSI和SAR指标的综合策略"
        )
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成综合交易信号
        """
        if df.empty:
            return df
            
        required_columns = ['macd', 'macd_signal', 'rsi', 'sar', 'close']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"数据缺少必要的指标列: {required_columns}")
            df['signal'] = 0
            return df
        
        df['signal'] = 0
        df['signal_score'] = 0
        
        df['macd_cross_up'] = (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1))
        df['macd_cross_down'] = (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1))
        
        df['rsi_oversold_exit'] = (df['rsi'] > 30) & (df['rsi'].shift(1) <= 30)
        df['rsi_overbought_exit'] = (df['rsi'] < 70) & (df['rsi'].shift(1) >= 70)
        
        df['price_above_sar'] = df['close'] > df['sar']
        df['sar_cross_up'] = df['price_above_sar'] & ~df['price_above_sar'].shift(1).fillna(False)
        df['sar_cross_down'] = ~df['price_above_sar'] & df['price_above_sar'].shift(1).fillna(False)
        
        df.loc[df['macd_cross_up'], 'signal_score'] += 1
        df.loc[df['macd_cross_down'], 'signal_score'] -= 1
        df.loc[df['rsi_oversold_exit'], 'signal_score'] += 1
        df.loc[df['rsi_overbought_exit'], 'signal_score'] -= 1
        df.loc[df['sar_cross_up'], 'signal_score'] += 1
        df.loc[df['sar_cross_down'], 'signal_score'] -= 1
        
        df.loc[df['signal_score'] >= 2, 'signal'] = 1
        df.loc[df['signal_score'] <= -2, 'signal'] = -1
        
        return df 