"""
交易策略基类模块
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from abc import ABC, abstractmethod
import logging
from config import STRATEGY_CONFIG

logger = logging.getLogger(__name__) # 每个模块使用自己的logger实例是个好习惯

class BaseStrategy(ABC):
    """交易策略基类，所有策略都应继承此类"""
    
    def __init__(self, name: str, description: str):
        """
        初始化策略
        
        参数:
            name (str): 策略名称
            description (str): 策略描述
        """
        self.name = name
        self.description = description
        self.positions = []  # 持仓记录
        self.trades = []  # 交易记录
        
    def get_primary_timeframe(self) -> str:
        """返回此策略主要操作的K线时间周期。默认为1分钟。"""
        return "1m"

    def get_auxiliary_timeframes_and_indicators(self) -> Dict[str, List[str]]:
        """返回此策略需要的辅助时间周期及其必需的指标列名。
        格式: {'5m': ['sar', 'low'], '1h': ['ema_20']}
        默认为空，表示不需要辅助数据。
        """
        return {}
        
    @abstractmethod
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        参数:
            df (pd.DataFrame): 带有技术指标的价格数据
            
        返回:
            pd.DataFrame: 添加了交易信号的DataFrame
        """
        pass
        
    def backtest(self, df: pd.DataFrame, initial_capital: float = 1000.0) -> Dict[str, Any]:
        """
        执行回测
        
        参数:
            df (pd.DataFrame): 带有交易信号的价格数据
            initial_capital (float): 初始资金
            
        返回:
            Dict[str, Any]: 回测结果，包括收益、交易次数、胜率等
        """
        if df.empty:
            logger.error("回测数据为空")
            return self._empty_result(initial_capital)
            
        required_columns = ['close', 'signal']
        if not all(col in df.columns for col in required_columns):
            logger.error(f"回测数据缺少必要的列: {required_columns}")
            return self._empty_result(initial_capital)
            
        capital = initial_capital
        position_units = 0.0 
        entry_price = 0.0
        trades = []
        equity_curve = []
        
        # 使用配置文件中的设置
        trade_amount = STRATEGY_CONFIG.get("trade_amount", 500.0)
        max_buy_times = STRATEGY_CONFIG.get("max_buy_times_per_coin", 5)
        buy_count = 0  # 跟踪买入次数

        for i, row in df.iterrows():
            current_price = row['close']
            current_signal = row['signal']

            logger.debug(f"BaseStrategy.backtest loop i={i}, Time={row.get('datetime', str(i))}, current_price={current_price:.15f}, current_signal={current_signal}, position_units={position_units}, capital={capital:.2f}")

            current_position_value = position_units * current_price
            equity_curve.append({
                'timestamp': row.get('timestamp', i),
                'datetime': row.get('datetime', str(i)),
                'equity': capital + current_position_value
            })
            
            if position_units == 0 and current_signal == 1:
                logger.debug(f"BaseStrategy.backtest: Attempting BUY at i={i}. position_units == 0: {position_units == 0}, current_signal == 1: {current_signal == 1}")
                
                # 检查买入次数限制
                if buy_count >= max_buy_times:
                    logger.info(f"BaseStrategy.backtest: BUY BLOCKED at i={i} - Max buy times limit reached ({buy_count}/{max_buy_times})")
                elif capital >= trade_amount:
                    logger.debug(f"BaseStrategy.backtest: Capital check PASSED for BUY at i={i}. Capital ({capital:.2f}) >= TradeAmount ({trade_amount:.2f})")
                    units_to_buy = trade_amount / current_price
                    position_units = units_to_buy
                    entry_price = current_price 
                    capital -= trade_amount 
                    buy_count += 1  # 增加买入次数计数
                    
                    trades.append({
                        'type': 'buy',
                        'timestamp': row.get('timestamp', i),
                        'datetime': row.get('datetime', str(i)),
                        'price': current_price,
                        'units': units_to_buy,
                        'cost': trade_amount, 
                        'capital_after_trade': capital,
                        'buy_sequence': buy_count  # 记录这是第几次买入
                    })
                    logger.info(f"BaseStrategy.backtest: BUY EXECUTED at i={i}, Time={row.get('datetime', str(i))}, Price={current_price:.15f}, Units={units_to_buy:.4f}, Buy Count: {buy_count}/{max_buy_times}")
                else:
                    logger.warning(f"BaseStrategy.backtest: Capital check FAILED for BUY at i={i}. Capital ({capital:.2f}) < TradeAmount ({trade_amount:.2f})")

            elif position_units > 0 and current_signal == -1: 
                logger.debug(f"BaseStrategy.backtest: Attempting SELL at i={i}. position_units > 0: {position_units > 0}, current_signal == -1: {current_signal == -1}")
                proceeds = position_units * current_price
                profit = proceeds - (position_units * entry_price) 
                capital += proceeds
                trades.append({
                    'type': 'sell',
                    'timestamp': row.get('timestamp', i),
                    'datetime': row.get('datetime', str(i)),
                    'price': current_price,
                    'units': position_units,
                    'proceeds': proceeds,
                    'profit_this_trade': profit,
                    'capital_after_trade': capital
                })
                logger.debug(f"卖出: {position_units:.4f} units at {current_price}, Proceeds: {proceeds}, Time: {row.get('datetime', i)}")
                position_units = 0.0
                entry_price = 0.0
        
        if position_units > 0:
            last_price = df['close'].iloc[-1]
            logger.info(f"BaseStrategy.backtest: Position held at end of data ({position_units:.4f} units). Liquidating at last price: {last_price:.15f}")
            proceeds = position_units * last_price
            profit = proceeds - (position_units * entry_price) 
            capital += proceeds
            trades.append({
                'type': 'sell_at_end',
                'timestamp': df.get('timestamp', df.index[-1]).iloc[-1],
                'datetime': df.get('datetime', str(df.index[-1])).iloc[-1],
                'price': last_price,
                'units': position_units,
                'proceeds': proceeds,
                'profit_this_trade': profit,
                'capital_after_trade': capital
            })
            logger.debug(f"回测结束平仓: {position_units:.4f} units at {last_price}, Proceeds: {proceeds}")
            position_units = 0.0
        
        self.trades = trades
        result = self._calculate_metrics(trades, initial_capital, capital, equity_curve)
        
        # 添加买入次数信息到结果中
        result['buy_count'] = buy_count
        result['max_buy_times'] = max_buy_times
        result['buy_times_used_percentage'] = (buy_count / max_buy_times * 100) if max_buy_times > 0 else 0
        result['ohlcv_df'] = df.copy() 
        
        return result
    
    def _calculate_metrics(
        self, 
        trades: List[Dict], 
        initial_capital: float, 
        final_capital: float,
        equity_curve: List[Dict]
    ) -> Dict[str, Any]:
        buy_trades = [t for t in trades if t['type'] == 'buy']
        sell_trades = [t for t in trades if t['type'] == 'sell' or t['type'] == 'sell_at_end']
        
        trade_count = len(sell_trades)
        buy_count = len(buy_trades)
        max_buy_times = STRATEGY_CONFIG.get("max_buy_times_per_coin", 5)
        
        if trade_count == 0:
            total_profit = final_capital - initial_capital
            profit_percentage = (total_profit / initial_capital) * 100 if initial_capital else 0
            return {
                'strategy_name': self.name,
                'initial_capital': initial_capital,
                'final_capital': final_capital, 
                'total_profit': total_profit,  
                'profit_percentage': profit_percentage, 
                'trade_count': 0, 
                'win_rate': 0.0,
                'avg_profit': 0.0,
                'avg_loss': 0.0,
                'profit_loss_ratio': 0.0,
                'max_drawdown': 0.0, 
                'max_drawdown_percentage': 0.0,
                'sharpe_ratio': 0.0,
                'buy_count': buy_count,
                'max_buy_times': max_buy_times,
                'buy_times_used_percentage': (buy_count / max_buy_times * 100) if max_buy_times > 0 else 0,
                'trades': trades, 
                'equity_curve': equity_curve,
            }
        
        profitable_trades = [t for t in sell_trades if t.get('profit_this_trade', 0) > 0]
        losing_trades = [t for t in sell_trades if t.get('profit_this_trade', 0) <= 0]
        
        win_rate = len(profitable_trades) / trade_count if trade_count > 0 else 0
        
        avg_profit = sum(t.get('profit_this_trade', 0) for t in profitable_trades) / len(profitable_trades) if profitable_trades else 0
        avg_loss = sum(t.get('profit_this_trade', 0) for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else float('inf')
        
        total_profit = final_capital - initial_capital
        profit_percentage = (total_profit / initial_capital) * 100
        
        equity_values = [e['equity'] for e in equity_curve]
        if not equity_values: # 处理 equity_values 为空的情况
             max_drawdown, max_drawdown_percentage = 0.0, 0.0
        else:
            max_drawdown, max_drawdown_percentage = self._calculate_max_drawdown(equity_values)

        if len(equity_values) > 1:
            returns = [(equity_values[i] - equity_values[i-1]) / equity_values[i-1] for i in range(1, len(equity_values)) if equity_values[i-1] != 0] # 避免除以0
            if returns and np.std(returns) > 0: # 确保 returns 不为空且标准差不为0
                 sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) 
            else:
                sharpe_ratio = 0.0
        else:
            sharpe_ratio = 0.0
        
        return {
            'strategy_name': self.name,
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'trade_count': trade_count,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_loss_ratio': profit_loss_ratio,
            'max_drawdown': max_drawdown,
            'max_drawdown_percentage': max_drawdown_percentage,
            'sharpe_ratio': sharpe_ratio,
            'buy_count': buy_count,
            'max_buy_times': max_buy_times,
            'buy_times_used_percentage': (buy_count / max_buy_times * 100) if max_buy_times > 0 else 0,
            'trades': trades,
            'equity_curve': equity_curve
        }
    
    def _calculate_max_drawdown(self, equity_values: List[float]) -> Tuple[float, float]:
        if not equity_values: # 处理空列表的情况
            return 0.0, 0.0
            
        max_drawdown = 0.0
        max_drawdown_percentage = 0.0
        peak = equity_values[0]
        
        for value in equity_values:
            if value > peak:
                peak = value
            
            drawdown = peak - value
            # 确保 peak 不为0，以避免除以0的错误
            drawdown_percentage = (drawdown / peak) * 100 if peak != 0 else 0.0
            
            if drawdown > max_drawdown:
                max_drawdown = drawdown
                max_drawdown_percentage = drawdown_percentage
        
        return max_drawdown, max_drawdown_percentage
    
    def _empty_result(self, initial_capital: float) -> Dict[str, Any]:
        max_buy_times = STRATEGY_CONFIG.get("max_buy_times_per_coin", 5)
        return {
            'strategy_name': self.name,
            'initial_capital': initial_capital,
            'final_capital': initial_capital,
            'total_profit': 0.0,
            'profit_percentage': 0.0,
            'trade_count': 0,
            'win_rate': 0.0,
            'avg_profit': 0.0,
            'avg_loss': 0.0,
            'profit_loss_ratio': 0.0,
            'max_drawdown': 0.0,
            'max_drawdown_percentage': 0.0,
            'sharpe_ratio': 0.0,
            'buy_count': 0,
            'max_buy_times': max_buy_times,
            'buy_times_used_percentage': 0.0,
            'trades': [],
            'equity_curve': []
        } 