import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class RSIStrategy(BaseStrategy):
    """基于RSI指标的交易策略"""
    
    def __init__(self, overbought: int = 70, oversold: int = 30):
        """初始化RSI策略"""
        super().__init__(
            name="RSI超买超卖策略",
            description=f"当RSI低于{oversold}时买入，高于{overbought}时卖出"
        )
        self.overbought = overbought
        self.oversold = oversold
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成RSI交易信号
        """
        if df.empty:
            return df
            
        if 'rsi' not in df.columns:
            logger.error("数据缺少RSI指标列")
            df['signal'] = 0
            return df
        
        df['signal'] = 0
        
        df['rsi_oversold_exit'] = (df['rsi'] > self.oversold) & (df['rsi'].shift(1) <= self.oversold)
        df['rsi_overbought_exit'] = (df['rsi'] < self.overbought) & (df['rsi'].shift(1) >= self.overbought)
        
        df.loc[df['rsi_oversold_exit'], 'signal'] = 1
        df.loc[df['rsi_overbought_exit'], 'signal'] = -1
        
        return df 