import pandas as pd
import logging
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class VWAPCrossStrategy(BaseStrategy):
    """VWAP交叉策略"""
    def __init__(self):
        super().__init__(
            name="VWAP 交叉策略",
            description="当K线从下向上穿过VWAP时买入。涨幅1000倍或跌破VWAP时卖出。"
        )

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成VWAP交叉策略的交易信号"""
        if 'close' not in df.columns or 'vwap' not in df.columns:
            logger.error("VWAPCrossStrategy: DataFrame缺少 'close' 或 'vwap' 列。")
            df['signal'] = 0
            return df

        signals = pd.Series(index=df.index, data=0)
        in_position = False
        entry_price = 0.0
        # logger.info(f"VWAPCrossStrategy: Starting signal generation for {len(df)} candles.")
        
        # 添加数据统计信息
        close_min, close_max = df['close'].min(), df['close'].max()
        vwap_min, vwap_max = df['vwap'].min(), df['vwap'].max()
        # logger.info(f"VWAPCrossStrategy: Price range: {close_min:.8f} - {close_max:.8f}")
        # logger.info(f"VWAPCrossStrategy: VWAP range: {vwap_min:.8f} - {vwap_max:.8f}")
        
        # 检查价格与VWAP的关系
        price_above_vwap = (df['close'] > df['vwap']).sum()
        price_below_vwap = (df['close'] <= df['vwap']).sum()
        # logger.info(f"VWAPCrossStrategy: Price above VWAP: {price_above_vwap} candles, below/equal: {price_below_vwap} candles")

        # 检查初始条件：如果第一个有效点价格高于VWAP，则买入
        # 这个操作应该在主循环之前，并且只在未持仓时执行一次
        if not in_position and len(df) > 0:
            first_valid_index = -1
            # 找到第一个 VWAP 和 close 都是有效值的位置
            # 通常VWAP的计算可能导致前面几根K线没有值，所以需要找到第一个有效点
            for idx_init in range(len(df)): # 使用不同的变量名以避免与主循环冲突
                if pd.notna(df['vwap'].iloc[idx_init]) and pd.notna(df['close'].iloc[idx_init]):
                    first_valid_index = idx_init
                    break
            
            if first_valid_index != -1:
                initial_close = df['close'].iloc[first_valid_index]
                initial_vwap = df['vwap'].iloc[first_valid_index]
                
                # 检查条件：初始收盘价高于初始VWAP
                if initial_close > initial_vwap:
                    signals.iloc[first_valid_index] = 1
                    in_position = True
                    entry_price = initial_close
                    # 确保时间戳的正确记录
                    time_val = df.index[first_valid_index]
                    time_str = time_val.strftime('%Y-%m-%d %H:%M:%S') if hasattr(time_val, 'strftime') else str(time_val)
                    # logger.info(f"VWAPCross INITIAL BUY SIGNAL (Price > VWAP at start): Index={first_valid_index}, Time={time_str}, Price={entry_price:.15f}")

        for i in range(1, len(df)):
            current_close = df['close'].iloc[i]
            current_vwap = df['vwap'].iloc[i]
            previous_close = df['close'].iloc[i-1]
            previous_vwap = df['vwap'].iloc[i-1]

            # 只在前10个和后10个K线打印详细调试信息
            # if i <= 10 or i >= len(df) - 10:
            #     logger.debug(f"VWAPCross loop i={i}: prev_c={previous_close:.15f}, prev_v={previous_vwap:.15f}, curr_c={current_close:.15f}, curr_v={current_vwap:.15f}, in_pos={in_position}")

            if pd.isna(current_vwap) or pd.isna(previous_vwap):
                if i <= 10 or i >= len(df) - 10:
                    logger.debug(f"VWAPCross loop i={i}: Skipping due to NaN in VWAP (curr: {current_vwap}, prev: {previous_vwap})")
                continue 

            if not in_position:
                buy_condition = (previous_close <= previous_vwap) and (current_close > current_vwap)
                if i <= 10 or i >= len(df) - 10:
                    logger.debug(f"VWAPCross loop i={i} (NOT IN POSITION): Buy condition (prev_c <= prev_v AND curr_c > curr_v) = {buy_condition}")
                if buy_condition:
                    signals.iloc[i] = 1
                    in_position = True
                    entry_price = current_close
                    time_str = df.index[i] if hasattr(df.index[i], 'strftime') else str(df.index[i])
                    # logger.info(f"VWAPCross BUY SIGNAL: Index={i}, Time={time_str}, Price={entry_price:.15f}")
            else: # in_position is True
                sell_signal = False
                sell_reason = ""
                
                target_profit_price = entry_price * 11000.0 
                tp_condition = (current_close >= target_profit_price)
                # logger.debug(f"VWAPCross loop i={i} (IN POSITION): curr_c={current_close:.15f}, entry_p={entry_price:.15f}, target_tp_p={target_profit_price:.15f}, TP_cond={tp_condition}")
                if tp_condition:
                    sell_signal = True
                    sell_reason = f"Take Profit at {target_profit_price:.15f}"
                
                vwap_cross_down_condition = (current_close < current_vwap)
                # logger.debug(f"VWAPCross loop i={i} (IN POSITION): curr_c={current_close:.15f}, curr_v={current_vwap:.15f}, VWAP_cross_down_cond={vwap_cross_down_condition}")
                if not sell_signal and vwap_cross_down_condition:
                    sell_signal = True
                    sell_reason = f"Cross Below VWAP (VWAP: {current_vwap:.15f})"

                if sell_signal:
                    signals.iloc[i] = -1
                    in_position = False
                    time_str = df.index[i] if hasattr(df.index[i], 'strftime') else str(df.index[i])
                    # logger.info(f"VWAPCross SELL SIGNAL: Index={i}, Time={time_str}, Price={current_close:.15f}, Reason: {sell_reason}, Entry Price was: {entry_price:.15f}")
                    entry_price = 0.0
        
        df['signal'] = signals
        # logger.info(f"VWAPCrossStrategy: Signal generation finished. Total buy signals: {(df['signal'] == 1).sum()}, Total sell signals: {(df['signal'] == -1).sum()}")
        return df 