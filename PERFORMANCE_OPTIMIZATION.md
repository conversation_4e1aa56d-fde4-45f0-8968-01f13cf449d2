# 🚀 性能优化总结

## 📊 优化内容

### 1. **后台监控优化**
- **减少定时器频率**：从 60 秒改为 300 秒（5分钟）
- **批处理请求**：每批只处理 3 个代币，代币间延迟 1 秒，批次间延迟 2 秒
- **统一缓存管理**：避免重复 API 请求，缓存有效期 3 分钟

### 2. **指标计算优化**
- **核心指标计算**：性能模式下只计算 MACD、RSI、VWAP 等核心指标
- **跳过详细日志**：减少不必要的日志输出
- **最小化信号发射**：减少 PyQt 信号槽开销

### 3. **新增组件**

#### **BackgroundMonitorManager**
- 统一管理所有后台图表的 API 请求
- 智能缓存，避免重复请求相同数据
- 批处理队列，控制并发请求数量

#### **PerformanceMonitor**
- 实时监控 CPU 和内存使用率
- 统计 API 请求和缓存命中率
- 提供性能分析和优化建议

## 🔧 配置参数

### `config.py` 新增配置：

```python
BACKGROUND_MONITORING_CONFIG = {
    "enabled": True,
    "refresh_interval": 300000,  # 5分钟刷新一次
    "batch_processing": {
        "enabled": True,
        "batch_size": 3,           # 每批3个代币
        "batch_delay": 2000,       # 批次间延迟2秒
        "item_delay": 1000,        # 代币间延迟1秒
    },
    "data_caching": {
        "enabled": True,
        "cache_duration": 180000,  # 缓存3分钟
        "share_cache": True,
    },
    "performance_mode": {
        "enabled": True,
        "skip_detailed_logs": True,      # 跳过详细日志
        "minimal_signals": True,         # 最小化信号
        "reduce_indicator_calc": True,   # 减少指标计算
    }
}
```

## 📈 预期效果

### **CPU 使用率预期降低**：
- **定时器优化**：减少 80% 的定时器触发频率
- **API 请求优化**：减少 60-70% 的重复 API 请求  
- **计算优化**：减少 40-50% 的指标计算开销
- **日志优化**：减少 90% 的日志输出开销

### **内存使用优化**：
- 统一缓存管理，避免重复存储
- 定期清理过期缓存
- 减少不必要的对象创建

## 🛠️ 使用方法

### 1. **查看性能监控**
在 LiveTradingWidget 中会显示实时性能数据：
- CPU 使用率（当前/平均/峰值）
- 内存使用情况
- API 请求统计
- 缓存命中率

### 2. **调整性能参数**
可以在 `config.py` 中调整优化参数：

```python
# 进一步降低 CPU 使用率
BACKGROUND_MONITORING_CONFIG = {
    "refresh_interval": 600000,  # 改为10分钟
    "batch_processing": {
        "batch_size": 2,         # 改为每批2个
        "batch_delay": 3000,     # 改为3秒延迟
    }
}
```

### 3. **禁用性能模式**
如果需要完整功能，可以禁用性能模式：

```python
BACKGROUND_MONITORING_CONFIG = {
    "performance_mode": {
        "enabled": False,  # 禁用性能模式
    }
}
```

## 🔍 监控和调试

### **查看缓存统计**：
```python
# 在 LiveTradingWidget 中
cache_stats = self.background_monitor_manager.get_cache_stats()
print(f"缓存大小: {cache_stats['cache_size']}")
print(f"待处理请求: {cache_stats['request_queue_size']}")
```

### **查看性能摘要**：
```python
perf_summary = self.performance_monitor.get_performance_summary()
print(f"平均 CPU: {perf_summary['cpu_avg']:.1f}%")
print(f"缓存命中率: {perf_summary['cache_hits']}/{perf_summary['api_requests']}")
```

## 🎯 优化建议

### **进一步优化**：
1. **减少监控代币数量**：只监控价值较高的代币
2. **调整刷新频率**：根据实际需要增加刷新间隔
3. **使用更轻量级的策略**：选择计算开销较小的策略
4. **关闭不必要的功能**：暂时关闭不需要的监控功能

### **监控指标**：
- **CPU 使用率** < 30%（目标）
- **内存增长率** < 5MB/小时
- **缓存命中率** > 60%
- **API 请求频率** < 10 次/分钟

## 🚨 注意事项

1. **首次启动时** CPU 可能较高，因为需要初始化缓存
2. **网络问题时** 可能会有短暂的 CPU 峰值
3. **策略变更时** 会触发重新计算，CPU 会临时上升
4. **性能模式下** 某些高级指标可能不可用

## 📝 更新日志

**v1.1 性能优化版本**：
- ✅ 实现统一后台监控管理器
- ✅ 增加智能缓存系统
- ✅ 优化批处理队列
- ✅ 集成性能监控器
- ✅ 减少不必要的日志输出
- ✅ 优化指标计算逻辑 