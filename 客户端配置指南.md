# TrendTrader 客户端配置指南

## 🚨 重要：API密钥配置

如果您在使用TrendTrader时遇到以下错误：
- `401 Client Error: Unauthorized`
- `数据获取失败`
- `API来源: Birdeye API`

这表示需要配置API密钥。

## 📋 解决方案

### 方法1：创建.env文件（推荐）

1. **在应用程序目录中创建.env文件**
   - 找到TrendTrader.app所在的目录
   - 创建一个名为`.env`的文件（注意前面有个点）

2. **在.env文件中添加以下内容：**
   ```
   BIRDEYE_API_KEY=your_api_key_here
   ```

3. **获取API密钥：**
   - 访问 [Birdeye API官网](https://docs.birdeye.so/)
   - 注册账户并获取免费API密钥
   - 将密钥替换`your_api_key_here`

### 方法2：使用环境变量

**macOS/Linux:**
```bash
export BIRDEYE_API_KEY="your_api_key_here"
./TrendTrader.app
```

**Windows:**
```cmd
set BIRDEYE_API_KEY=your_api_key_here
TrendTrader.exe
```

## 🔧 详细步骤

### 1. 获取Birdeye API密钥

1. 访问 https://docs.birdeye.so/
2. 点击"Get API Key"
3. 注册账户（免费）
4. 复制您的API密钥

### 2. 配置密钥

**macOS用户：**
1. 右键点击TrendTrader.app，选择"显示包内容"
2. 进入Contents文件夹
3. 创建.env文件，内容如下：
   ```
   BIRDEYE_API_KEY=your_actual_api_key
   ```

**Windows用户：**
1. 在TrendTrader.exe同目录下创建.env文件
2. 添加API密钥配置

### 3. 重启应用程序

配置完成后重新启动TrendTrader，错误应该消失。

## 📝 注意事项

- API密钥是免费的，但有使用限制
- 请妥善保管您的API密钥
- 如果仍有问题，请检查网络连接

## 🆘 故障排除

### 问题：仍然显示401错误
**解决方案：**
1. 确认.env文件格式正确（无多余空格）
2. 确认API密钥有效
3. 重启应用程序

### 问题：找不到.env文件位置
**解决方案：**
1. 确保.env文件与可执行文件在同一目录
2. 文件名必须是`.env`（注意前面的点）

### 问题：API密钥无效
**解决方案：**
1. 重新获取API密钥
2. 确认复制时没有多余字符

## 📞 技术支持

如果按照以上步骤仍无法解决问题，请联系技术支持并提供：
1. 操作系统版本
2. 错误截图
3. .env文件内容（隐藏API密钥） 