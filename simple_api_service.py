#!/usr/bin/env python3
"""
简化的API服务 - 用于多进程环境，不依赖PyQt5
"""

import requests
import json
import time
import logging
from typing import List, Dict, Optional
from datetime import datetime

# 配置
BIRDEYE_API_KEY = "a0c45b7306f54ab4a5639b49a454bc18"  # 🔥 使用测试成功的API key
OHLCV_API_URL = "https://public-api.birdeye.so/defi/history_price"
TREND_API_URL = "https://public-api.birdeye.so/defi/tokenlist"

logger = logging.getLogger(__name__)

class SimpleAPIService:
    """简化的API服务，不依赖PyQt5，用于多进程环境"""
    
    def __init__(self):
        """初始化简化API服务"""
        pass
    
    @staticmethod
    def get_ohlcv_data(
        token_address: str,
        timeframe: str = '1m',
        days: int = 1,
        source: str = 'historical'
    ) -> List[Dict]:
        """
        获取OHLCV数据，支持历史数据源和Birdeye
        
        参数:
            token_address: 代币地址
            timeframe: 时间周期
            days: 天数
            source: 数据源 ('historical' 或 'trend')
        
        返回:
            OHLCV数据列表
        """
        try:
            if source == 'historical' or source == 'historical_api':
                return SimpleAPIService._get_historical_ohlcv(token_address, timeframe)
            else:
                return SimpleAPIService._get_birdeye_ohlcv(token_address, timeframe, days)
        except Exception as e:
            logger.error(f"SimpleAPIService: 获取OHLCV数据失败 ({source}): {e}")
            return []
    
    @staticmethod
    def _get_historical_ohlcv(token_address: str, timeframe: str) -> List[Dict]:
        """从历史数据API获取OHLCV数据"""
        try:
            url = f"https://ohlcv-sync.vercel.app/api/ohlcv?address={token_address}&type={timeframe}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'application/json'
            }
            
            response = requests.get(url, headers=headers, timeout=20)
            response.raise_for_status()
            data = response.json()
            
            formatted_data = []
            if data.get('success') and isinstance(data.get('data'), list):
                items = data['data']
                logger.info(f"SimpleAPIService: 历史API返回 {len(items)} 条数据 for {token_address}")
                
                for item in items:
                    if all(k in item for k in ['o', 'h', 'l', 'c', 'v', 'unixTime']):
                        formatted_data.append({
                            'timestamp': item['unixTime'],
                            'datetime': datetime.fromtimestamp(item['unixTime']).strftime('%Y-%m-%d %H:%M:%S'),
                            'open': float(item['o']),
                            'high': float(item['h']),
                            'low': float(item['l']),
                            'close': float(item['c']),
                            'volume': float(item['v'])
                        })
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"SimpleAPIService: 历史API获取失败 {token_address}: {e}")
            return []
    
    @staticmethod
    def _get_birdeye_ohlcv(token_address: str, timeframe: str, days: int) -> List[Dict]:
        """从Birdeye API获取OHLCV数据"""
        try:
            if not BIRDEYE_API_KEY or BIRDEYE_API_KEY == "YOUR_BIRDEYE_API_KEY":
                logger.error("SimpleAPIService: BIRDEYE_API_KEY未设置")
                return []
            
            end_time = int(time.time())
            start_time = end_time - (days * 24 * 60 * 60)
            
            headers = {
                'X-API-KEY': BIRDEYE_API_KEY,
                'accept': 'application/json',
                'x-chain': 'solana'
            }
            params = {
                'address': token_address,
                'type': timeframe,
                'currency': 'usd',  # 🔥 添加currency参数
                'time_from': start_time,
                'time_to': end_time
            }
            
            response = requests.get(OHLCV_API_URL, headers=headers, params=params, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            formatted_data = []
            if isinstance(data, dict) and 'data' in data and 'items' in data['data']:
                for item in data['data']['items']:
                    if all(k in item for k in ['o', 'h', 'l', 'c', 'v', 'unixTime']):
                        formatted_data.append({
                            'timestamp': item['unixTime'],
                            'datetime': datetime.fromtimestamp(item['unixTime']).strftime('%Y-%m-%d %H:%M:%S'),
                            'open': float(item['o']),
                            'high': float(item['h']),
                            'low': float(item['l']),
                            'close': float(item['c']),
                            'volume': float(item['v'])
                        })
            
            return formatted_data
            
        except Exception as e:
            logger.error(f"SimpleAPIService: Birdeye API获取失败 {token_address}: {e}")
            return [] 