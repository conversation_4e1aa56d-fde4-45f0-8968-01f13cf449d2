# api_service.py
"""
API服务模块 - 负责与外部API交互获取数据
"""

import requests
import time
import json
import logging
import threading  # 添加threading导入
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import os # 新增
from dotenv import load_dotenv # 新增
from concurrent.futures import ThreadPoolExecutor # 导入线程池

# 导入 PyQt 相关的类
from PyQt5.QtCore import QObject, QThread, pyqtSignal, pyqtSlot, QMetaObject, Qt, Q_ARG

# 配置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # 🔥 改回INFO级别以显示详细信息

# 如果还没有处理器，添加控制台处理器
if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)  # 🔥 改回INFO级别
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    logger.info('API服务日志系统已初始化')

# 加载 .env 文件中的环境变量
load_dotenv() 

# 从环境变量读取配置
BIRDEYE_API_KEY = os.getenv("BIRDEYE_API_KEY")
TREND_API_URL = os.getenv("TREND_API_URL", "https://token-news-roan.vercel.app/api/tokens/aggregated-data")
OHLCV_API_URL = os.getenv("OHLCV_API_URL", "https://public-api.birdeye.so/defi/ohlcv")
DATABASE_FILE_PATH = os.getenv("DATABASE_FILE_PATH", "app_data.db")

# 🔥 新增：Birdeye API timeframe格式转换映射
def convert_timeframe_for_birdeye(timeframe: str) -> str:
    """将内部timeframe格式转换为Birdeye API要求的格式"""
    conversion_map = {
        '1m': '1m',    # 分钟级别保持小写
        '3m': '3m',
        '5m': '5m',
        '15m': '15m',
        '30m': '30m',
        '1h': '1H',    # 小时级别转换为大写
        '2h': '2H',
        '4h': '4H',
        '6h': '6H',
        '8h': '8H',
        '12h': '12H',
        '1d': '1D',    # 天级别转换为大写
        '3d': '3D',
        '1w': '1W',    # 周级别转换为大写
        '1M': '1M',    # 月级别保持大写
    }
    
    converted = conversion_map.get(timeframe, timeframe)
    if converted != timeframe:
        logger.info(f"转换timeframe格式: {timeframe} -> {converted} (for Birdeye API)")
    return converted

# 导入缓存配置
try:
    from config import HISTORICAL_TOKENS_CACHE_CONFIG
except ImportError:
    logger.warning("无法导入HISTORICAL_TOKENS_CACHE_CONFIG，使用默认配置")
    HISTORICAL_TOKENS_CACHE_CONFIG = {
        "cache_enabled": True,
        "cache_max_age_seconds": 3600,
        "force_cache_on_network_error": True,
    }

# 检查 API Key 是否成功加载，如果没有则使用默认值
if not BIRDEYE_API_KEY:
    logger.warning("BIRDEYE_API_KEY 未在环境变量中设置，使用默认API Key")
    BIRDEYE_API_KEY = "a0c45b7306f54ab4a5639b49a454bc18"  # 🔥 使用你测试成功的API key
    logger.info("已设置默认BIRDEYE_API_KEY")
elif BIRDEYE_API_KEY == "YOUR_BIRDEYE_API_KEY":
    logger.warning("警告: BIRDEYE_API_KEY 设置为占位符，使用默认API Key")
    BIRDEYE_API_KEY = "a0c45b7306f54ab4a5639b49a454bc18"  # 🔥 使用你测试成功的API key
else:
    logger.info("BIRDEYE_API_KEY 已成功加载")

# 导入数据库服务
from database_service import DatabaseService


# 定义一个 Worker 类，它将在单独的线程中执行耗时操作
class ApiWorker(QObject):
    # 定义信号，用于将结果发送回主线程
    trending_data_ready = pyqtSignal(list)
    trending_error = pyqtSignal(str)
    ohlcv_data_ready = pyqtSignal(str, str, list)
    ohlcv_error = pyqtSignal(str, str, str)
    historical_tokens_ready = pyqtSignal(list)
    historical_tokens_error = pyqtSignal(str)

    def __init__(self, db_service: DatabaseService, parent=None):
        super().__init__(parent)
        self.db_service = db_service # 存储 DatabaseService 实例
        self.CACHE_SOURCE_HISTORICAL_RAW_JSON = "historical_1d_1m_raw_json" # Cache identifier
        # 🔥 初始化线程池，用于并发处理网络请求
        self.thread_pool = ThreadPoolExecutor(max_workers=10, thread_name_prefix='ApiWorkerPool')
        logger.info(f"ApiWorker: ThreadPoolExecutor initialized with max_workers=10")

    @pyqtSlot()
    def fetch_trending_tokens(self):
        """在工作线程中获取趋势榜单数据，优先从缓存加载。"""
        cached_tokens_loaded = False
        try:
            # # 1. 尝试从数据库加载缓存的趋势代币
            # if self.db_service:
            #     logger.info("Worker: 正在从数据库加载缓存的趋势代币...")
            #     cached_tokens = self.db_service.get_all_tokens_metadata(source='trend')
            #     if cached_tokens:
            #         logger.info(f"Worker: 从缓存加载了 {len(cached_tokens)} 个趋势代币。")
            #         self.trending_data_ready.emit(cached_tokens) # 先发出缓存数据
            #         cached_tokens_loaded = True
            #     else:
            #         logger.info("Worker: 缓存中没有趋势代币数据。")
            # else:
            #     logger.warning("Worker: DatabaseService 未初始化，无法加载缓存。")
            
            # 2. 总是尝试从API获取最新数据
            logger.info("Worker: 正在从API获取最新的趋势榜单数据...")
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                'Accept': 'application/json'
            }
            
            response = requests.get(TREND_API_URL, headers=headers, timeout=15)
            response.raise_for_status()
            api_data = response.json()

            api_tokens = []
            if isinstance(api_data, dict) and api_data.get('status') == 'success' and 'data' in api_data:
                api_tokens = api_data['data']
            elif isinstance(api_data, list):
                api_tokens = api_data # 有些API直接返回列表
            else:
                error_msg = "趋势API返回格式错误"
                logger.error(f"Worker: {error_msg}: {api_data}")
                if not cached_tokens_loaded: # 只有当缓存也未加载时才发出错误信号
                    self.trending_error.emit(error_msg)
                return
            
            logger.info(f"Worker: API成功获取到 {len(api_tokens)} 个趋势代币")
            
            # 3. 保存从API获取的数据到数据库
            if self.db_service and api_tokens:
                logger.info(f"Worker: 正在将 {len(api_tokens)} 个API代币保存到数据库...")
                for token_data in api_tokens:
                    if 'tokenAddress' in token_data and 'address' not in token_data:
                        token_data['address'] = token_data['tokenAddress']
                    self.db_service.save_token_metadata(token_data, source='trend')
            
            # 4. 发出从API获取的最新数据 (即使它与缓存数据相同，UI也可能需要刷新时间戳等)
            self.trending_data_ready.emit(api_tokens)

        except requests.exceptions.Timeout as e_timeout:
            logger.error(f"Worker: API获取趋势榜单超时: {e_timeout}")
            if not cached_tokens_loaded:
                self.trending_error.emit("获取趋势榜单超时 (API)。请检查网络或稍后重试。")
            # 如果缓存已加载，用户至少有旧数据看，可以不发错误，或者发一个更温和的提示
        except requests.exceptions.RequestException as e_req:
            logger.error(f"Worker: API获取趋势榜单失败: {str(e_req)}")
            if not cached_tokens_loaded:
                error_msg = f"获取趋势榜单失败 (API): {str(e_req)}"
                if hasattr(e_req, 'response') and e_req.response is not None:
                    error_msg += f" (Status: {e_req.response.status_code})"
                self.trending_error.emit(error_msg)
        except json.JSONDecodeError as e_json:
            logger.error(f"Worker: API解析趋势榜单JSON失败: {str(e_json)}")
            if not cached_tokens_loaded:
                self.trending_error.emit(f"解析API趋势榜单数据失败: {str(e_json)}")
        except Exception as e_generic:
            logger.error(f"Worker: 获取趋势榜单时发生未知错误: {str(e_generic)}", exc_info=True)
            if not cached_tokens_loaded:
                self.trending_error.emit(f"获取趋势榜单时发生未知错误: {str(e_generic)}")

    # 🔥 新增：实际执行HTTP请求并处理数据的内部方法
    def _perform_ohlcv_http_request(self, token_address: str, timeframe: str, days: int, source: str) -> tuple[Optional[list], Optional[str]]:
        """执行单个OHLCV HTTP请求并处理数据，在线程池中运行。返回 (formatted_data, error_message)元组。"""
        logger.debug(f"WorkerPool [{threading.get_ident()}]: Starting _perform_ohlcv_http_request for {token_address} ({timeframe}) from {source}")
        
        items_to_format = [] # 🔥 初始化 items_to_format

        # 缓存检查 (如果适用，例如历史数据)
        if (source == 'historical_api' or source == 'historical') and self.db_service:
            cache_key_source = f"{self.CACHE_SOURCE_HISTORICAL_RAW_JSON}_{timeframe}"
            cached_data = self.db_service.get_ohlcv_from_cache(token_address, timeframe, cache_key_source)
            if cached_data:
                logger.info(f"WorkerPool [{threading.get_ident()}]: CACHE HIT (in _perform_ohlcv_http_request) for {token_address} ({timeframe}).")
                return cached_data, None
            logger.info(f"WorkerPool [{threading.get_ident()}]: CACHE MISS (in _perform_ohlcv_http_request) for {token_address} ({timeframe}).")

        formatted_data = []
        api_url_to_use = ""
        headers = {}
        params = {}
        is_ohlcv_sync_api = False
        max_points_per_request = 1000

        if source == 'historical_api' or source == 'historical':
            is_ohlcv_sync_api = True
            api_url_to_use = f"https://ohlcv-sync.vercel.app/api/ohlcv?address={token_address}&type={timeframe}"
            headers = {'User-Agent': 'Mozilla/5.0...', 'Accept': 'application/json'}
            logger.debug(f"WorkerPool [{threading.get_ident()}]: Using ohlcv-sync API: {api_url_to_use}")
            try:
                response = requests.get(api_url_to_use, headers=headers, timeout=20)
                response.raise_for_status()
                data = response.json()
                if data.get('success') and isinstance(data.get('data'), list):
                    items_to_format = data['data']
                else:
                    return None, "ohlcv-sync API返回格式错误或未成功。"
            except Exception as e:
                logger.error(f"WorkerPool [{threading.get_ident()}]: ohlcv-sync API请求失败 for {token_address}: {e}")
                return None, f"ohlcv-sync API请求失败: {e}"

        elif source == 'trend':
            if not BIRDEYE_API_KEY or BIRDEYE_API_KEY == "YOUR_BIRDEYE_API_KEY":
                return None, "API Key未设置，无法获取趋势代币K线数据。"
            api_url_to_use = OHLCV_API_URL
            end_time = int(time.time())
            start_time = end_time - (days * 24 * 60 * 60)
            
            # 🔥 转换timeframe格式为Birdeye API要求的格式
            birdeye_timeframe = convert_timeframe_for_birdeye(timeframe)
            
            timeframe_to_minutes = {'1m': 1, '5m': 5, '15m': 15, '30m': 30, '1h': 60, '4h': 240, '1d': 1440}
            minutes_per_point = timeframe_to_minutes.get(timeframe, 1)
            total_minutes = days * 24 * 60
            required_points = total_minutes // minutes_per_point

            if required_points > max_points_per_request:
                current_end_time = end_time
                all_items = []
                logger.info(f"WorkerPool [{threading.get_ident()}]: Birdeye API需要分批获取 {required_points} 点 for {token_address}")
                while len(all_items) < required_points and current_end_time > start_time:
                    points_needed = min(max_points_per_request, required_points - len(all_items))
                    time_range_minutes = points_needed * minutes_per_point
                    current_start_time = max(start_time, current_end_time - time_range_minutes * 60)
                    _headers = {'X-API-KEY': BIRDEYE_API_KEY, 'accept': 'application/json', 'x-chain': 'solana'}
                    _params = {'address': token_address, 'type': birdeye_timeframe, 'currency': 'usd', 'time_from': current_start_time, 'time_to': current_end_time}
                    try:
                        response = requests.get(api_url_to_use, headers=_headers, params=_params, timeout=20)
                        response.raise_for_status()
                        data = response.json()
                        if isinstance(data, dict) and 'data' in data and 'items' in data['data']:
                            batch_items = data['data']['items']
                            existing_timestamps = {item.get('unixTime') for item in all_items}
                            new_items = [item for item in batch_items if item.get('unixTime') not in existing_timestamps]
                            all_items.extend(new_items)
                            if len(batch_items) < points_needed: break
                        else: 
                            logger.warning(f"WorkerPool [{threading.get_ident()}]: Birdeye分批数据格式错误 for {token_address}")
                            break 
                    except Exception as e:
                        logger.error(f"WorkerPool [{threading.get_ident()}]: Birdeye分批获取失败 for {token_address}: {e}")
                        return None, f"Birdeye分批获取失败: {e}"
                    if all_items:
                        # 🔥 Bugfix: Ensure new_items is not empty before calling min to avoid ValueError
                        if new_items:
                           earliest_timestamp = min(item.get('unixTime', current_end_time) for item in new_items) # Use new_items to get earliest from current batch
                           current_end_time = earliest_timestamp - 1
                        else: # No new unique items were added in this batch, break to avoid infinite loop on duplicate data
                           logger.warning(f"WorkerPool [{threading.get_ident()}]: No new unique items in batch for {token_address}, breaking batch.")
                           break
                    else: # Should not happen if new_items was populated, but as a safeguard
                        current_end_time = current_start_time - 1
                all_items.sort(key=lambda x: x.get('unixTime', 0))
                items_to_format = all_items
            else:                 # Birdeye 单次请求
                headers = {'X-API-KEY': BIRDEYE_API_KEY, 'accept': 'application/json', 'x-chain': 'solana'}
                params = {'address': token_address, 'type': birdeye_timeframe, 'currency': 'usd', 'time_from': start_time, 'time_to': end_time}
                logger.debug(f"WorkerPool [{threading.get_ident()}]: Using Birdeye API single request for {token_address}")
                try:
                    response = requests.get(api_url_to_use, headers=headers, params=params, timeout=20)
                    response.raise_for_status()
                    data = response.json()
                    if isinstance(data, dict) and 'data' in data and 'items' in data['data']:
                        items_to_format = data['data']['items']
                    else:
                        logger.warning(f"WorkerPool [{threading.get_ident()}]: Birdeye API单次请求格式错误 for {token_address}: {data}")
                        return None, "Birdeye API 返回格式错误 (单次请求)"
                except Exception as e: # 🔥 Linter fix: Added except for the try block
                    logger.error(f"WorkerPool [{threading.get_ident()}]: Birdeye API单次请求失败 for {token_address}: {e}")
                    return None, f"Birdeye API单次请求失败: {e}"
        else:
            logger.error(f"WorkerPool [{threading.get_ident()}]: Unknown source type '{source}' for {token_address}")
            return None, f"未知的K线数据源: {source}"

        # 通用的数据格式化和缓存逻辑
        if not items_to_format:
            # This case should ideally be handled within each API's block if it means no data was found.
            # If it's reached here, it implies an issue or an API returned an empty list successfully.
            logger.warning(f"WorkerPool [{threading.get_ident()}]: No items to format for {token_address} ({timeframe}) from {source}. API might have returned empty list.")
            # For ohlcv-sync, an empty list from API is treated as an error if it was expected to have data.
            # For Birdeye, an empty list is a valid response if there's no data for the period.
            if is_ohlcv_sync_api: # ohlcv-sync (historical) specific empty check
                 return None, f"{source} API未返回任何K线数据 (0条记录)。"
            # For Birdeye, an empty 'items_to_format' is fine, will result in empty 'formatted_data'

        for item in items_to_format:
            # Birdeye uses 'o', 'h', 'l', 'c', 'v'. ohlcv-sync also uses these.
            key_o, key_h, key_l, key_c, key_v = 'o', 'h', 'l', 'c', 'v'
            if all(k in item for k in [key_o, key_h, key_l, key_c, key_v, 'unixTime']):
                formatted_data.append({
                    'timestamp': item['unixTime'],
                    'datetime': datetime.fromtimestamp(item['unixTime']).strftime('%Y-%m-%d %H:%M:%S'),
                    'open': float(item[key_o]), 'high': float(item[key_h]),
                    'low': float(item[key_l]), 'close': float(item[key_c]),
                    'volume': float(item[key_v])
                })
            else:
                logger.warning(f"WorkerPool [{threading.get_ident()}]: Skipping item with missing keys for {token_address}: {item}")
        
        # 缓存历史数据 (如果适用)
        if (source == 'historical_api' or source == 'historical') and self.db_service and formatted_data:
            cache_key_source = f"{self.CACHE_SOURCE_HISTORICAL_RAW_JSON}_{timeframe}"
            self.db_service.save_ohlcv_to_cache(token_address, timeframe, formatted_data, cache_key_source)
            logger.info(f"WorkerPool [{threading.get_ident()}]: Saved {len(formatted_data)} historical records to cache for {token_address} ({timeframe})")

        logger.debug(f"WorkerPool [{threading.get_ident()}]: Successfully processed {len(formatted_data)} OHLCV records for {token_address} ({timeframe})")
        return formatted_data, None
    
    # 🔥 新增：用于从线程池安全发射信号的槽
    @pyqtSlot(str, str, list, str)
    def _emit_ohlcv_signal_from_thread(self, token_address: str, timeframe: str, data: Optional[list], error_message: Optional[str]):
        """在ApiWorker的主线程中安全地发射OHLCV信号。"""
        if error_message:
            # logger.warning(f"ApiWorker Emit [{threading.get_ident()}]: Emitting ohlcv_error for {token_address} ({timeframe}): {error_message}")
            self.ohlcv_error.emit(token_address, timeframe, error_message)
        elif data is not None: # 确保data不是None，即使是空列表也算成功
            # logger.info(f"ApiWorker Emit [{threading.get_ident()}]: Emitting ohlcv_data_ready for {token_address} ({timeframe}), {len(data)} records.")
            self.ohlcv_data_ready.emit(token_address, timeframe, data)
        else: # 两者都为None的意外情况
            #  logger.error(f"ApiWorker Emit [{threading.get_ident()}]: Both data and error are None for {token_address} ({timeframe}). This should not happen.")
             self.ohlcv_error.emit(token_address, timeframe, "Internal error: No data or error message from worker thread.")


    @pyqtSlot(str, str, int, str)
    def fetch_ohlcv_data(self, token_address: str, timeframe: str, days: int, source: str):
        """使用线程池并发获取OHLCV数据。"""
        logger.info(f"ApiWorker [{threading.get_ident()}]: Queueing fetch_ohlcv_data for {token_address} ({timeframe}), source: {source} to ThreadPool.")

        future = self.thread_pool.submit(self._perform_ohlcv_http_request, token_address, timeframe, days, source)

        def _callback(f):
            try:
                data, error_msg = f.result()
                # 使用QMetaObject.invokeMethod确保在ApiWorker的主线程发射信号
                QMetaObject.invokeMethod(
                    self, "_emit_ohlcv_signal_from_thread", Qt.QueuedConnection,
                    Q_ARG(str, token_address),
                    Q_ARG(str, timeframe),
                    Q_ARG(list, data if data is not None else []), # 确保传递列表或空列表
                    Q_ARG(str, error_msg if error_msg is not None else "") # 确保传递字符串或空字符串
                )
            except Exception as e:
                logger.error(f"ApiWorker [{threading.get_ident()}]: Error in ThreadPool future callback for {token_address}: {e}", exc_info=True)
                QMetaObject.invokeMethod(
                    self, "_emit_ohlcv_signal_from_thread", Qt.QueuedConnection,
                    Q_ARG(str, token_address),
                    Q_ARG(str, timeframe),
                    Q_ARG(list, []), # 传递空列表表示错误
                    Q_ARG(str, f"Internal error in future callback: {e}")
                )
        
        future.add_done_callback(_callback)


    @pyqtSlot()
    def fetch_historical_tokens(self):
        """在工作线程中获取历史代币数据。"""
        try:
            logger.info("Worker: 开始获取历史代币数据...")
            
            # 1. 尝试从数据库缓存加载
            if self.db_service:
                cached_tokens = self.db_service.get_all_tokens_metadata(source='historical')
                if cached_tokens:
                    logger.info(f"Worker: 从缓存加载了 {len(cached_tokens)} 个历史代币。")
                    self.historical_tokens_ready.emit(cached_tokens)
                    # 如果缓存命中，可以考虑是否仍然需要从API获取，或者仅在特定条件下获取
                    # return # 暂时：如果缓存命中，则不从API获取
            
            # 2. 从API获取 (如果需要)
            # 注意：这个API可能需要特定的参数或认证
            # api_url = "https://your-historical-tokens-api.com/tokens" 
            # response = requests.get(api_url, timeout=15)
            # response.raise_for_status()
            # api_data = response.json()
            
            # 模拟API返回数据
            time.sleep(1) # 模拟网络延迟
            simulated_api_tokens = [
                {'tokenAddress': 'hist_token_1', 'symbol': 'HTK1', 'name': 'Historical Token 1', 'network': 'SOLANA', 'source': 'historical_api'},
                {'tokenAddress': 'hist_token_2', 'symbol': 'HTK2', 'name': 'Historical Token 2', 'network': 'SOLANA', 'source': 'historical_api'},
            ]
            api_tokens = simulated_api_tokens # 使用模拟数据
            
            logger.info(f"Worker: (模拟)API成功获取到 {len(api_tokens)} 个历史代币")
            
            # 3. 保存到数据库
            if self.db_service and api_tokens:
                for token_data in api_tokens:
                    if 'tokenAddress' in token_data and 'address' not in token_data:
                        token_data['address'] = token_data['tokenAddress']
                    self.db_service.save_token_metadata(token_data, source='historical')
            
            self.historical_tokens_ready.emit(api_tokens)

        except requests.RequestException as e_req:
            logger.error(f"Worker: API获取历史代币失败: {str(e_req)}")
            self.historical_tokens_error.emit(f"获取历史代币失败 (API): {str(e_req)}")
        except Exception as e_generic: # 🔥 Linter fix: Corrected indentation
            logger.error(f"Worker: 获取历史代币时发生未知错误: {str(e_generic)}", exc_info=True)
            self.historical_tokens_error.emit(f"获取历史代币时发生未知错误: {str(e_generic)}")

# APIService 类将负责管理 ApiWorker 和 QThread
class APIService(QObject):
    # 定义 APIService 对外暴露的信号
    trending_tokens_ready = pyqtSignal(list)
    trending_tokens_error = pyqtSignal(str)
    ohlcv_data_ready = pyqtSignal(str, str, list)
    ohlcv_data_error = pyqtSignal(str)
    historical_tokens_ready = pyqtSignal(list)
    historical_tokens_error = pyqtSignal(str)

    _db_service_instance = None # 类变量用于存储数据库服务实例

    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化 DatabaseService 单例（或者传递已创建的实例）
        if APIService._db_service_instance is None:
            APIService._db_service_instance = DatabaseService(db_name=DATABASE_FILE_PATH)
        self.db_service = APIService._db_service_instance

        self._worker = ApiWorker(db_service=self.db_service) # 将db_service传递给Worker
        self._worker_thread = QThread()
        
        # 将 Worker 移动到新的线程
        self._worker.moveToThread(self._worker_thread)

        # 连接 Worker 的信号到 APIService 自己的信号 (用于对外暴露)
        self._worker.trending_data_ready.connect(self.trending_tokens_ready)
        self._worker.trending_error.connect(self.trending_tokens_error)
        self._worker.ohlcv_data_ready.connect(self.on_worker_ohlcv_data_ready)  # 🔥 通过缓存处理方法
        self._worker.ohlcv_error.connect(self.on_worker_ohlcv_error)  # 🔥 修复：使用中间处理方法
        self._worker.historical_tokens_ready.connect(self.historical_tokens_ready)
        self._worker.historical_tokens_error.connect(self.historical_tokens_error)

        # 当线程启动时，可以执行一些初始化操作 (可选)
        # self._worker_thread.started.connect(self._worker.init_worker) # 假设Worker有一个init方法
        
        # 线程退出时清理 worker (可选，但推荐)
        self._worker_thread.finished.connect(self._worker.deleteLater)
        self._worker_thread.finished.connect(self._worker_thread.deleteLater)

        # 🔥 添加内存缓存 - 用于临时存储最近获取的 OHLCV 数据
        self.memory_cache = {}  # {token_address: {'data': ohlcv_data, 'timestamp': time, 'timeframe': str}}
        self.memory_cache_timeout = 60  # 缓存1分钟（60秒）
        
        # 🔥🔥 性能监控引用
        self.performance_monitor = None

        # 启动线程
        self._worker_thread.start()
        logger.info("APIService: 工作线程已启动。")

    def __del__(self):
        """确保在对象销毁时停止线程"""
        if self._worker_thread.isRunning():
            self._worker_thread.quit()
            self._worker_thread.wait() # 等待线程终止
            logger.info("APIService: 工作线程已停止。")

    @pyqtSlot()
    def get_trending_tokens_async(self):
        """
        异步获取趋势榜单数据。
        通过 QMetaObject.invokeMethod 将调用调度到工作线程。
        """
        logger.info("APIService: 调度异步获取趋势榜单任务到工作线程...")
        QMetaObject.invokeMethod(
            self._worker,
            'fetch_trending_tokens',
            Qt.QueuedConnection # 确保在工作线程的事件循环中执行
        )

    @pyqtSlot(str, str, int, str)
    def get_ohlcv_data_async(self, token_address: str, timeframe: str, days: int, source: str):
        """
        异步获取代币的OHLCV数据，根据source选择API。
        通过 QMetaObject.invokeMethod 将调用调度到工作线程。
        """
        logger.info(f"APIService: 调度异步获取 {token_address} K线数据任务 (source: {source}) 到工作线程...")
        
        # 🔥🔥 记录API请求到性能监控器
        if self.performance_monitor:
            self.performance_monitor.record_api_request()
        
        QMetaObject.invokeMethod(
            self._worker,
            'fetch_ohlcv_data',
            Qt.QueuedConnection, 
            Q_ARG(str, token_address),
            Q_ARG(str, timeframe),
            Q_ARG(int, days),
            Q_ARG(str, source) # 传递 source
        )
            
    @pyqtSlot()
    def get_historical_tokens_async(self):
        """
        异步获取历史代币列表。
        通过 QMetaObject.invokeMethod 将调用调度到工作线程。
        """
        logger.info("APIService: 调度异步获取历史代币列表任务到工作线程...")
        QMetaObject.invokeMethod(
            self._worker,
            'fetch_historical_tokens',
            Qt.QueuedConnection # 确保在工作线程的事件循环中执行
        )

    # 同步方法保持不变，但通常不应在主UI线程中直接调用
    @staticmethod
    def get_trending_tokens() -> List[Dict]:
        """同步获取趋势榜单数据 (不推荐在UI线程直接调用)"""
        # 这里的实现可以保持原来的同步逻辑，或者直接调用Worker的同步版本
        # 为了简洁，我们假设 Worker 有一个同步的 fetch 方法，或者直接复制粘贴之前的逻辑
        logger.warning("APIService: 同步获取趋势榜单数据被调用。建议使用异步方法。")
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                'Accept': 'application/json'
            }
            response = requests.get(TREND_API_URL, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            if isinstance(data, dict) and data.get('status') == 'success' and 'data' in data:
                return data['data']
            elif isinstance(data, list):
                return data
            return []
        except Exception as e:
            logger.error(f"同步获取趋势榜单失败: {e}")
            return []
            
            
    @staticmethod
    def get_ohlcv_data(
        token_address: str,
        timeframe: str = '1m',
        days: int = 1,
        source: Optional[str] = 'trend' # 添加可选的source，默认为trend (Birdeye)
    ) -> List[Dict]:
        """同步获取OHLCV数据 (不推荐在UI线程直接调用)
           注意: 此同步版本目前主要适配Birdeye。若要支持ohlcv-sync,需进一步修改或调用者确保只用于Birdeye。
        """
        logger.warning(f"APIService: 同步获取OHLCV数据被调用 (source: {source}, address: {token_address}, timeframe: {timeframe}, days: {days}). 建议使用异步方法。")
        
        # --- 尝试从缓存加载 ---
        # APIService 本身也需要 db_service 实例来访问缓存
        # 这是静态方法，所以不能直接用 self.db_service。需要通过类变量或重新获取。
        # 为了保持静态方法的简单性，我们暂时不在这里实现同步的缓存读取。
        # 依赖于调用此方法的 SingleBacktestThread 自己处理缓存逻辑，或者此同步方法仅用于非历史数据源。
        # 如果APIService._db_service_instance 存在，理论上可以用它。

        if (source == 'historical_api' or source == 'historical'):
            # 尝试从缓存获取 (同步版本)
            db_service_instance = APIService._db_service_instance # Access class variable
            if db_service_instance:
                cache_key_source = f"historical_1d_1m_raw_json_{timeframe}" # Consistent cache key
                logger.info(f"APIService (Sync OHLCV): Checking cache for {token_address} ({timeframe}) with cache_source_key: {cache_key_source}")
                cached_data = db_service_instance.get_ohlcv_from_cache(token_address, timeframe, cache_key_source)
                if cached_data:
                    logger.info(f"APIService (Sync OHLCV): CACHE HIT for {token_address} ({timeframe}). Loaded {len(cached_data)} records.")
                    return cached_data
                else:
                    logger.info(f"APIService (Sync OHLCV): CACHE MISS for {token_address} ({timeframe}). Proceeding to API fetch.")
            
            logger.info(f"APIService (Sync): Using ohlcv-sync for {token_address}, days: {days} (days param might be ignored by this API)")
            try:
                url = f"https://ohlcv-sync.vercel.app/api/ohlcv?address={token_address}&type={timeframe}"
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                    'Accept': 'application/json'
                }
                response = requests.get(url, headers=headers, timeout=20)
                response.raise_for_status()
                data = response.json()
                formatted_data = []
                if data.get('success') and isinstance(data.get('data'), list):
                    items = data['data']
                    logger.info(f"APIService (Sync): ohlcv-sync API returned {len(items)} items in 'data' list for {token_address}.")
                    for item in items:
                        if all(k in item for k in ['o', 'h', 'l', 'c', 'v', 'unixTime']):
                            formatted_data.append({
                                'timestamp': item['unixTime'],
                                'datetime': datetime.fromtimestamp(item['unixTime']).strftime('%Y-%m-%d %H:%M:%S'),
                                'open': float(item['o']),
                                'high': float(item['h']),
                                'low': float(item['l']),
                                'close': float(item['c']),
                                'volume': float(item['v'])
                            })
                        else:
                            logger.warning(f"APIService (Sync): Skipping item due to missing keys: {item} for {token_address}")
                else:
                    logger.warning(f"APIService (Sync): ohlcv-sync API call for {token_address} did not return success or 'data' is not a list. Response: {data}")
                
                if not formatted_data:
                    logger.warning(f"APIService (Sync): formatted_data is empty for {token_address} after processing. Original API response: {data}")
                
                # 保存到缓存 (同步版本)
                if db_service_instance and formatted_data:
                    cache_key_source = f"historical_1d_1m_raw_json_{timeframe}"
                    logger.info(f"APIService (Sync): Saving {len(formatted_data)} OHLCV records to JSON cache for {token_address} ({cache_key_source}).")
                    db_service_instance.save_ohlcv_to_cache(token_address, timeframe, cache_key_source, formatted_data)

                return formatted_data
            except Exception as e:
                logger.error(f"APIService (Sync): ohlcv-sync API失败 for {token_address}: {e}")
                return []
        else:
            # --- Birdeye API 逻辑 ---
            logger.info(f"APIService (Sync): Using Birdeye API for {token_address}, timeframe: {timeframe}, days: {days}")
            if not BIRDEYE_API_KEY or BIRDEYE_API_KEY == "YOUR_BIRDEYE_API_KEY":
                logger.error("APIService (Sync): BIRDEYE_API_KEY未设置")
                return []
                
            # 🔥 转换timeframe格式为Birdeye API要求的格式
            birdeye_timeframe = convert_timeframe_for_birdeye(timeframe)
                
            try:
                from datetime import datetime, timedelta
                import time
                
                end_time = int(datetime.now().timestamp())
                start_time = int((datetime.now() - timedelta(days=days)).timestamp())
                
                api_url = "https://public-api.birdeye.so/defi/ohlcv"
                headers = {
                    'X-API-KEY': BIRDEYE_API_KEY,
                    'accept': 'application/json',
                    'x-chain': 'solana'
                }
                
                # 🔥 计算需要的数据点数量，判断是否需要分批获取
                if timeframe == '1m':
                    minutes_in_period = days * 24 * 60
                    required_points = minutes_in_period
                elif timeframe == '5m':
                    required_points = days * 24 * 12
                elif timeframe == '15m':
                    required_points = days * 24 * 4
                elif timeframe == '1h':
                    required_points = days * 24
                elif timeframe == '4h':
                    required_points = days * 6
                elif timeframe == '1d':
                    required_points = days
                else:
                    required_points = 1000  # 默认
                
                max_points_per_request = 1000
                
                logger.info(f"APIService (Sync): Birdeye API需要获取 {required_points} 个数据点 ({timeframe}, {days}天)")
                
                all_data = []
                
                if required_points > max_points_per_request:
                    # 分批获取
                    logger.info(f"APIService (Sync): 需要分 {(required_points + max_points_per_request - 1) // max_points_per_request} 批获取数据")
                    
                    current_end_time = end_time
                    current_start_time = start_time
                    
                    while len(all_data) < required_points and current_end_time > current_start_time:
                        points_needed = min(max_points_per_request, required_points - len(all_data))
                        
                        params = {
                            'address': token_address,
                            'type': birdeye_timeframe,
                            'currency': 'usd',  # 🔥 添加currency参数
                            'time_from': current_start_time,
                            'time_to': current_end_time
                        }
                        
                        logger.info(f"APIService (Sync): 分批获取 - 批次 {len(all_data)//max_points_per_request + 1}, 时间范围: {datetime.fromtimestamp(current_start_time)} 到 {datetime.fromtimestamp(current_end_time)}")
                        
                        response = requests.get(api_url, headers=headers, params=params, timeout=20)
                        response.raise_for_status()
                        data = response.json()
                        
                        if isinstance(data, dict) and 'data' in data and 'items' in data['data']:
                            batch_items = data['data']['items']
                            logger.info(f"APIService (Sync): 当前批次获取到 {len(batch_items)} 条数据")
                            
                            # 按时间戳去重并合并
                            existing_timestamps = {item.get('unixTime') for item in all_data}
                            new_items = [item for item in batch_items if item.get('unixTime') not in existing_timestamps]
                            all_data.extend(new_items)
                            
                            logger.info(f"APIService (Sync): 累计获取 {len(all_data)} 条唯一数据")
                            
                            if len(batch_items) < points_needed:
                                logger.info("APIService (Sync): 当前批次数据不足，已获取所有可用数据")
                                break
                        else:
                            logger.warning(f"APIService (Sync): 分批获取 - 批次数据格式错误: {data}")
                            break
                        
                        # 更新下一批次的结束时间
                        if all_data:
                            earliest_timestamp = min(item.get('unixTime', current_end_time) for item in all_data[-len(new_items):])
                            current_end_time = earliest_timestamp - 1
                        else:
                            current_end_time = current_start_time - 1
                        
                        # 避免请求过快
                        time.sleep(0.1)
                    
                    logger.info(f"APIService (Sync): 分批获取完成，总共获取 {len(all_data)} 条数据")
                    raw_items = all_data
                else:
                    # 单次请求
                    params = {
                        'address': token_address,
                        'type': birdeye_timeframe,
                        'currency': 'usd',  # 🔥 添加currency参数
                        'time_from': start_time,
                        'time_to': end_time
                    }
                    
                    response = requests.get(api_url, headers=headers, params=params, timeout=20)
                    response.raise_for_status()
                    data = response.json()
                    
                    if isinstance(data, dict) and 'data' in data and 'items' in data['data']:
                        raw_items = data['data']['items']
                        logger.info(f"APIService (Sync): 单次请求获取到 {len(raw_items)} 条数据")
                    else:
                        logger.warning(f"APIService (Sync): Birdeye API返回格式错误: {data}")
                        return []
                
                # 转换数据格式
                formatted_data = []
                for item in raw_items:
                    if all(k in item for k in ['o', 'h', 'l', 'c', 'v', 'unixTime']):
                        formatted_data.append({
                            'timestamp': item['unixTime'],
                            'datetime': datetime.fromtimestamp(item['unixTime']).strftime('%Y-%m-%d %H:%M:%S'),
                            'open': float(item['o']),
                            'high': float(item['h']),
                            'low': float(item['l']),
                            'close': float(item['c']),
                            'volume': float(item['v'])
                        })
                
                logger.info(f"APIService (Sync): Birdeye API 分批获取成功，最终获得 {len(formatted_data)} 条OHLCV数据 for {token_address}")
                
                # 🔥 尝试更新内存缓存（如果有APIService实例存在的话）
                APIService._try_update_memory_cache(token_address, timeframe, formatted_data)
                
                return formatted_data
                
            except Exception as e:
                logger.error(f"APIService (Sync): Birdeye API失败 for {token_address}: {e}")
                return []
    
    @staticmethod
    def _try_update_memory_cache(token_address: str, timeframe: str, data: list):
        """尝试更新任何现有APIService实例的内存缓存"""
        try:
            import gc
            import time
            
            # 查找所有存在的APIService实例
            for obj in gc.get_objects():
                if isinstance(obj, APIService) and hasattr(obj, 'memory_cache'):
                    current_time = time.time()
                    obj.memory_cache[token_address] = {
                        'data': data.copy() if data else [],
                        'timestamp': current_time,
                        'timeframe': timeframe
                    }
                    logger.info(f"APIService (Static): 已更新实例内存缓存 {token_address} 的 OHLCV 数据 ({len(data) if data else 0} 条, {timeframe})")
                    
        except Exception as e:
            logger.debug(f"APIService (Static): 更新内存缓存失败: {e}")  # 使用debug级别，因为这不是关键错误

    @staticmethod
    def get_historical_tokens() -> List[Dict]:
        """同步获取历史代币列表 (不推荐在UI线程直接调用)"""
        logger.warning("APIService: 同步获取历史代币列表被调用。建议使用异步方法。")
        try:
            url = "https://ohlcv-sync.vercel.app/api/ohlcv-tokens"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                'Accept': 'application/json'
            }
            response = requests.get(url, headers=headers, timeout=60)
            response.raise_for_status()
            data = response.json()
            if isinstance(data, dict) and data.get('success') is True and 'data' in data and isinstance(data['data'], list):
                logger.info(f"同步获取到 {len(data['data'])} 个历史代币")
                return data['data']
            else:
                logger.error(f"历史代币API返回格式错误或未成功: {data}")
                return []
        except requests.exceptions.Timeout:
            logger.error("同步获取历史代币列表超时")
            return []
        except requests.exceptions.RequestException as e:
            logger.error(f"同步获取历史代币列表失败: {str(e)}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"同步解析历史代币JSON失败: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"同步获取历史代币时发生未知错误: {str(e)}", exc_info=True)
            return []

    @pyqtSlot(str, str, list)
    def on_worker_ohlcv_data_ready(self, token_address: str, timeframe: str, data: list):
        """处理工作线程的OHLCV数据就绪信号"""
        # 将数据存储到内存缓存
        current_time = time.time()
        self.memory_cache[token_address] = {
            'data': data.copy() if data else [],
            'timestamp': current_time,
            'timeframe': timeframe
        }
        logger.info(f"APIService: 已缓存 {token_address} 的 OHLCV 数据 ({len(data) if data else 0} 条, {timeframe})")
        
        # 转发信号
        self.ohlcv_data_ready.emit(token_address, timeframe, data)
    
    @pyqtSlot(str, str, str)
    def on_worker_ohlcv_error(self, token_address: str, timeframe: str, error_message: str):
        """处理工作线程的OHLCV错误信号 - 修复信号参数不匹配问题"""
        logger.warning(f"APIService: OHLCV错误 - {token_address} ({timeframe}): {error_message}")
        
        # 🔥 只转发错误信息，不包含token_address和timeframe
        # 这样避免了之前token_address被当作error_message传递的问题
        self.ohlcv_data_error.emit(error_message)

    def get_cached_ohlcv_data(self, token_address: str, timeframe: str = '1m') -> Optional[List[Dict]]:
        """获取缓存的 OHLCV 数据"""
        try:
            current_time = time.time()
            
            # 检查缓存中是否有该代币的数据
            if token_address in self.memory_cache:
                cache_entry = self.memory_cache[token_address]
                
                # 检查缓存是否过期
                if current_time - cache_entry['timestamp'] <= self.memory_cache_timeout:
                    # 检查时间周期是否匹配
                    if cache_entry['timeframe'] == timeframe:
                        # logger.info(f"APIService: 使用缓存的OHLCV数据: {token_address[:8]}... ({len(cache_entry['data'])} 条)")
                        # 🔥🔥 记录缓存命中到性能监控器
                        if self.performance_monitor:
                            self.performance_monitor.record_cache_hit()
                        return cache_entry['data'].copy()  # 返回数据副本
                    else:
                        logger.info(f"APIService: 缓存数据时间周期不匹配: 需要 {timeframe}, 缓存的是 {cache_entry['timeframe']}")
                else:
                    logger.info(f"APIService: 缓存已过期: {current_time - cache_entry['timestamp']:.1f}秒前的数据")
                    # 清理过期数据
                    del self.memory_cache[token_address]
            else:
                logger.info(f"APIService: 缓存中没有 {token_address} 的数据")
            
            return None
            
        except Exception as e:
            logger.error(f"APIService: 获取缓存数据失败: {e}")
            return None

# 简单测试 (仅当直接运行此文件时执行)
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    app = QApplication([]) # 需要 QApplication 才能创建 QObject

    # 创建 APIService 实例 (它现在会内部创建 DatabaseService)
    api_service = APIService()

    # 连接信号到本地函数进行测试
    def on_trending_success(tokens):
        print(f"\n[测试成功] 获取到 {len(tokens)} 个趋势代币: {tokens[:2]}...")
    def on_trending_error(msg):
        print(f"\n[测试失败] 获取趋势代币出错: {msg}")

    def on_ohlcv_success(ohlcv_data):
        print(f"\n[测试成功] 获取到 {len(ohlcv_data)} 条OHLCV数据: {ohlcv_data[:2]}...")
    def on_ohlcv_error(msg):
        print(f"\n[测试失败] 获取OHLCV数据出错: {msg}")

    api_service.trending_tokens_ready.connect(on_trending_success)
    api_service.trending_tokens_error.connect(on_trending_error)
    api_service.ohlcv_data_ready.connect(on_ohlcv_success)
    api_service.ohlcv_data_error.connect(on_ohlcv_error)

    print("开始异步测试...")
    api_service.get_trending_tokens_async() # 启动趋势数据获取

    # 模拟获取K线数据 (需要一个实际的代币地址)
    # 假设你有一个测试地址
    test_token_address = "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263" # 示例代币地址
    # 如果你想测试K线，确保BIRDEYE_API_KEY是有效的
    if BIRDEYE_API_KEY != "YOUR_BIRDEYE_API_KEY":
        api_service.get_ohlcv_data_async(test_token_address, '1m', 1, 'trend')
    else:
        print("警告: BIRDEYE_API_KEY未设置，跳过K线数据异步测试。")

    # 运行事件循环，让异步任务有机会执行
    app.exec_()
    print("测试结束。")