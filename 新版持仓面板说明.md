# 新版智能持仓面板

基于 `trending_window.py` 设计的新版持仓面板，集成 OKX DEX API 和自动策略分析功能。

## 🚀 主要功能

### 1. 智能钱包连接
- **自动获取钱包地址**: 从 OKX DEX API 自动获取默认钱包的 Solana 地址
- **手动输入支持**: 支持手动输入钱包地址进行分析
- **实时数据同步**: 自动获取钱包的实时持仓数据和总价值

### 2. 持仓数据管理
- **实时持仓获取**: 通过 OKX DEX API 获取 Solana 钱包的所有代币持仓
- **智能排序**: 按代币价值自动排序，优先分析高价值持仓
- **风险代币检测**: 自动标识和过滤风险代币（空投代币、貔貅盘等）
- **价值统计**: 显示每个代币的数量、价格、价值和占比

### 3. 自动策略分析
- **批量分析**: 自动分析价值排名前 30 个代币（可配置 10/20/30）
- **多线程下载**: 并行下载 OHLCV 数据，提高效率
- **策略信号**: 实时显示买入/卖出/持有/观察信号
- **自动刷新**: 30 秒自动刷新周期，持续监控持仓变化

### 4. 可视化图表
- **K线图表**: 双击代币行或点击"图表"按钮打开独立的K线图表窗口
- **策略指标**: 图表中显示策略分析结果和技术指标
- **缓存优化**: 使用数据缓存减少重复API调用

### 5. 用户界面
- **直观显示**: 清晰的表格显示所有持仓信息
- **状态跟踪**: 实时显示分析进度和状态信息
- **颜色编码**: 使用颜色区分不同类型的信号和状态

## 📋 界面说明

### 钱包控制面板
```
[钱包地址输入框] [获取钱包信息] [刷新持仓] 总价值: $X,XXX.XX
```

### 策略控制面板
```
☑ 启用自动策略分析 (30秒周期)  分析前 [30▼] 个代币  [立即分析]  状态: 就绪  [进度条]
```

### 持仓表格
| 图像 | 代币 | 数量 | 价格 | 价值 | 占比 | 链 | 风险 | 策略 | 信号时间 | 24h变化 | 操作 |
|------|------|------|------|------|------|-----|------|------|----------|----------|------|
| 🪙   | SOL  | 1.23 | $180 | $221 | 45% | Solana | ✅ | 买入 | 14:30:25 | +5.2% | [图表] |

## 🛠 技术架构

### 核心组件
1. **HoldingsWindow**: 主窗口类，继承自 QDialog
2. **OKXDexClient**: OKX DEX API 客户端
3. **MultiThreadOHLCVManager**: 多线程 OHLCV 数据管理器
4. **TokenImageLabel**: 代币图像显示组件（复用自 trending_window）
5. **ChartWidget**: K线图表组件

### 数据流程
```
OKX DEX API → 钱包信息 → 持仓数据 → 价值排序 → 策略分析 → 信号显示
```

### 信号处理
```python
# 主要信号
strategy_signal_generated(token_address, symbol, signal_type, price, timestamp, strategy_name)
download_completed(token_address, symbol, ohlcv_data, token_data)
all_downloads_completed(success_count, total_count)
```

## 🚀 快速开始

### 1. 直接启动
```bash
python launch_holdings_panel.py
```

### 2. 测试模式
```bash
python test_holdings_window.py
```

### 3. 在现有程序中集成
```python
from holdings_panel_window import HoldingsWindow
from api_service import APIService

# 创建API服务
api_service = APIService()

# 创建持仓面板
holdings_window = HoldingsWindow(api_service=api_service)
holdings_window.show()
```

## ⚙️ 配置选项

### config.py 配置
```python
PORTFOLIO_CONFIG = {
    "okx_dex_api_url": "https://okx-local-api.vercel.app",
    "refresh_interval": 30000,  # 刷新间隔（毫秒）
    "default_strategy": "VWAP 交叉策略",
    "max_monitor_count": 20,
    # ... 其他配置
}
```

### 自定义分析数量
- 支持分析前 10、20、30 个代币
- 通过界面下拉菜单选择
- 减少分析数量可提高响应速度

## 🔧 依赖要求

### Python 包
```bash
pip install PyQt5 requests pandas python-dotenv
```

### 外部服务
- **OKX DEX API**: 必须运行在配置的URL地址
- **Birdeye API**: 用于获取OHLCV数据（需要API密钥）
- **趋势榜单API**: 可选，用于代币信息补充

## 📊 使用流程

### 1. 启动应用
- 运行启动器脚本
- 系统自动初始化API服务和界面

### 2. 获取钱包信息
- 点击"获取钱包信息"自动从OKX获取钱包地址
- 或手动输入钱包地址

### 3. 查看持仓数据
- 系统自动获取所有代币持仓
- 按价值排序显示在表格中
- 显示总价值和各代币占比

### 4. 启用自动分析
- 勾选"启用自动策略分析"
- 系统每30秒自动分析前N个代币
- 在表格中显示策略信号

### 5. 查看详细图表
- 双击代币行打开K线图表
- 或点击"图表"按钮
- 图表显示详细的技术分析

## 🎯 策略信号说明

| 信号 | 颜色 | 含义 |
|------|------|------|
| 买入 | 🟢 绿色 | 策略建议买入该代币 |
| 卖出 | 🔴 红色 | 策略建议卖出该代币 |
| 持有 | 🟠 橙色 | 策略建议继续持有 |
| 观察 | ⚪ 灰色 | 策略建议观察等待 |

## ⚠️ 注意事项

### 风险提示
- 策略信号仅供参考，不构成投资建议
- 请结合自己的判断进行投资决策
- 注意风险代币的标识，谨慎操作

### 性能优化
- 建议分析代币数量不超过30个
- 网络不稳定时可能影响数据获取
- 大量并发分析可能触发API限制

### 故障排除
1. **无法获取钱包信息**: 检查OKX DEX API服务状态
2. **持仓数据为空**: 确认钱包地址正确且有持仓
3. **策略分析失败**: 检查网络连接和API密钥
4. **图表无法打开**: 确保OHLCV数据下载成功

## 🔄 更新日志

### v1.0.0 (初始版本)
- ✅ 实现基础持仓显示功能
- ✅ 集成OKX DEX API
- ✅ 添加自动策略分析
- ✅ 支持K线图表显示
- ✅ 实现多线程数据下载
- ✅ 添加风险代币检测

## 📞 技术支持

如遇到问题，请检查：
1. OKX DEX API服务状态
2. 网络连接
3. 配置文件设置
4. 日志输出信息

---

**开发基于**: trending_window.py 架构  
**适用平台**: Windows, macOS, Linux  
**Python版本**: 3.7+  
**界面框架**: PyQt5 