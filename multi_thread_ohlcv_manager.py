"""
多线程OHLCV数据下载管理器
负责批量下载代币的OHLCV数据并提供给ChartWidget进行策略计算
"""

import logging
import time
import pandas as pd
from typing import Dict, List, Optional, Callable
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from threading import Lock
import queue

from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtWidgets import QApplication

from api_service import APIService
from ui.chart_widget import ChartWidget
from config import PORTFOLIO_CONFIG
from signal_client import SignalData

logger = logging.getLogger(__name__)



class OHLCVDownloadTask:
    """OHLCV下载任务"""
    def __init__(self, token_data: Dict, strategy_name: str = None, timeframe: str = '1m', days: int = 1):
        self.token_data = token_data.copy()  # 创建副本避免修改原数据
        self.strategy_name = strategy_name or PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
        self.timeframe = timeframe
        self.days = days
        self.created_at = time.time()
        
        # 确保必要字段存在
        if 'strategy_name' not in self.token_data:
            self.token_data['strategy_name'] = self.strategy_name
        if 'timeframe' not in self.token_data:
            self.token_data['timeframe'] = self.timeframe
        if 'source' not in self.token_data:
            self.token_data['source'] = 'trend'  # 默认使用趋势数据源


class MultiThreadOHLCVManager(QObject):
    """多线程OHLCV下载管理器"""
    
    # 信号定义
    download_completed = pyqtSignal(str, str, list, dict)  # token_address, symbol, ohlcv_data, token_data
    download_failed = pyqtSignal(str, str, str)  # token_address, symbol, error_message
    all_downloads_completed = pyqtSignal(int, int)  # success_count, total_count
    strategy_signal_generated = pyqtSignal(object)  # Emits SignalData object
    strategy_analysis_completed = pyqtSignal(str, str, str, int)
    individual_raw_signal = pyqtSignal(str, str, str, float, int, str, float, dict)  # token_address, symbol, signal_type, price, timestamp_ms, strategy_name, confidence, metadata
    
    def __init__(self, api_service: APIService, max_workers: int = 5, parent=None):
        """
        初始化多线程OHLCV管理器
        
        参数:
            api_service: API服务实例
            max_workers: 最大并发下载线程数
            parent: 父对象
        """
        super().__init__(parent)
        self.api_service = api_service
        self.max_workers = max_workers
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务管理
        self.pending_tasks = queue.Queue()
        self.running_tasks = {}  # token_address -> future
        self.completed_tasks = {}  # token_address -> result
        self.failed_tasks = {}  # token_address -> error
        
        # ChartWidget管理（后台并行模式）
        self.chart_widgets = {}  # token_address -> ChartWidget
        
        # 统计信息
        self.total_submitted = 0
        self.total_completed = 0
        self.total_failed = 0
        
        # 线程锁
        self.stats_lock = Lock()
        
        # 定时器用于处理任务队列
        self.task_processor_timer = QTimer(self)
        self.task_processor_timer.timeout.connect(self.process_pending_tasks)
        
        logger.info(f"MultiThreadOHLCVManager: 初始化完成，最大并发数: {max_workers}")
    
    def submit_download_task(self, token_data: Dict, strategy_name: str = None, timeframe: str = '1m', days: int = 1):
        """
        提交OHLCV下载任务
        
        参数:
            token_data: 代币数据
            strategy_name: 策略名称
            timeframe: 时间周期
            days: 天数
        """
        try:
            token_address = token_data.get('tokenAddress') or token_data.get('address')
            if not token_address:
                logger.error("MultiThreadOHLCVManager: 代币数据缺少地址信息")
                return False
            
            # 检查是否已在处理中
            if token_address in self.running_tasks:
                logger.debug(f"MultiThreadOHLCVManager: {token_address} 已在下载队列中，跳过")
                return False
            
            # 创建下载任务
            task = OHLCVDownloadTask(token_data, strategy_name, timeframe, days)
            self.pending_tasks.put(task)
            
            symbol = token_data.get('symbol', 'Unknown')
            logger.info(f" DEBUG: 任务已加入队列 - {symbol}, 队列大小: {self.pending_tasks.qsize()}")
            logger.info(f"MultiThreadOHLCVManager: 提交下载任务 {symbol} ({token_address[:8]}...)")
            
            # 启动任务处理器
            if not self.task_processor_timer.isActive():
                logger.info(f" DEBUG: 启动任务处理器定时器")
                self.task_processor_timer.start(100)  # 每100ms检查一次
            else:
                logger.info(f" DEBUG: 任务处理器定时器已在运行")
            
            return True
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 提交下载任务失败: {e}")
            return False
    
    def submit_batch_download(self, token_list: List[Dict], strategy_name: str = None, timeframe: str = '1m', days: int = 1):
        """
        批量提交OHLCV下载任务
        
        参数:
            token_list: 代币数据列表
            strategy_name: 策略名称
            timeframe: 时间周期
            days: 天数
        """
        logger.info(f"MultiThreadOHLCVManager: 批量提交 {len(token_list)} 个下载任务")
        
        submitted_count = 0
        for token_data in token_list:
            if self.submit_download_task(token_data, strategy_name, timeframe, days):
                submitted_count += 1
        
        logger.info(f"MultiThreadOHLCVManager: 成功提交 {submitted_count}/{len(token_list)} 个下载任务")
        return submitted_count
    
    def submit_task(self, task: OHLCVDownloadTask):
        """提交单个OHLCV下载任务（兼容性方法）"""
        return self.submit_download_task(
            token_data=task.token_data,
            strategy_name=task.strategy_name,
            timeframe=task.timeframe,
            days=task.days
        )
    
    @pyqtSlot()
    def process_pending_tasks(self):
        """处理待处理的任务队列"""
        try:
            logger.info(f" DEBUG: process_pending_tasks 被调用，队列大小: {self.pending_tasks.qsize()}")
            
            # 检查是否有空闲的worker
            current_running = len(self.running_tasks)
            if current_running >= self.max_workers:
                logger.info(f" DEBUG: 所有worker都在忙，当前运行: {current_running}/{self.max_workers}")
                return
            
            # 从队列中取出任务
            available_slots = self.max_workers - current_running
            tasks_to_start = []
            
            for _ in range(available_slots):
                try:
                    task = self.pending_tasks.get_nowait()
                    tasks_to_start.append(task)
                except queue.Empty:
                    break
            
            if not tasks_to_start:
                # 没有待处理任务，检查是否可以停止定时器
                if not self.running_tasks and self.pending_tasks.empty():
                    self.task_processor_timer.stop()
                return
            
            # 启动任务
            for task in tasks_to_start:
                self.start_download_task(task)
                
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 处理任务队列时出错: {e}")
    
    def start_download_task(self, task: OHLCVDownloadTask):
        """启动单个下载任务"""
        try:
            token_address = task.token_data.get('tokenAddress') or task.token_data.get('address')
            symbol = task.token_data.get('symbol', 'Unknown')
            
            # 提交到线程池
            logger.info(f" DEBUG: 准备提交下载任务到线程池 - {symbol}")
            future = self.executor.submit(self.download_worker, task)
            future.token_address = token_address
            future.symbol = symbol
            future.add_done_callback(self.on_download_completed)
            
            # 记录运行中的任务
            self.running_tasks[token_address] = future
            
            with self.stats_lock:
                self.total_submitted += 1
            
            logger.info(f" DEBUG: 下载任务已提交到线程池 - {symbol}, 总提交数: {self.total_submitted}")
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 启动下载任务失败: {e}")
    
    def download_worker(self, task: OHLCVDownloadTask) -> tuple:
        """
        下载工作线程
        
        返回:
            tuple: (success: bool, data: list or error: str, task: OHLCVDownloadTask)
        """
        try:
            token_address = task.token_data.get('tokenAddress') or task.token_data.get('address')
            symbol = task.token_data.get('symbol', 'Unknown')
            
            logger.info(f" DEBUG: Worker线程开始执行 - {symbol}")
            logger.debug(f"MultiThreadOHLCVManager Worker: 开始下载 {symbol}")
            
            # 添加重试机制和更好的错误处理
            max_retries = 2
            retry_delay = 1  # 秒
            
            for attempt in range(max_retries + 1):
                try:
                    # 使用APIService的静态方法下载数据
                    ohlcv_data = APIService.get_ohlcv_data(
                        token_address=token_address,
                        timeframe=task.timeframe,
                        days=task.days,
                        source=task.token_data.get('source', 'trend')
                    )
                    
                    if ohlcv_data and len(ohlcv_data) > 0:
                        logger.debug(f"MultiThreadOHLCVManager Worker: {symbol} 下载成功，{len(ohlcv_data)} 条数据")
                        return (True, ohlcv_data, task)
                    elif attempt < max_retries:
                        logger.debug(f"MultiThreadOHLCVManager Worker: {symbol} 无数据，重试 {attempt + 1}/{max_retries}")
                        time.sleep(retry_delay * (attempt + 1))  # 递增延迟
                        continue
                    else:
                        error_msg = f"No OHLCV data available"
                        logger.debug(f"MultiThreadOHLCVManager Worker: {symbol} {error_msg}")
                        return (False, error_msg, task)
                        
                except Exception as api_error:
                    if attempt < max_retries:
                        logger.debug(f"MultiThreadOHLCVManager Worker: {symbol} API错误，重试 {attempt + 1}/{max_retries}: {api_error}")
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        raise api_error
                        
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            # 只在调试模式下记录详细错误
            if "400 Client Error" in str(e) or "Bad Request" in str(e):
                logger.debug(f"MultiThreadOHLCVManager Worker: {symbol} API请求错误: {e}")
            else:
                logger.warning(f"MultiThreadOHLCVManager Worker: {symbol} {error_msg}")
            return (False, error_msg, task)
    
    def on_download_completed(self, future):
        """下载完成回调"""
        try:
            token_address = future.token_address
            symbol = future.symbol
            
            logger.info(f" DEBUG: on_download_completed 被调用 - {symbol}")
            
            # 从运行任务中移除
            if token_address in self.running_tasks:
                del self.running_tasks[token_address]
            
            # 获取结果
            success, data_or_error, task = future.result()
            
            logger.info(f" DEBUG: 任务结果 - {symbol}, 成功: {success}, 数据类型: {type(data_or_error)}")
            
            if success:
                # 下载成功
                ohlcv_data = data_or_error
                self.completed_tasks[token_address] = ohlcv_data
                
                with self.stats_lock:
                    self.total_completed += 1
                
                # 发射成功信号
                self.download_completed.emit(token_address, symbol, ohlcv_data, task.token_data)
                
                # 调试：确认进入策略分析阶段
                logger.info(f" DEBUG: {symbol} 准备进入策略分析阶段，数据条数: {len(ohlcv_data)}")
                
                # 创建或更新ChartWidget（后台并行模式）
                self.create_or_update_chart_widget(task.token_data, ohlcv_data)
                
                logger.info(f"MultiThreadOHLCVManager: {symbol} 下载完成并处理")
                
            else:
                # 下载失败
                error_msg = data_or_error
                self.failed_tasks[token_address] = error_msg
                
                with self.stats_lock:
                    self.total_failed += 1
                
                # 发射失败信号
                self.download_failed.emit(token_address, symbol, error_msg)
                
                # 根据错误类型调整日志级别
                if "No OHLCV data available" in error_msg or "Bad Request" in error_msg:
                    logger.debug(f"MultiThreadOHLCVManager: {symbol} 下载失败: {error_msg}")
                else:
                    logger.warning(f"MultiThreadOHLCVManager: {symbol} 下载失败: {error_msg}")
            
            # 检查是否所有任务都完成了
            with self.stats_lock:
                if self.total_completed + self.total_failed >= self.total_submitted:
                    self.all_downloads_completed.emit(self.total_completed, self.total_submitted)
                    logger.info(f"MultiThreadOHLCVManager: 所有下载任务完成 - 成功: {self.total_completed}, 失败: {self.total_failed}")
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 处理下载完成回调时出错: {e}")
    
    def create_or_update_chart_widget(self, token_data: Dict, ohlcv_data: List):
        """创建或更新ChartWidget（后台并行模式）"""
        try:
            token_address = token_data.get('tokenAddress') or token_data.get('address')
            symbol = token_data.get('symbol', 'Unknown')
            
            # 重要：由于线程安全问题，我们使用信号发射机制来通知策略分析完成
            # 而不是直接创建ChartWidget，避免在工作线程中创建GUI组件
            logger.info(f"MultiThreadOHLCVManager: 开始处理 {symbol} 的策略分析（使用策略分析完成信号）")
            
            # 直接进行策略分析并发射结果信号
            self.perform_strategy_analysis_and_emit_signal(token_data, ohlcv_data)
            
            logger.info(f"MultiThreadOHLCVManager: 完成 {symbol} 的策略分析处理")
                
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 策略分析处理失败 {symbol}: {e}")
    
    def perform_strategy_analysis_and_emit_signal(self, token_data: Dict, ohlcv_data: List):
        """执行策略分析并发射信号"""
        try:
            symbol = token_data.get('symbol', 'Unknown')
            token_address = token_data.get('tokenAddress') or token_data.get('address', 'Unknown')
            logger.info(f"MultiThreadOHLCVManager: 开始为 {symbol} 执行策略分析")
            
            # 转换数据为DataFrame
            if not ohlcv_data:
                logger.warning(f"MultiThreadOHLCVManager: {symbol} 没有OHLCV数据")
                return
            
            df = pd.DataFrame(ohlcv_data)
            if df.empty:
                logger.warning(f"MultiThreadOHLCVManager: {symbol} DataFrame为空")
                return
            
            # 从token_data获取策略名称
            strategy_name = token_data.get('strategy_name') or PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
            
            # 获取策略实例
            from strategies import StrategyFactory
            strategy = StrategyFactory.get_strategy_by_name(strategy_name)
            if not strategy:
                logger.error(f"MultiThreadOHLCVManager: {symbol} 无法获取策略: {strategy_name}")
                return
            
            # 计算指标（ChartWidget中的逻辑）
            try:
                # 添加必要的技术指标
                from ta.trend import SMAIndicator, EMAIndicator
                from ta.momentum import RSIIndicator
                from ta.volatility import BollingerBands
                # from ta.volume import VolumeSMAIndicator  # 此指标在ta库中不存在
                import ta
                
                # SMA均线
                df['sma_5'] = SMAIndicator(close=df['close'], window=5).sma_indicator()
                df['sma_10'] = SMAIndicator(close=df['close'], window=10).sma_indicator()
                df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()
                
                # EMA均线
                df['ema_12'] = EMAIndicator(close=df['close'], window=12).ema_indicator()
                df['ema_26'] = EMAIndicator(close=df['close'], window=26).ema_indicator()
                
                # RSI
                df['rsi'] = RSIIndicator(close=df['close'], window=14).rsi()
                
                # MACD
                macd = ta.trend.MACD(close=df['close'])
                df['macd'] = macd.macd()
                df['macd_signal'] = macd.macd_signal()
                df['macd_histogram'] = macd.macd_diff()
                
                # 布林带
                bollinger = BollingerBands(close=df['close'], window=20, window_dev=2)
                df['bb_upper'] = bollinger.bollinger_hband()
                df['bb_lower'] = bollinger.bollinger_lband()
                df['bb_middle'] = bollinger.bollinger_mavg()
                
                # VWAP (如果有成交量数据)
                if 'volume' in df.columns:
                    df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
                else:
                    # 没有成交量数据时，使用价格加权平均作为替代
                    df['vwap'] = df['close'].expanding().mean()
                
                # Parabolic SAR
                df['sar'] = ta.trend.PSARIndicator(high=df['high'], low=df['low'], close=df['close']).psar()
                
            except Exception as indicator_error:
                logger.warning(f"MultiThreadOHLCVManager: {symbol} 计算技术指标时出错: {indicator_error}")
                # 如果指标计算失败，至少确保有VWAP
                if 'vwap' not in df.columns:
                    df['vwap'] = df['close'].expanding().mean()
            
            # 执行策略分析
            df_with_signals = strategy.generate_signals(df)
            if df_with_signals is None or df_with_signals.empty:
                logger.warning(f"MultiThreadOHLCVManager: {symbol} 策略未生成信号")
                return
            
            # --- BEGIN MODIFICATION: Emit all individual raw signals ---
            # 确保 strategy 对象存在且 df_with_signals 不是 None
            if strategy and df_with_signals is not None and not df_with_signals.empty and 'signal' in df_with_signals.columns:
                logger.debug(f"MultiThreadOHLCVManager: Processing {len(df_with_signals)} k-lines for raw signals for {symbol}")
                for i in range(len(df_with_signals)):
                    signal_val = df_with_signals['signal'].iloc[i]
                    
                    if signal_val == 1 or signal_val == -1: # 买入或卖出信号
                        signal_type = "buy" if signal_val == 1 else "sell"
                        
                        # 确定时间戳
                        # 优先使用 'timestamp' 列（通常是毫秒级 Unix 时间戳）
                        # 否则，如果索引是 DatetimeIndex, 使用它
                        timestamp_ms = None
                        if 'timestamp' in df_with_signals.columns:
                            timestamp_ms = int(df_with_signals['timestamp'].iloc[i])
                        elif isinstance(df_with_signals.index, pd.DatetimeIndex):
                            timestamp_val = df_with_signals.index[i]
                            timestamp_ms = int(timestamp_val.timestamp() * 1000)
                        else:
                            logger.warning(f"MultiThreadOHLCVManager: {symbol} 无法从DataFrame获取精确信号时间戳在索引 {i}. 将使用当前时间近似.")
                            timestamp_ms = int(time.time() * 1000) 

                        price = float(df_with_signals['close'].iloc[i]) # 确保是 float
                        strategy_name = strategy.name if hasattr(strategy, 'name') and strategy.name else "UnknownStrategy"
                        confidence = 1.0 # 默认置信度
                        metadata = {
                            "kline_index": i, 
                            "kline_time": pd.to_datetime(timestamp_ms, unit='ms').isoformat() if timestamp_ms else "N/A",
                            "source_dataframe_rows": len(df_with_signals),
                            "strategy_params": strategy.get_params() if hasattr(strategy, 'get_params') else {}
                        }

                        logger.info(f"MultiThreadOHLCVManager: Emitting RAW signal for {symbol}: {signal_type} @ {price:.8f}, Time: {timestamp_ms}, Strategy: {strategy_name}")
                        self.individual_raw_signal.emit(
                            token_data.get('address', 'N/A'),
                            symbol,
                            signal_type,
                            price,
                            timestamp_ms,
                            strategy_name,
                            confidence,
                            metadata
                        )
            else:
                logger.debug(f"MultiThreadOHLCVManager: No raw signals to emit for {symbol} (df_with_signals is None, empty, or no 'signal' column, or no strategy).")
            # --- END MODIFICATION ---
            
            # --- BEGIN NEW UNIFIED SIGNAL LOGIC ---
            final_signal_type_str = "wait"  # Default to wait
            final_signal_index = -1
            # Initialize with latest data point as fallback
            final_price = df_with_signals['close'].iloc[-1] if df_with_signals is not None and not df_with_signals.empty else 0.0
            final_timestamp_ms = int(df_with_signals['timestamp'].iloc[-1]) if df_with_signals is not None and not df_with_signals.empty and 'timestamp' in df_with_signals.columns else int(time.time() * 1000)

            if df_with_signals is not None and not df_with_signals.empty and 'signal' in df_with_signals.columns:
                signals_arr = df_with_signals['signal'].values
                buy_positions = [i for i, s in enumerate(signals_arr) if s == 1]
                sell_positions = [i for i, s in enumerate(signals_arr) if s == -1]

                last_buy_pos = buy_positions[-1] if buy_positions else -1
                last_sell_pos = sell_positions[-1] if sell_positions else -1

                if not buy_positions and not sell_positions:
                    final_signal_type_str = "wait" 
                    final_signal_index = -1 # No specific candle for 'wait' based on this logic
                elif last_buy_pos > last_sell_pos:
                    final_signal_index = last_buy_pos
                    signals_after_buy = signals_arr[last_buy_pos + 1:]
                    if len(signals_after_buy) >= 10 and all(s == 0 for s in signals_after_buy[-10:]):
                        final_signal_type_str = "hold"
                    else:
                        final_signal_type_str = "buy"
                elif last_sell_pos > last_buy_pos:
                    final_signal_index = last_sell_pos
                    final_signal_type_str = "sell"
                else: # Both -1 or equal
                    final_signal_type_str = "wait"
                    final_signal_index = -1
                
                if final_signal_index != -1:
                    final_price = float(df_with_signals['close'].iloc[final_signal_index])
                    if 'timestamp' in df_with_signals.columns:
                        final_timestamp_ms = int(df_with_signals['timestamp'].iloc[final_signal_index])
                    # If 'timestamp' column is not present, final_timestamp_ms (initialized earlier) will be used
            # If df_with_signals is None or empty, or no 'signal' column, defaults (wait, latest price/time) are used.
            
            logger.info(f"MultiThreadOHLCVManager: {symbol} unified analysis result: Signal: {final_signal_type_str}, Index: {final_signal_index}, Price: {final_price:.8f}, TimestampMS: {final_timestamp_ms}")

            signal_data = SignalData(
                token_address=token_address,
                symbol=symbol,
                signal_type=final_signal_type_str,
                price=final_price,
                timestamp=int(final_timestamp_ms / 1000), # Convert ms to s for SignalData
                strategy_name=strategy_name, # strategy_name is defined earlier in the method
                source="ohlcv_manager_unified",
                confidence=1.0, 
                metadata={
                    "kline_index": final_signal_index,
                    "kline_time_ms": final_timestamp_ms,
                    "source_dataframe_rows": len(df_with_signals) if df_with_signals is not None else 0,
                    "strategy_params": strategy.get_params() if hasattr(strategy, 'get_params') else {}
                }
            )
            
            self.strategy_signal_generated.emit(signal_data)
            logger.info(f"🔥 MultiThreadOHLCVManager: Emitted unified SignalData - {symbol} -> {signal_data.signal_type} @ {signal_data.price:.8f}")
            # --- END NEW UNIFIED SIGNAL LOGIC ---
            
            logger.info(f"MultiThreadOHLCVManager: {symbol} 策略分析完成")
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 执行策略分析失败 {symbol}: {e}", exc_info=True)
    

    
    @pyqtSlot(object)  # Changed to accept SignalData object
    def on_strategy_signal_generated(self, signal_data: SignalData):  # Changed to accept SignalData object
        """处理策略信号 (现在接收SignalData对象)"""
        try:
            sender = self.sender()
            sender_type = type(sender).__name__ if sender else "None"
            
            logger.info(f"🔥 DEBUG: 收到策略信号 (SignalData) - 发送者类型: {sender_type}, 信号: {signal_data.signal_type}, 策略: {signal_data.strategy_name}, 来自: {signal_data.source}, Token: {signal_data.symbol}")

            # This slot's original purpose was to capture signals from ChartWidgets this manager created,
            # potentially enrich them, and re-emit via the manager's central 'strategy_signal_generated'.
            # Now, 'perform_strategy_analysis_and_emit_signal' directly emits a complete SignalData.
            # If ChartWidgets are modified to also emit SignalData, and they are still connected here,
            # this slot would receive that SignalData.
            # Re-emitting it ensures that any external listeners connected to the manager's 
            # 'strategy_signal_generated' signal will receive it.
            
            # Forward the received SignalData object.
            self.strategy_signal_generated.emit(signal_data)
            
            # Updated logging using information from SignalData
            signal_type_zh_map = {"buy": "买入", "sell": "卖出", "hold": "持有", "wait": "观察"}
            signal_type_zh = signal_type_zh_map.get(signal_data.signal_type, signal_data.signal_type)
            
            logger.info(f"MultiThreadOHLCVManager: {signal_data.symbol} 策略信号转发 (SignalData) - {signal_type_zh} @ ${signal_data.price:.6f}, Source: {signal_data.source}")

        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 处理策略信号(SignalData)时出错: {e}", exc_info=True)
    
    @pyqtSlot(str, str, str, int)
    def on_strategy_analysis_completed(self, token_address: str, token_symbol: str, final_signal: str, chart_index: int):
        """处理策略分析完成信号"""
        try:
            logger.info(f"🔥 DEBUG: MultiThreadOHLCVManager 接收到策略分析完成信号 - {token_symbol}: {final_signal}")
            
            # 🔥 测试：检查发送者
            sender = self.sender()
            if sender:
                sender_type = type(sender).__name__
                object_name = getattr(sender, 'objectName', lambda: 'N/A')()
                logger.info(f"🔥 DEBUG: 策略分析信号发送者 - 类型: {sender_type}, ObjectName: {object_name}")
            else:
                logger.warning(f"🔥 DEBUG: 警告 - 没有找到策略分析信号发送者")
            
            # 🔥 将最终策略信号转发给TrendingWindow进行表格更新
            # 尝试获取信号发生的实际时间戳和价格
            signal_timestamp = int(time.time())  # 默认使用当前时间
            signal_price = 0.0

            # 尝试从发送者获取信号时间戳和价格
            sender = self.sender()
            # 🔥 修复：支持所有类型的图表组件获取价格数据和时间戳
            if sender and hasattr(sender, 'df') and sender.df is not None and not sender.df.empty:
                try:
                    # 尝试获取最新价格
                    signal_price = float(sender.df['close'].iloc[-1])

                    # 🔥 尝试获取信号发生的时间戳
                    # 如果发送者有最新的信号信息，使用信号发生的时间
                    if hasattr(sender, 'current_buy_signals') and sender.current_buy_signals:
                        # 获取最新买入信号的时间戳
                        latest_buy = sender.current_buy_signals[-1]
                        if len(latest_buy) >= 3:  # (index, price, timestamp)
                            signal_timestamp = latest_buy[2]
                            signal_price = latest_buy[1]
                            logger.info(f"MultiThreadOHLCVManager: {token_symbol} 使用买入信号时间戳: {signal_timestamp}")
                    elif hasattr(sender, 'current_sell_signals') and sender.current_sell_signals:
                        # 获取最新卖出信号的时间戳
                        latest_sell = sender.current_sell_signals[-1]
                        if len(latest_sell) >= 3:  # (index, price, timestamp)
                            signal_timestamp = latest_sell[2]
                            signal_price = latest_sell[1]
                            logger.info(f"MultiThreadOHLCVManager: {token_symbol} 使用卖出信号时间戳: {signal_timestamp}")
                    elif 'timestamp' in sender.df.columns:
                        # 如果没有具体的信号时间戳，使用最新的数据时间戳
                        signal_timestamp = int(sender.df['timestamp'].iloc[-1])
                        logger.info(f"MultiThreadOHLCVManager: {token_symbol} 使用最新数据时间戳: {signal_timestamp}")

                except (IndexError, ValueError, KeyError) as e:
                    logger.debug(f"无法从发送者({type(sender).__name__})获取 {token_symbol} 的信号时间戳/价格: {e}")
            else:
                logger.debug(f"MultiThreadOHLCVManager: {token_symbol} 无法从发送者获取数据，使用当前时间")
            
            # 转发策略信号到trending window（用于更新策略列）
            # 🔥 支持更多信号类型：买入、卖出、持有、观察
            if final_signal in ["买入", "卖出", "持有", "观察"]:
                # 将不同的信号类型映射到显示文本
                if final_signal == "买入":
                    signal_type = "buy"
                    display_signal = "买入"
                elif final_signal == "卖出":
                    signal_type = "sell" 
                    display_signal = "卖出"
                elif final_signal == "持有":
                    signal_type = "hold"
                    display_signal = "持有"
                else:  # 观察
                    signal_type = "wait"
                    display_signal = "观察"
                
                strategy_name = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
                
                # 🔥 创建SignalData对象并发射信号
                # 🔥 测试：发射信号前的详细日志
                logger.info(f"🔥 TEST: 准备发射策略信号 - {token_symbol}: {signal_type} -> {display_signal}")
                logger.info(f"🔥 TEST: 信号参数 - token_address: {token_address}, price: {signal_price}, timestamp: {signal_timestamp}")

                signal_data = SignalData(
                    token_address=token_address,
                    symbol=token_symbol,
                    signal_type=signal_type,
                    price=signal_price,
                    timestamp=signal_timestamp,
                    strategy_name=strategy_name,
                    source="strategy_analysis_completed",
                    confidence=1.0,
                    metadata={"display_signal": display_signal}
                )

                self.strategy_signal_generated.emit(signal_data)
                logger.info(f"🔥 TEST: 已发射策略信号 (SignalData) - {token_symbol} -> {display_signal}")
            else:
                logger.warning(f"MultiThreadOHLCVManager: {token_symbol} 未知的策略信号: {final_signal}")
                
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 处理策略分析完成信号时出错: {e}")
    
    def get_chart_widget(self, token_address: str) -> Optional[ChartWidget]:
        """获取指定代币的ChartWidget"""
        return self.chart_widgets.get(token_address)
    
    def get_headless_widget(self, token_address: str) -> Optional[ChartWidget]:
        """获取指定代币的ChartWidget（兼容性方法，保持向后兼容）"""
        return self.chart_widgets.get(token_address)
    
    def get_all_chart_widgets(self) -> Dict[str, ChartWidget]:
        """获取所有ChartWidget"""
        return self.chart_widgets.copy()
    
    def get_all_headless_widgets(self) -> Dict[str, ChartWidget]:
        """获取所有ChartWidget（兼容性方法，保持向后兼容）"""
        return self.chart_widgets.copy()
    
    def remove_chart_widget(self, token_address: str):
        """移除指定代币的ChartWidget"""
        try:
            if token_address in self.chart_widgets:
                widget = self.chart_widgets[token_address]
                if hasattr(widget, 'stop_activity'):
                    widget.stop_activity()
                widget.deleteLater()
                del self.chart_widgets[token_address]
                logger.info(f"MultiThreadOHLCVManager: 移除ChartWidget {token_address}")
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 移除ChartWidget失败: {e}")
    
    def remove_headless_widget(self, token_address: str):
        """移除指定代币的ChartWidget（兼容性方法，保持向后兼容）"""
        self.remove_chart_widget(token_address)
    
    def clear_all_widgets(self):
        """清理所有ChartWidget"""
        try:
            logger.info(f"MultiThreadOHLCVManager: 清理 {len(self.chart_widgets)} 个ChartWidget")
            for token_address in list(self.chart_widgets.keys()):
                self.remove_chart_widget(token_address)
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 清理所有ChartWidget失败: {e}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        with self.stats_lock:
            return {
                'total_submitted': self.total_submitted,
                'total_completed': self.total_completed,
                'total_failed': self.total_failed,
                'currently_running': len(self.running_tasks),
                'pending_tasks': self.pending_tasks.qsize(),
                'active_widgets': len(self.chart_widgets),
                'completion_rate': (self.total_completed / self.total_submitted * 100) if self.total_submitted > 0 else 0
            }
    
    def force_stop_all_tasks(self):
        """强制停止所有正在进行的任务"""
        try:
            logger.info(f"MultiThreadOHLCVManager: 强制停止 {len(self.running_tasks)} 个运行中的任务...")
            
            # 取消所有正在运行的任务
            cancelled_count = 0
            for token_address, future in list(self.running_tasks.items()):
                try:
                    if future.cancel():
                        cancelled_count += 1
                        logger.debug(f"MultiThreadOHLCVManager: 成功取消任务 {token_address[:8]}...")
                    else:
                        logger.debug(f"MultiThreadOHLCVManager: 无法取消任务 {token_address[:8]}... (可能已开始执行)")
                except Exception as e:
                    logger.debug(f"MultiThreadOHLCVManager: 取消任务 {token_address[:8]}... 时出错: {e}")
            
            # 清理运行任务列表
            self.running_tasks.clear()
            
            # 清理待处理任务队列
            cleared_pending = 0
            try:
                while not self.pending_tasks.empty():
                    self.pending_tasks.get_nowait()
                    cleared_pending += 1
            except queue.Empty:
                pass
            
            # 停止任务处理器定时器
            if self.task_processor_timer.isActive():
                self.task_processor_timer.stop()
            
            logger.info(f"MultiThreadOHLCVManager: 强制停止完成 - 取消: {cancelled_count}, 清理待处理: {cleared_pending}")
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 强制停止任务时出错: {e}")
    
    def reset_statistics(self):
        """重置统计信息"""
        with self.stats_lock:
            self.total_submitted = 0
            self.total_completed = 0
            self.total_failed = 0
            self.completed_tasks.clear()
            self.failed_tasks.clear()
            logger.debug("MultiThreadOHLCVManager: 统计信息已重置")
    
    def is_busy(self) -> bool:
        """检查是否正在进行任务"""
        return len(self.running_tasks) > 0 or not self.pending_tasks.empty()

    def shutdown(self):
        """关闭管理器"""
        try:
            logger.info("MultiThreadOHLCVManager: 开始关闭...")
            
            # 强制停止所有任务
            self.force_stop_all_tasks()
            
            # 清理所有ChartWidget
            self.clear_all_widgets()
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            logger.info("MultiThreadOHLCVManager: 关闭完成")
            
        except Exception as e:
            logger.error(f"MultiThreadOHLCVManager: 关闭时出错: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.shutdown()
        except Exception as e:
            logger.debug(f"MultiThreadOHLCVManager: 析构时出错: {e}") 