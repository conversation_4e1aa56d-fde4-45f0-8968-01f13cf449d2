"""
快速信号一致性测试 - 用于验证HeadlessChartWidget和ChartWidget的一致性
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from indicators import TechnicalIndicators
from strategies import StrategyFactory

def create_test_data(n_points=200):
    """创建一致的测试数据"""
    np.random.seed(42)  # 固定随机种子确保一致性
    
    # 生成价格数据
    base_price = 0.001
    trend = np.linspace(0, 0.0005, n_points)
    noise = np.random.normal(0, 0.00001, n_points)
    prices = base_price + trend + noise
    
    # 创建OHLCV数据
    df = pd.DataFrame({
        'timestamp': pd.date_range(start='2024-01-01', periods=n_points, freq='1min').astype(int) // 10**9,
        'open': prices * (1 + np.random.uniform(-0.001, 0.001, n_points)),
        'high': prices * (1 + np.random.uniform(0, 0.002, n_points)),
        'low': prices * (1 - np.random.uniform(0, 0.002, n_points)),
        'close': prices,
        'volume': np.random.uniform(1000, 10000, n_points)
    })
    
    # 设置时间索引
    df['datetime_pd'] = pd.to_datetime(df['timestamp'], unit='s')
    df = df.set_index('datetime_pd')
    
    return df

def test_indicators_consistency():
    """测试技术指标计算的一致性"""
    print("🔍 测试技术指标计算一致性...")
    
    # 创建两个相同的数据集
    df1 = create_test_data()
    df2 = df1.copy()
    
    # 分别计算技术指标
    df1_with_indicators = TechnicalIndicators.add_all_indicators(df1)
    df2_with_indicators = TechnicalIndicators.add_all_indicators(df2)
    
    # 比较结果
    indicator_columns = ['rsi', 'macd', 'macd_signal', 'vwap', 'sar', 'bb_high', 'bb_low']
    differences = []
    
    for col in indicator_columns:
        if col in df1_with_indicators.columns and col in df2_with_indicators.columns:
            try:
                # 比较非NaN值
                mask = ~(df1_with_indicators[col].isna() | df2_with_indicators[col].isna())
                if mask.any():
                    diff = abs(df1_with_indicators[col][mask] - df2_with_indicators[col][mask])
                    max_diff = diff.max()
                    if max_diff > 1e-10:  # 极小的容差
                        differences.append({
                            'indicator': col,
                            'max_difference': max_diff,
                            'mean_difference': diff.mean()
                        })
                        print(f"   ❌ {col}: 最大差异 {max_diff:.2e}")
                    else:
                        print(f"   ✅ {col}: 一致")
            except Exception as e:
                differences.append({
                    'indicator': col,
                    'error': str(e)
                })
                print(f"   🔥 {col}: 计算错误 - {e}")
    
    return len(differences) == 0

def test_strategy_consistency():
    """测试策略信号的一致性"""
    print("\n🎯 测试策略信号一致性...")
    
    # 获取测试数据
    df = create_test_data()
    df = TechnicalIndicators.add_all_indicators(df)
    
    # 获取可用策略
    try:
        strategies = StrategyFactory.get_all_strategies()
        if not strategies:
            print("   ⚠️ 没有找到可用策略")
            return False
    except Exception as e:
        print(f"   🔥 获取策略失败: {e}")
        return False
    
    all_consistent = True
    
    for strategy in strategies[:3]:  # 测试前3个策略
        try:
            print(f"   测试策略: {strategy.name}")
            
            # 生成两次信号（使用相同数据）
            df1 = strategy.generate_signals(df.copy())
            df2 = strategy.generate_signals(df.copy())
            
            if 'signal' not in df1.columns or 'signal' not in df2.columns:
                print(f"     ⚠️ 策略未生成signal列")
                continue
            
            # 比较信号
            signal_diff = df1['signal'] != df2['signal']
            different_count = signal_diff.sum()
            
            if different_count > 0:
                print(f"     ❌ 发现 {different_count} 个信号差异")
                # 显示一些差异样本
                diff_indices = signal_diff[signal_diff].index[:5]
                for idx in diff_indices:
                    print(f"        索引 {idx}: {df1.loc[idx, 'signal']} vs {df2.loc[idx, 'signal']}")
                all_consistent = False
            else:
                print(f"     ✅ 信号完全一致")
                
        except Exception as e:
            print(f"     🔥 策略测试失败: {e}")
            all_consistent = False
    
    return all_consistent

def test_data_processing_consistency():
    """测试数据处理的一致性"""
    print("\n📊 测试数据处理一致性...")
    
    # 创建原始OHLCV数据（列表格式，模拟API返回）
    np.random.seed(42)
    raw_data = []
    
    for i in range(100):
        timestamp = 1640995200 + i * 300  # 5分钟间隔
        base_price = 0.001 + i * 0.00001
        raw_data.append({
            'unixTime': timestamp,
            'o': base_price * (1 + np.random.uniform(-0.01, 0.01)),
            'h': base_price * (1 + np.random.uniform(0, 0.02)),
            'l': base_price * (1 - np.random.uniform(0, 0.02)),
            'c': base_price,
            'v': np.random.uniform(1000, 10000)
        })
    
    # 模拟两个组件的数据处理流程
    def process_data(data):
        df = pd.DataFrame(data)
        df.rename(columns={'unixTime': 'timestamp', 'o': 'open', 'h': 'high', 
                          'l': 'low', 'c': 'close', 'v': 'volume'}, inplace=True)
        df['datetime_pd'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.set_index('datetime_pd')
        return df
    
    # 处理两次
    df1 = process_data(raw_data.copy())
    df2 = process_data(raw_data.copy())
    
    # 比较结果
    differences = []
    for col in ['open', 'high', 'low', 'close', 'volume']:
        if not df1[col].equals(df2[col]):
            differences.append(col)
            print(f"   ❌ {col}: 数据不一致")
        else:
            print(f"   ✅ {col}: 数据一致")
    
    return len(differences) == 0

def main():
    """主测试函数"""
    print("🧪 开始快速信号一致性测试")
    print("="*50)
    
    test_results = []
    
    # 测试1: 技术指标一致性
    try:
        indicators_ok = test_indicators_consistency()
        test_results.append(("技术指标", indicators_ok))
    except Exception as e:
        print(f"🔥 技术指标测试失败: {e}")
        test_results.append(("技术指标", False))
    
    # 测试2: 策略信号一致性
    try:
        strategy_ok = test_strategy_consistency()
        test_results.append(("策略信号", strategy_ok))
    except Exception as e:
        print(f"🔥 策略信号测试失败: {e}")
        test_results.append(("策略信号", False))
    
    # 测试3: 数据处理一致性
    try:
        data_ok = test_data_processing_consistency()
        test_results.append(("数据处理", data_ok))
    except Exception as e:
        print(f"🔥 数据处理测试失败: {e}")
        test_results.append(("数据处理", False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📋 测试结果汇总:")
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    
    if all_passed:
        print("\n🎉 恭喜！基础一致性测试全部通过")
        print("💡 建议: 现在可以运行完整的 test_signal_consistency.py 进行更全面的测试")
    else:
        print("\n⚠️ 警告: 发现一致性问题")
        print("💡 建议: 在使用HeadlessChartWidget之前需要先解决这些问题")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    print(f"\n退出码: {0 if success else 1}") 