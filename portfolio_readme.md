# Portfolio 钱包管理功能

## 概述

Portfolio 功能允许您查看Solana钱包的代币余额和总估值。它使用OKX DEX本地API服务来获取实时的钱包数据。

## 前置要求

1. **OKX DEX本地API服务**：必须在 `http://localhost:9527` 运行
2. **有效的Solana钱包地址**：44个字符的base58编码地址

## 功能特性

- 📊 **钱包总估值显示**：显示所有代币的USD总价值
- 🪙 **代币余额详情**：列出每个代币的名称、符号、余额、价格和价值
- 🔄 **实时刷新**：手动刷新或开启自动刷新功能
- 🌐 **多链支持**：支持Solana、Ethereum、BSC、Polygon等
- ⚠️ **地址验证**：自动验证钱包地址格式

## 使用方法

### 1. 启动Portfolio界面

在主程序中点击 "Portfolio" 标签页。

### 2. 输入钱包地址

在"钱包地址"输入框中输入您的Solana钱包地址：
- 地址必须是44个字符长
- 使用base58编码（不包含0、O、I、l字符）
- 示例：`EHo5NW4NZe8QKFVLKVFj1zbMH1dUWLjmYhCTqU5mRtzM`

### 3. 选择区块链

从下拉菜单中选择区块链：
- Solana (501) - 默认选择
- Ethereum (1)
- BSC (56) 
- Polygon (137)

### 4. 刷新余额

点击"刷新余额"按钮获取最新数据。

### 5. 自动刷新（可选）

点击"自动刷新"按钮开启定时刷新功能，默认每30秒刷新一次。

## 显示信息

### 钱包总估值
- 显示所有代币的美元总价值
- API状态指示器（正常/异常）
- 最后更新时间

### 代币余额表格
| 列名 | 说明 |
|------|------|
| 代币 | 代币全名 |
| 符号 | 代币符号（如SOL、USDC） |
| 余额 | 持有数量 |
| 价格(USD) | 单价（美元） |
| 价值(USD) | 总价值（美元） |
| 合约地址 | 代币合约地址 |

## 常见问题

### 1. "没有列出代币"

**可能原因：**
- 钱包地址格式错误
- API服务未运行
- 钱包中没有代币
- API限制（Too Many Requests）

**解决方法：**
- 检查钱包地址是否为44个字符的有效Solana地址
- 确保OKX DEX API服务在 `http://localhost:9527` 运行
- 等待几秒后重试（避免API限制）

### 2. "地址格式错误"

**解决方法：**
- 确保地址长度为44个字符
- 检查地址是否包含无效字符（0、O、I、l）
- 从钱包应用复制完整地址

### 3. "API连接失败"

**解决方法：**
- 检查OKX DEX本地API服务是否运行
- 确认端口9527未被其他应用占用
- 检查防火墙设置

### 4. "API请求频率限制"

**解决方法：**
- 等待10-30秒后重试
- 关闭自动刷新功能
- 减少手动刷新频率

## 配置文件

在 `config.py` 中的 `PORTFOLIO_CONFIG` 部分可以配置：

```python
PORTFOLIO_CONFIG = {
    "okx_dex_api_url": "https://okx-local-api.vercel.app",  # API地址
    "default_wallet_address": "您的钱包地址",     # 默认地址
    "refresh_interval": 30000,                   # 刷新间隔（毫秒）
}
```

## 测试功能

运行测试脚本验证功能：

```bash
# 测试Portfolio组件
python test_portfolio.py

# 测试API响应
python test_portfolio_api.py
```

## 安全提示

- 钱包地址是公开信息，查看余额是安全的
- Portfolio功能仅读取余额，不执行任何交易
- 请确保OKX DEX API服务来源可信

## 技术支持

如有问题，请检查：
1. 控制台日志输出
2. API服务状态
3. 网络连接
4. 钱包地址格式 