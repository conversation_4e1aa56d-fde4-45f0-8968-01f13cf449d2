# 📡 Webhook通知功能使用说明

## 功能概述

新增的Webhook功能允许在检测到新的交易信号时自动向指定的URL发送HTTP POST请求，实现信号的实时推送通知。

## 使用步骤

### 1. 访问设置面板

在"未处理消息"面板中，点击"⚙️ 设置"按钮打开Webhook配置对话框。

### 2. 配置Webhook

- **启用Webhook通知**: 勾选此选项以启用功能
- **Webhook URL**: 输入接收通知的服务器地址，例如：`http://localhost:5000/webhook`
- **请求超时**: 设置HTTP请求的超时时间（1-60秒）
- **自定义请求头**: 可以添加自定义HTTP头（JSON格式），例如API密钥

### 3. 测试连接

点击"🧪 测试Webhook"按钮可以发送测试请求验证配置是否正确。

### 4. 保存设置

点击"💾 保存"按钮保存配置，设置会自动保存到`webhook_settings.json`文件。

## 数据格式

当有新信号时，系统会向配置的URL发送POST请求，请求体包含以下数据：

```json
{
  "timestamp": 1640995200,
  "token_symbol": "SOL",
  "token_address": "So11111111111111111111111111111111111111112",
  "signal_type": "buy",
  "price": 1.234567,
  "strategy_name": "VWAP 交叉策略",
  "source": "background_chart",
  "chart_index": 1,
  "unique_key": "So11111111111111111111111111111111111111112_buy_1640995200",
  "test": false
}
```

### 字段说明

- `timestamp`: Unix时间戳，信号发生的时间
- `token_symbol`: 代币符号
- `token_address`: 代币合约地址
- `signal_type`: 信号类型（"buy" 或 "sell"）
- `price`: 信号发生时的价格
- `strategy_name`: 触发信号的策略名称
- `source`: 信号来源（"background_chart" 或 "strategy_analysis"）
- `chart_index`: 图表索引（用于定位具体图表）
- `unique_key`: 信号的唯一标识符
- `test`: 是否为测试请求（测试时为true）

## 测试服务器

提供了一个简单的测试服务器 `test_webhook_server.py`，用于接收和显示webhook请求：

### 启动测试服务器

```bash
# 安装Flask依赖
pip install flask

# 启动测试服务器
python test_webhook_server.py
```

### 测试URL

- Webhook接收地址: `http://localhost:5000/webhook`
- 健康检查: `http://localhost:5000/health`

## 自定义请求头示例

可以在设置中添加自定义请求头，格式为JSON：

```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer your-api-key",
  "X-Custom-Header": "custom-value"
}
```

## 错误处理

- 请求超时: 系统会记录超时错误，不会影响正常交易
- 网络错误: 系统会记录网络错误，信号处理继续进行
- 服务器错误: 系统会记录服务器响应错误

## 安全建议

1. **使用HTTPS**: 生产环境建议使用HTTPS加密传输
2. **API密钥**: 通过自定义请求头添加身份验证
3. **IP白名单**: 在接收服务器端设置IP白名单
4. **数据验证**: 接收端应验证数据格式和内容的有效性

## 常见问题

### Q: Webhook没有发送怎么办？

A: 检查以下项目：
- 确认已启用Webhook功能
- 检查URL格式是否正确
- 验证网络连接
- 查看控制台日志错误信息

### Q: 如何确认Webhook是否成功发送？

A: 系统会在控制台打印发送结果：
- ✅ 成功: 显示"Webhook发送成功"
- ❌ 失败: 显示具体的错误信息

### Q: 可以设置多个Webhook URL吗？

A: 当前版本只支持一个URL，如需多个接收端，建议在服务器端进行转发。

## 示例代码

### Python Flask接收端

```python
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def webhook():
    data = request.get_json()
    
    # 处理信号数据
    print(f"收到信号: {data['token_symbol']} - {data['signal_type']}")
    
    # 这里可以添加你的业务逻辑
    # 例如: 发送邮件、推送通知、记录数据库等
    
    return jsonify({"status": "success"}), 200

if __name__ == '__main__':
    app.run(port=5000)
```

### Node.js Express接收端

```javascript
const express = require('express');
const app = express();

app.use(express.json());

app.post('/webhook', (req, res) => {
    const data = req.body;
    
    // 处理信号数据
    console.log(`收到信号: ${data.token_symbol} - ${data.signal_type}`);
    
    // 这里可以添加你的业务逻辑
    
    res.json({ status: 'success' });
});

app.listen(5000, () => {
    console.log('Webhook服务器运行在端口5000');
});
``` 