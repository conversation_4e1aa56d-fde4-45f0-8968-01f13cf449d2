#!/usr/bin/env python3
"""
颜色对比度测试 - 验证新版持仓面板的深色配色
"""
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class ColorContrastTest(QWidget):
    """颜色对比度测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("颜色对比度测试 - 深色主题持仓面板")
        self.setGeometry(200, 200, 800, 600)
        
        # 应用深色主题样式
        self.setStyleSheet("""
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Microsoft YaHei', 'SimHei', <PERSON>l, sans-serif;
                font-size: 12px;
            }
            QLabel {
                color: #ffffff;
                background-color: transparent;
                padding: 5px;
            }
        """)
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("深色主题颜色对比度测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: #61dafb;")
        layout.addWidget(title)
        
        # 状态标签测试
        status_layout = QHBoxLayout()
        
        normal_label = QLabel("普通文字 (#ffffff)")
        normal_label.setStyleSheet("color: #ffffff;")
        status_layout.addWidget(normal_label)
        
        success_label = QLabel("成功/价值 (#4ade80)")
        success_label.setStyleSheet("color: #4ade80; font-weight: bold;")
        status_layout.addWidget(success_label)
        
        error_label = QLabel("错误/风险 (#f87171)")
        error_label.setStyleSheet("color: #f87171; font-weight: bold;")
        status_layout.addWidget(error_label)
        
        warning_label = QLabel("警告/持有 (#fbbf24)")
        warning_label.setStyleSheet("color: #fbbf24; font-weight: bold;")
        status_layout.addWidget(warning_label)
        
        gray_label = QLabel("观察/时间 (#9ca3af)")
        gray_label.setStyleSheet("color: #9ca3af;")
        status_layout.addWidget(gray_label)
        
        layout.addLayout(status_layout)
        
        # 测试表格
        self.test_table = QTableWidget()
        self.test_table.setColumnCount(6)
        self.test_table.setHorizontalHeaderLabels(["编号", "代币", "价值", "策略", "变化", "风险"])
        self.test_table.setRowCount(5)
        
        # 应用深色表格样式
        self.test_table.setStyleSheet("""
            QTableWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 2px solid #404040;
                gridline-color: #404040;
                selection-background-color: #374151;
                selection-color: #61dafb;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #404040;
                border-right: 1px solid #404040;
            }
            QTableWidget::item:selected {
                background-color: #374151;
                color: #61dafb;
            }
            QTableWidget::item:hover {
                background-color: #2d2d2d;
            }
            QHeaderView::section {
                background-color: #111827;
                color: #ffffff;
                padding: 10px;
                border: 1px solid #374151;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:hover {
                background-color: #1f2937;
            }
        """)
        
        # 填充测试数据
        test_data = [
            ("SOL", "$235.67", "买入", "+5.23%", "✅"),
            ("BTC", "$42,150.89", "卖出", "-2.14%", "✅"),
            ("ETH", "$3,245.12", "持有", "+1.87%", "✅"),
            ("DOGE", "$0.0823", "观察", "-0.45%", "⚠️"),
            ("SCAM", "$0.0001", "--", "--", "⚠️")
        ]
        
        for row, (symbol, value, strategy, change, risk) in enumerate(test_data):
            # 编号
            number_item = QTableWidgetItem(str(row + 1))
            number_item.setTextAlignment(Qt.AlignCenter)
            self.test_table.setItem(row, 0, number_item)
            
            # 代币
            self.test_table.setItem(row, 1, QTableWidgetItem(symbol))
            
            # 价值 (亮绿色)
            value_item = QTableWidgetItem(value)
            value_item.setForeground(QColor("#4ade80"))
            self.test_table.setItem(row, 2, value_item)
            
            # 策略
            strategy_item = QTableWidgetItem(strategy)
            if strategy == "买入":
                strategy_item.setForeground(QColor("#4ade80"))  # 亮绿色
            elif strategy == "卖出":
                strategy_item.setForeground(QColor("#f87171"))  # 亮红色
            elif strategy == "持有":
                strategy_item.setForeground(QColor("#fbbf24"))  # 亮黄色
            elif strategy == "观察":
                strategy_item.setForeground(QColor("#9ca3af"))  # 亮灰色
            self.test_table.setItem(row, 3, strategy_item)
            
            # 变化
            change_item = QTableWidgetItem(change)
            if change.startswith("+"):
                change_item.setForeground(QColor("#4ade80"))
            elif change.startswith("-"):
                change_item.setForeground(QColor("#f87171"))
            self.test_table.setItem(row, 4, change_item)
            
            # 风险
            risk_item = QTableWidgetItem(risk)
            if risk == "⚠️":
                risk_item.setForeground(QColor("#f87171"))
            else:
                risk_item.setForeground(QColor("#4ade80"))
            self.test_table.setItem(row, 5, risk_item)
        
        layout.addWidget(self.test_table)
        
        # 说明
        info_label = QLabel("""
深色主题颜色说明:
• 亮绿色 (#4ade80): 买入信号、正向变化、安全代币
• 亮红色 (#f87171): 卖出信号、负向变化、风险代币  
• 亮黄色 (#fbbf24): 持有信号
• 亮灰色 (#9ca3af): 观察信号、时间信息
• 白色 (#ffffff): 普通文字
• 亮蓝色 (#61dafb): 强调色、按钮色、选中色

背景: 深灰色 (#1e1e1e) - 护眼深色主题，高对比度显示
边框: 中灰色 (#404040) - 清晰的元素分割
        """)
        info_label.setStyleSheet("background-color: #111827; padding: 10px; border-radius: 5px; margin: 10px; border: 1px solid #374151;")
        layout.addWidget(info_label)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = ColorContrastTest()
    window.show()
    
    print("🌙 深色主题颜色对比度测试窗口已启动")
    print("请检查:")
    print("1. 所有文字在深色背景下是否清晰可见")
    print("2. 颜色对比度是否足够高")
    print("3. 深色主题表格样式是否美观")
    print("4. 不同策略信号颜色是否容易区分")
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 