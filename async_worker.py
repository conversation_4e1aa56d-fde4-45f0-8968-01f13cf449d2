"""
异步工作线程模块 - 处理耗时操作，避免阻塞主线程
包含线程池管理和任务优先级功能
"""

import time
import logging
import queue
import threading
from typing import Dict, Any, Callable, List, Optional
from enum import Enum

from PyQt5.QtCore import QObject, QThread, pyqtSignal, pyqtSlot, QMutex, QWaitCondition

# 配置日志
logger = logging.getLogger('async_worker')

# 任务优先级定义
class TaskPriority(Enum):
    """
    任务优先级定义
    
    - HIGH: 高优先级，用于用户交互相关的操作
    - NORMAL: 正常优先级，用于一般操作
    - LOW: 低优先级，用于后台操作
    """
    HIGH = 0
    NORMAL = 1
    LOW = 2

class Task:
    """任务类，包含任务信息和回调函数"""
    
    def __init__(self, func, args=None, kwargs=None, on_result=None, on_error=None, 
                 on_finished=None, on_progress=None, priority=TaskPriority.NORMAL):
        """
        初始化任务
        
        参数:
            func (Callable): 要执行的函数
            args (tuple): 传递给函数的位置参数
            kwargs (dict): 传递给函数的关键字参数
            on_result (Callable): 结果回调函数
            on_error (Callable): 错误回调函数
            on_finished (Callable): 完成回调函数
            on_progress (Callable): 进度回调函数
            priority (TaskPriority): 任务优先级
        """
        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.on_result = on_result
        self.on_error = on_error
        self.on_finished = on_finished
        self.on_progress = on_progress
        self.priority = priority
        self.id = id(self)  # 任务唯一ID


class Worker(QObject):
    """工作线程类，用于执行耗时操作"""
    
    # 信号定义
    finished = pyqtSignal(int)  # 任务完成信号，参数为任务ID
    error = pyqtSignal(int, str)  # 错误信号，参数为任务ID和错误信息
    result = pyqtSignal(int, object)  # 结果信号，参数为任务ID和结果
    progress = pyqtSignal(int, int)  # 进度信号，参数为任务ID和进度值
    ready_for_task = pyqtSignal()  # 准备好执行任务的信号
    
    def __init__(self, task_queue, worker_id):
        """
        初始化工作线程
        
        参数:
            task_queue (queue.PriorityQueue): 任务队列
            worker_id (int): 工作线程 ID
        """
        super().__init__()
        self.task_queue = task_queue
        self.worker_id = worker_id
        self.running = True
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        self.current_task = None
        
    @pyqtSlot()
    def run(self):
        """执行任务循环"""
        logger.debug(f"Worker {self.worker_id} 已启动")
        
        while self.running:
            try:
                # 发送准备好执行任务的信号
                self.ready_for_task.emit()
                
                # 从队列中获取任务
                priority, task = self.task_queue.get(block=True, timeout=0.5)
                self.current_task = task
                
                logger.debug(f"Worker {self.worker_id} 正在执行任务 {task.id} (优先级: {priority})")
                
                try:
                    # 发送进度信号
                    self.progress.emit(task.id, 10)
                    
                    # 执行函数
                    result = task.func(*task.args, **task.kwargs)
                    
                    # 发送进度信号
                    self.progress.emit(task.id, 100)
                    
                    # 发送结果信号
                    self.result.emit(task.id, result)
                    
                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"Worker {self.worker_id} 执行任务 {task.id} 时出错: {error_msg}")
                    self.error.emit(task.id, error_msg)
                finally:
                    # 发送完成信号
                    self.finished.emit(task.id)
                    
                    # 标记任务完成
                    self.task_queue.task_done()
                    self.current_task = None
                    
            except queue.Empty:
                # 队列为空，等待新任务
                self.mutex.lock()
                if self.running:
                    self.condition.wait(self.mutex, 500)  # 等待500毫秒
                self.mutex.unlock()
            except Exception as e:
                logger.error(f"Worker {self.worker_id} 循环中出错: {str(e)}")
        
        logger.debug(f"Worker {self.worker_id} 已停止")
            
    def stop(self):
        """停止工作线程"""
        self.running = False
        self.mutex.lock()
        self.condition.wakeAll()
        self.mutex.unlock()
        
    def wake(self):
        """唤醒工作线程"""
        self.mutex.lock()
        self.condition.wakeAll()
        self.mutex.unlock()


class ThreadPoolManager(QObject):
    """线程池管理器，管理工作线程和任务队列"""
    
    # 单例模式
    _instance = None
    
    # 信号定义
    task_finished = pyqtSignal(int)  # 任务完成信号
    task_error = pyqtSignal(int, str)  # 任务错误信号
    task_result = pyqtSignal(int, object)  # 任务结果信号
    task_progress = pyqtSignal(int, int)  # 任务进度信号
    
    # 槽函数定义
    @pyqtSlot(int, object)
    def _execute_result_callback(self, task_id, result):
        """在主线程中执行结果回调函数"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_result:
            try:
                task.on_result(result)
            except Exception as e:
                logger.error(f"执行结果回调函数时出错: {str(e)}")
    
    @pyqtSlot(int, str)
    def _execute_error_callback(self, task_id, error_msg):
        """在主线程中执行错误回调函数"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_error:
            try:
                task.on_error(error_msg)
            except Exception as e:
                logger.error(f"执行错误回调函数时出错: {str(e)}")
    
    @pyqtSlot(int)
    def _execute_finished_callback(self, task_id):
        """在主线程中执行完成回调函数"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_finished:
            try:
                task.on_finished()
            except Exception as e:
                logger.error(f"执行完成回调函数时出错: {str(e)}")
    
    @pyqtSlot(int, int)
    def _execute_progress_callback(self, task_id, progress):
        """在主线程中执行进度回调函数"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_progress:
            try:
                task.on_progress(progress)
            except Exception as e:
                logger.error(f"执行进度回调函数时出错: {str(e)}")
    
    def __new__(cls, *args, **kwargs):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(ThreadPoolManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, max_workers=4):
        """
        初始化线程池管理器
        
        参数:
            max_workers (int): 最大工作线程数量
        """
        # 先调用父类的__init__方法
        super().__init__()
        
        # 如果已经初始化过，直接返回
        if hasattr(self, 'initialized'):
            return
            
        self.initialized = True
        self.max_workers = max_workers
        self.task_queue = queue.PriorityQueue()
        self.workers = []
        self.threads = []
        self.tasks = {}  # 任务ID到任务对象的映射
        self.mutex = QMutex()
        
        # 初始化线程池
        self._init_thread_pool()
    
    def _init_thread_pool(self):
        """初始化线程池"""
        logger.info(f"初始化线程池，创建 {self.max_workers} 个工作线程")
        
        for i in range(self.max_workers):
            # 创建线程和工作线程
            thread = QThread()
            worker = Worker(self.task_queue, i)
            
            # 将工作线程移动到线程
            worker.moveToThread(thread)
            
            # 连接信号
            thread.started.connect(worker.run)
            
            # 连接工作线程信号到管理器
            worker.finished.connect(self._on_task_finished)
            worker.error.connect(self._on_task_error)
            worker.result.connect(self._on_task_result)
            worker.progress.connect(self._on_task_progress)
            
            # 启动线程
            thread.start()
            
            # 添加到列表
            self.workers.append(worker)
            self.threads.append(thread)
            
            logger.debug(f"初始化工作线程 {i}")
    
    def add_task(self, func, on_result=None, on_error=None, on_finished=None, 
                 on_progress=None, priority=TaskPriority.NORMAL, *args, **kwargs):
        """
        添加任务到队列
        
        参数:
            func (Callable): 要执行的函数
            on_result (Callable): 结果回调函数
            on_error (Callable): 错误回调函数
            on_finished (Callable): 完成回调函数
            on_progress (Callable): 进度回调函数
            priority (TaskPriority): 任务优先级
            *args, **kwargs: 传递给函数的参数
            
        返回:
            int: 任务ID
        """
        # 创建任务
        task = Task(
            func=func,
            args=args,
            kwargs=kwargs,
            on_result=on_result,
            on_error=on_error,
            on_finished=on_finished,
            on_progress=on_progress,
            priority=priority
        )
        
        # 添加任务到字典
        self.mutex.lock()
        self.tasks[task.id] = task
        self.mutex.unlock()
        
        # 添加任务到队列
        self.task_queue.put((task.priority.value, task))
        
        # 唤醒所有工作线程
        for worker in self.workers:
            worker.wake()
        
        logger.debug(f"添加任务 {task.id} (优先级: {task.priority.name})")
        
        return task.id
    
    def _on_task_finished(self, task_id):
        """处理任务完成事件"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_finished:
            # 使用QMetaObject.invokeMethod确保回调函数在主线程中执行
            from PyQt5.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_execute_finished_callback",
                                  Qt.ConnectionType.QueuedConnection,
                                  Qt.Q_ARG(int, task_id))
        
        # 发送任务完成信号
        self.task_finished.emit(task_id)
        
        # 从字典中移除任务
        self.mutex.lock()
        if task_id in self.tasks:
            del self.tasks[task_id]
        self.mutex.unlock()
    
    def _on_task_error(self, task_id, error_msg):
        """处理任务错误事件"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_error:
            # 使用QMetaObject.invokeMethod确保回调函数在主线程中执行
            from PyQt5.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_execute_error_callback",
                                  Qt.ConnectionType.QueuedConnection,
                                  Qt.Q_ARG(int, task_id),
                                  Qt.Q_ARG(str, error_msg))
        
        # 发送任务错误信号
        self.task_error.emit(task_id, error_msg)
    
    def _on_task_result(self, task_id, result):
        """处理任务结果事件"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_result:
            # 使用QMetaObject.invokeMethod确保回调函数在主线程中执行
            from PyQt5.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_execute_result_callback",
                                  Qt.ConnectionType.QueuedConnection,
                                  Qt.Q_ARG(int, task_id),
                                  Qt.Q_ARG(object, result))
        
        # 发送任务结果信号
        self.task_result.emit(task_id, result)
    
    def _on_task_progress(self, task_id, progress):
        """处理任务进度事件"""
        self.mutex.lock()
        task = self.tasks.get(task_id)
        self.mutex.unlock()
        
        if task and task.on_progress:
            # 使用QMetaObject.invokeMethod确保回调函数在主线程中执行
            from PyQt5.QtCore import QMetaObject, Qt
            QMetaObject.invokeMethod(self, "_execute_progress_callback",
                                  Qt.ConnectionType.QueuedConnection,
                                  Qt.Q_ARG(int, task_id),
                                  Qt.Q_ARG(int, progress))
        
        # 发送任务进度信号
        self.task_progress.emit(task_id, progress)
    
    def shutdown(self):
        """关闭线程池"""
        logger.debug("关闭线程池...")
        
        # 停止所有工作线程
        for worker in self.workers:
            worker.stop()
        
        # 等待所有线程结束
        for thread in self.threads:
            thread.quit()
            thread.wait()
        
        logger.debug("线程池已关闭")


# 创建线程池管理器实例
thread_pool = ThreadPoolManager()


class AsyncTask:
    """异步任务管理类，用于创建和管理工作线程"""
    
    @staticmethod
    def run(func, on_result=None, on_error=None, on_finished=None, on_progress=None, 
            priority=TaskPriority.NORMAL, *args, **kwargs):
        """
        运行异步任务
        
        参数:
            func (Callable): 要执行的函数
            on_result (Callable): 结果回调函数
            on_error (Callable): 错误回调函数
            on_finished (Callable): 完成回调函数
            on_progress (Callable): 进度回调函数
            priority (TaskPriority): 任务优先级
            *args, **kwargs: 传递给函数的参数
            
        返回:
            int: 任务ID
        """
        # 使用线程池添加任务
        return thread_pool.add_task(
            func=func,
            on_result=on_result,
            on_error=on_error,
            on_finished=on_finished,
            on_progress=on_progress,
            priority=priority,
            *args,
            **kwargs
        )
