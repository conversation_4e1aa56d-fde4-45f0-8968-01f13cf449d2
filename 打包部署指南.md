# TrendTrader 打包部署指南

## 概述

本指南将帮助您将 TrendTrader 应用程序打包成可在没有开发环境的用户电脑上运行的可执行文件。

## 方法一：快速打包（推荐）

### 1. 使用快速打包脚本

```bash
python quick_build.py
```

这个脚本会：
- 自动安装 PyInstaller
- 打包应用程序为单个可执行文件
- 包含所有必要的依赖

### 2. 打包结果

打包完成后，您会在 `dist/` 目录中找到：
- **Windows**: `TrendTrader.exe`
- **macOS**: `TrendTrader.app`
- **Linux**: `TrendTrader`

## 方法二：完整打包（包含安装程序）

### 1. 使用完整打包脚本

```bash
python build_package.py
```

这个脚本会创建：
- 可执行文件
- 安装脚本
- 用户说明文档
- 完整的分发包

### 2. 分发包内容

- `TrendTrader_Distribution_[系统]/`
  - 可执行文件
  - 安装脚本（`install.bat` 或 `install.sh`）
  - 说明文档（`README_DISTRIBUTION.md`）
  - 应用图标（如果有）

## 方法三：手动打包

### 1. 安装 PyInstaller

```bash
pip install pyinstaller
```

### 2. 基本打包命令

```bash
pyinstaller --onefile --windowed --name=TrendTrader main.py
```

### 3. 高级打包命令（包含所有依赖）

```bash
pyinstaller \
  --onefile \
  --windowed \
  --name=TrendTrader \
  --add-data="ui:ui" \
  --add-data="strategies:strategies" \
  --hidden-import=PyQt5.sip \
  --hidden-import=pandas \
  --hidden-import=numpy \
  --hidden-import=requests \
  --hidden-import=ta \
  --hidden-import=matplotlib \
  --hidden-import=pyqtgraph \
  --hidden-import=sqlite3 \
  main.py
```

## 打包参数说明

| 参数 | 说明 |
|------|------|
| `--onefile` | 打包成单个可执行文件 |
| `--windowed` | 隐藏控制台窗口（GUI应用） |
| `--name=TrendTrader` | 设置可执行文件名称 |
| `--add-data` | 包含数据文件和目录 |
| `--hidden-import` | 包含隐式导入的模块 |
| `--icon=icon.ico` | 设置应用图标 |

## 分发给用户

### 1. 文件准备

将以下文件打包给用户：
- 可执行文件（`TrendTrader.exe` 等）
- 配置文件模板（可选）
- 用户说明文档

### 2. 系统要求

告知用户系统要求：
- **Windows**: Windows 10 或更高版本
- **macOS**: macOS 10.14 或更高版本  
- **Linux**: 主流发行版（Ubuntu 18.04+, CentOS 7+）
- **网络**: 需要互联网连接（用于 API 调用）

### 3. 安装说明

#### Windows 用户
1. 下载 `TrendTrader.exe`
2. 双击运行
3. 如果出现安全警告，选择"仍要运行"

#### macOS 用户
1. 下载 `TrendTrader.app`
2. 拖拽到 Applications 文件夹
3. 首次运行时，右键点击选择"打开"

#### Linux 用户
1. 下载 `TrendTrader`
2. 添加执行权限：`chmod +x TrendTrader`
3. 运行：`./TrendTrader`

## 常见问题解决

### 1. 打包失败

**问题**: 缺少模块错误
```
ModuleNotFoundError: No module named 'xxx'
```

**解决**: 添加隐式导入
```bash
--hidden-import=xxx
```

**问题**: 文件路径错误
```
FileNotFoundError: [Errno 2] No such file or directory
```

**解决**: 添加数据文件
```bash
--add-data="source:destination"
```

### 2. 运行时错误

**问题**: 应用无法启动

**解决方案**:
1. 检查目标系统是否满足要求
2. 确保网络连接正常
3. 检查防病毒软件是否阻止运行

**问题**: API 连接失败

**解决方案**:
1. 检查网络连接
2. 验证 API 密钥配置
3. 检查防火墙设置

### 3. 性能优化

**减小文件大小**:
```bash
pyinstaller --onefile --windowed --exclude-module tkinter --exclude-module unittest main.py
```

**提高启动速度**:
```bash
pyinstaller --onedir --windowed main.py  # 使用目录模式而非单文件
```

## 高级配置

### 1. 创建自定义图标

1. 准备图标文件：
   - Windows: `.ico` 格式
   - macOS: `.icns` 格式
   - Linux: `.png` 格式

2. 在打包命令中添加：
```bash
--icon=path/to/icon.ico
```

### 2. 添加版本信息（Windows）

创建 `version.txt` 文件：
```
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable('040904B0', [
        StringStruct('CompanyName', 'TrendTrader'),
        StringStruct('FileDescription', '趋势交易应用'),
        StringStruct('FileVersion', '*******'),
        StringStruct('ProductName', 'TrendTrader'),
        StringStruct('ProductVersion', '*******')
      ])
    ]),
    VarFileInfo([VarStruct('Translation', [1033, 1200])])
  ]
)
```

然后添加参数：
```bash
--version-file=version.txt
```

### 3. 代码签名（可选）

#### Windows
使用 `signtool.exe` 对 `.exe` 文件进行签名

#### macOS  
使用 `codesign` 对 `.app` 进行签名

## 自动化部署

### 1. 创建 GitHub Actions 工作流

```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: Build executable
      run: python quick_build.py
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v2
      with:
        name: TrendTrader-${{ matrix.os }}
        path: dist/
```

### 2. 本地批量构建脚本

创建 `build_all.sh`：
```bash
#!/bin/bash

# 为多个平台构建
platforms=("windows" "macos" "linux")

for platform in "${platforms[@]}"; do
    echo "Building for $platform..."
    python build_package.py --platform=$platform
done

echo "All builds completed!"
```

## 总结

选择适合您需求的打包方法：

- **快速测试**: 使用 `quick_build.py`
- **正式分发**: 使用 `build_package.py`
- **自定义需求**: 手动使用 PyInstaller

打包完成后，确保在目标环境中测试应用程序，验证所有功能正常工作。 