{"total_tests": 12, "consistent_count": 0, "inconsistent_count": 12, "consistency_rate": 0.0, "inconsistent_details": [{"token": "SOL", "strategy": "MACD交叉策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "SOL", "strategy": "MACD交叉策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "SOL", "strategy": "RSI超买超卖策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "SOL", "strategy": "RSI超买超卖策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "SOL", "strategy": "抛物线SAR策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "SOL", "strategy": "抛物线SAR策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "MACD交叉策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "MACD交叉策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "RSI超买超卖策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "RSI超买超卖策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "抛物线SAR策略", "timeframe": "5m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}, {"token": "USDC", "strategy": "抛物线SAR策略", "timeframe": "15m", "error": "'APIService' object has no attribute 'get_ohlcv_data_sync'", "type": "exception"}], "test_timestamp": "2025-06-01T02:42:11.948841"}