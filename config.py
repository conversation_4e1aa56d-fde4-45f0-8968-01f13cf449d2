"""
配置文件 - 存储应用程序的所有配置参数
"""

import os
import logging
from pathlib import Path

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    # 加载.env文件
    env_path = Path('.') / '.env'
    load_dotenv(dotenv_path=env_path)
    logging.info(".env文件加载成功")
except ImportError:
    logging.warning("python-dotenv模块未安装，将不会加载.env文件")
except Exception as e:
    logging.warning(f"加载.env文件时出错: {str(e)}")

# API配置
# 从环境变量或默认值加载API密钥
BIRDEYE_API_KEY = os.environ.get("BIRDEYE_API_KEY", "")
if not BIRDEYE_API_KEY:
    logging.warning("BIRDEYE_API_KEY未设置，请在.env文件或环境变量中设置")

# API URL
TREND_API_URL = "https://token-news-roan.vercel.app/api/tokens/aggregated-data"  # 用户自定义的趋势榜单API
OHLCV_API_URL = "https://public-api.birdeye.so/defi/ohlcv"  # OHLCV API地址

# 应用程序设置
APP_NAME = "Meme币量化分析软件"
APP_VERSION = "1.0.0"
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800

# 图表设置
DEFAULT_TIMEFRAME = "1m"  # 默认时间周期
AVAILABLE_TIMEFRAMES = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
CANDLE_COLORS = {
    "up": "#26a69a",    # 上涨蜡烛颜色
    "down": "#ef5350",  # 下跌蜡烛颜色
}

# 技术指标默认参数
INDICATOR_PARAMS = {
    "MACD": {
        "fast_period": 12,
        "slow_period": 26,
        "signal_period": 9
    },
    "RSI": {
        "period": 14
    },
    "SAR": {
        "acceleration": 0.02,
        "maximum": 0.2
    }
}

# 回测设置
DEFAULT_BACKTEST_DAYS = 7  # 默认回测天数

# UI主题设置
DARK_MODE = True  # 是否使用暗色主题
THEME_COLORS = {
    "dark": {
        "background": "#121212",
        "card": "#1e1e1e",
        "text": "#ffffff",
        "accent": "#bb86fc"
    },
    "light": {
        "background": "#f5f5f5",
        "card": "#ffffff",
        "text": "#121212",
        "accent": "#6200ee"
    }
}

# 定时刷新设置
TREND_REFRESH_INTERVAL = 60000  # 趋势榜单刷新间隔（毫秒）
CHART_REFRESH_INTERVAL = 60000  # 图表刷新间隔（毫秒）

# 🔥🔥 后台监控性能优化配置
BACKGROUND_MONITORING_CONFIG = {
    "enabled": True,  # 是否启用后台监控
    "refresh_interval": 60000,  # 后台图表刷新间隔（毫秒）- 5分钟，减少API请求
    "batch_processing": {
        "enabled": True,  # 启用批处理
        "batch_size": 30,  # 每批处理30个代币（降低并发）
        "batch_delay": 200,  # 🔥 OPTIMIZED: 批次间延迟200ms（从2000ms减少）
        "item_delay": 50,  # 🔥 OPTIMIZED: 单个代币间延迟50ms（从1000ms减少）
    },
    "data_caching": {
        "enabled": True,  # 启用数据缓存
        "cache_duration": 30000,  # 缓存
        "share_cache": True,  # 多个图表共享缓存
    },
    "performance_mode": {
        "enabled": True,  # 启用性能模式
        "skip_detailed_logs": True,  # 跳过详细日志
        "minimal_signals": True,  # 最小化信号发射
        "reduce_indicator_calc": True,  # 减少指标计算
    }
}

# 交易策略设置
STRATEGY_CONFIG = {
    "max_buy_times_per_coin": 5,  # 每个币种最大买入次数
    "trade_amount": 500.0,        # 每次买入金额
}

# Portfolio 钱包配置
PORTFOLIO_CONFIG = {
    "okx_dex_api_url": "https://okx-local-api.vercel.app",  # OKX DEX 本地API地址
    "default_wallet_address": "",  # 用户可以设置默认钱包地址
    "refresh_interval": 30000,  # 自动刷新间隔（毫秒）
    "supported_chains": {
        "501": "Solana"
    },
    
    # Portfolio 监控配置
    "min_monitor_value": 1.0,  # 最小监控价值（美元），只监控价值大于此值的代币
    "max_monitor_count": 20,   # 最大监控代币数量，避免监控太多代币影响性能
    "default_strategy": "VWAP 交叉策略",  # 默认监控策略
    "default_timeframe": "1m",  # 默认监控时间周期
    "monitor_risk_tokens": False,  # 是否监控风险代币（空投代币、貔貅盘等）
    "monitor_stablecoins": False,  # 是否监控稳定币（USDC、USDT等）
    "monitor_refresh_interval": 10000,  # 监控状态更新间隔（毫秒）
}

# 历史代币缓存配置
HISTORICAL_TOKENS_CACHE_CONFIG = {
    "cache_enabled": True,  # 是否启用缓存
    "cache_max_age_seconds": 3600,  # 缓存有效期（秒），默认1小时
    "force_cache_on_network_error": True,  # 网络错误时是否强制使用过期缓存
}

# Database configuration
