#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势表格性能分析工具 - 使用 cProfile 专门分析 update_trend_table 的性能瓶颈
"""

import sys
import os
import cProfile
import pstats
import io
import time
import threading
from contextlib import contextmanager
from typing import Dict, Any, Optional, List
import argparse

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TrendTableProfiler:
    """趋势表格性能分析器"""
    
    def __init__(self):
        self.profiler = None
        self.start_time = None
        
    @contextmanager
    def profile_context(self, description: str = ""):
        """性能分析上下文管理器"""
        print(f"🎯 开始分析{description}...")
        self.start_time = time.time()
        
        # 使用 cProfile
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            yield profiler
        finally:
            profiler.disable()
            end_time = time.time()
            
            print(f"⏱️ 分析完成，耗时: {end_time - self.start_time:.2f}秒")
            self._analyze_results(profiler, description)
    
    def _analyze_results(self, profiler: cProfile.Profile, description: str):
        """分析性能结果"""
        
        # 创建字符串缓冲区来捕获输出
        s = io.StringIO()
        stats = pstats.Stats(profiler, stream=s)
        
        print("\n" + "="*80)
        print(f"📊 {description} 性能分析报告")
        print("="*80)
        
        # 1. 总体统计
        stats.print_stats(0)
        total_stats = s.getvalue()
        print("📈 总体统计:")
        for line in total_stats.split('\n')[:15]:
            if line.strip():
                print(f"   {line}")
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 2. 最耗时的函数（累积时间）
        print(f"\n🔥 最耗时的前15个函数:")
        print("-" * 80)
        stats.sort_stats('cumulative')
        stats.print_stats(15)
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 3. 调用次数最多的函数
        print(f"\n🔄 调用次数最多的前15个函数:")
        print("-" * 80)
        stats.sort_stats('calls')
        stats.print_stats(15)
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 4. 单次调用耗时最长的函数
        print(f"\n⏱️ 单次调用最耗时的前15个函数:")
        print("-" * 80)
        stats.sort_stats('tottime')
        stats.print_stats(15)
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 5. PyQt5相关的性能瓶颈
        print(f"\n🖥️ PyQt5相关函数:")
        print("-" * 80)
        stats.sort_stats('cumulative')
        stats.print_stats('PyQt5', 10)
        pyqt_stats = s.getvalue()
        if pyqt_stats.strip():
            print(pyqt_stats)
        else:
            print("   没有发现PyQt5相关的耗时函数")
        
        # 清空缓冲区
        s.seek(0)
        s.truncate(0)
        
        # 6. QTableWidget相关函数
        print(f"\n📋 QTableWidget相关函数:")
        print("-" * 80)
        stats.sort_stats('cumulative')
        stats.print_stats('QTable', 10)
        table_stats = s.getvalue()
        if table_stats.strip():
            print(table_stats)
        else:
            print("   没有发现QTableWidget相关的耗时函数")
        
        # 保存详细报告
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"trend_table_profile_{timestamp}.prof"
        profiler.dump_stats(filename)
        print(f"\n💾 详细性能数据已保存到: {filename}")
        print("   可使用以下命令查看:")
        print(f"   python -m pstats {filename}")

    def analyze_trend_table_update(self):
        """分析 update_trend_table 函数的性能"""
        print("🚀 分析趋势表格更新性能...")
        
        with self.profile_context("趋势表格更新"):
            try:
                # 导入必要的模块
                from PyQt5.QtWidgets import QApplication
                from ui.live_trading_widget import LiveTradingWidget
                
                # 创建应用
                app = QApplication(sys.argv)
                
                # 创建LiveTradingWidget
                print("🏠 创建LiveTradingWidget...")
                widget = LiveTradingWidget()
                
                # 初始化UI
                print("🎨 初始化UI...")
                widget.init_ui()
                
                # 模拟趋势数据
                print("📊 生成模拟趋势数据...")
                mock_trend_data = self._generate_mock_trend_data()
                
                # 分析 update_trend_table 函数
                print("🔍 分析 update_trend_table 函数...")
                widget.update_trend_table(mock_trend_data)
                
                print("✅ 分析完成")
                
                # 清理资源
                widget.close()
                app.quit()
                
            except Exception as e:
                print(f"❌ 分析失败: {e}")
                import traceback
                traceback.print_exc()

    def _generate_mock_trend_data(self, count: int = 30) -> List[Dict]:
        """生成模拟的趋势数据"""
        import random
        import string
        
        mock_data = []
        
        for i in range(count):
            # 生成随机代币数据
            symbol = ''.join(random.choices(string.ascii_uppercase, k=4))
            name = f"Test Token {i+1}"
            
            token_data = {
                'tokenAddress': f"{''.join(random.choices(string.ascii_lowercase + string.digits, k=44))}",
                'symbol': symbol,
                'name': name,
                'tokenImage': f"https://example.com/token_{i}.png",
                'priceChange5m': random.uniform(-50, 50),
                'marketCap': random.uniform(100000, 10000000000),
                'volume24h': random.uniform(10000, 1000000000),
                'holders': random.randint(100, 10000),
                'tweetCount': random.randint(0, 1000),
                'totalTweets': random.randint(0, 5000),
                'smartBuyStats': random.randint(0, 100),
                'vmRatio': random.uniform(0, 10)
            }
            
            mock_data.append(token_data)
        
        return mock_data

    def analyze_specific_functions(self):
        """分析趋势表格中的特定函数"""
        print("🔍 分析趋势表格中的特定性能瓶颈...")
        
        # 分析QTableWidgetItem创建的性能
        with self.profile_context("QTableWidgetItem创建"):
            self._test_table_item_creation()
        
        # 分析setCellWidget的性能
        with self.profile_context("setCellWidget操作"):
            self._test_set_cell_widget()
        
        # 分析setItem的性能
        with self.profile_context("setItem操作"):
            self._test_set_item()

    def _test_table_item_creation(self):
        """测试表格项创建的性能"""
        from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
        from PyQt5.QtGui import QColor
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        # 创建大量表格项
        items = []
        for i in range(1000):
            item = QTableWidgetItem(f"Test Item {i}")
            item.setForeground(QColor('#27ae60'))
            items.append(item)
        
        print(f"创建了 {len(items)} 个QTableWidgetItem")

    def _test_set_cell_widget(self):
        """测试setCellWidget操作的性能"""
        from PyQt5.QtWidgets import QApplication, QTableWidget, QLabel
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        table = QTableWidget(100, 5)
        
        # 测试setCellWidget的性能
        for row in range(100):
            for col in range(5):
                label = QLabel(f"Cell {row},{col}")
                table.setCellWidget(row, col, label)
        
        print("完成了 500 次setCellWidget操作")

    def _test_set_item(self):
        """测试setItem操作的性能"""
        from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
        
        app = QApplication.instance() or QApplication(sys.argv)
        
        table = QTableWidget(100, 5)
        
        # 测试setItem的性能
        for row in range(100):
            for col in range(5):
                item = QTableWidgetItem(f"Item {row},{col}")
                table.setItem(row, col, item)
        
        print("完成了 500 次setItem操作")

def print_performance_recommendations():
    """打印性能优化建议"""
    print("\n" + "="*80)
    print("💡 趋势表格性能优化建议")
    print("="*80)
    
    recommendations = [
        "1. 🔥 减少表格更新频率：避免过于频繁的数据刷新",
        "2. 📊 使用setUpdatesEnabled(False)：在批量操作时禁用UI更新",
        "3. 🚫 禁用排序：在数据更新时临时禁用setSortingEnabled(False)",
        "4. 💾 缓存表格项：重用已创建的QTableWidgetItem",
        "5. 🎯 延迟加载：只为可见行创建UI组件",
        "6. 🔄 批量操作：一次性设置多个单元格，而不是逐个设置",
        "7. 📈 数据去重：避免重复处理相同的代币数据",
        "8. ⏰ 异步处理：将耗时操作移到后台线程",
        "9. 🎨 简化UI：减少复杂的自定义组件",
        "10. 📦 数据分页：限制同时显示的行数"
    ]
    
    for rec in recommendations:
        print(f"   {rec}")
    
    print("\n🔧 具体实现建议:")
    print("   - 将MAX_DISPLAY_TOKENS进一步减少到15-20")
    print("   - 使用QTimer.singleShot延迟UI更新")
    print("   - 实现表格项的对象池（重用机制）")
    print("   - 优化TokenInfoWidget和TokenImageLabel的创建")
    print("   - 考虑使用QTreeView替代QTableWidget")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="趋势表格性能分析工具")
    parser.add_argument('--mode', choices=['full', 'functions', 'recommendations'], 
                       default='full', help='分析模式')
    
    args = parser.parse_args()
    
    print("🔬 趋势表格性能分析工具")
    print("=" * 60)
    
    profiler = TrendTableProfiler()
    
    if args.mode == 'full':
        profiler.analyze_trend_table_update()
    elif args.mode == 'functions':
        profiler.analyze_specific_functions()
    elif args.mode == 'recommendations':
        print_performance_recommendations()
        return
    
    print_performance_recommendations()
    
    print("\n✅ 分析完成!")

if __name__ == "__main__":
    main() 