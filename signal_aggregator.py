"""
策略信号聚合器 - 多进程信号收集与分发中心
支持trending和holdings窗口的策略信号聚合
"""
import logging
import time
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, asdict
import json
import os

logger = logging.getLogger(__name__)


@dataclass
class StrategySignal:
    """策略信号数据结构"""
    token_address: str
    symbol: str
    signal_type: str  # buy, sell, hold, wait
    price: float
    timestamp: int
    strategy_name: str
    source: str  # trending, holdings
    confidence: float = 0.0  # 信号置信度 0-1
    metadata: Dict = None  # 额外元数据
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        if data['metadata'] is None:
            data['metadata'] = {}
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'StrategySignal':
        """从字典创建信号对象"""
        return cls(
            token_address=data['token_address'],
            symbol=data['symbol'],
            signal_type=data['signal_type'],
            price=data['price'],
            timestamp=data['timestamp'],
            strategy_name=data['strategy_name'],
            source=data['source'],
            confidence=data.get('confidence', 0.0),
            metadata=data.get('metadata', {})
        )


class SimpleSignalAggregator:
    """简化的信号聚合器 - 使用文件系统进行进程间通信"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.signal_history = []
        self.latest_signals = {}  # token_address -> signal
        self.aggregated_signals = {}  # token_address -> aggregated_signal
        
        # 使用文件进行进程间通信
        self.signal_file = "signals_cache.json"
        self.aggregated_file = "aggregated_signals.json"
        
        # 加载现有数据
        self._load_signals_from_file()
        
        # 回调函数
        self.signal_callbacks = []
        
        logger.info("SimpleSignalAggregator: 信号聚合器初始化完成")
    
    def _load_signals_from_file(self):
        """从文件加载信号数据"""
        try:
            if os.path.exists(self.signal_file):
                with open(self.signal_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.signal_history = data.get('history', [])
                    self.latest_signals = data.get('latest', {})
                    
            if os.path.exists(self.aggregated_file):
                with open(self.aggregated_file, 'r', encoding='utf-8') as f:
                    self.aggregated_signals = json.load(f)
                    
        except Exception as e:
            logger.warning(f"加载信号文件失败: {e}")
            self.signal_history = []
            self.latest_signals = {}
            self.aggregated_signals = {}
    
    def _save_signals_to_file(self):
        """保存信号数据到文件"""
        try:
            # 保存原始信号
            with open(self.signal_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'history': self.signal_history[-self.max_history_size:],
                    'latest': self.latest_signals
                }, f, ensure_ascii=False, indent=2)
            
            # 保存聚合信号
            with open(self.aggregated_file, 'w', encoding='utf-8') as f:
                json.dump(self.aggregated_signals, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存信号文件失败: {e}")
    
    def send_signal(self, signal: StrategySignal):
        """发送策略信号到聚合器"""
        try:
            signal_dict = signal.to_dict()
            
            # 添加到历史记录
            self.signal_history.append(signal_dict)
            
            # 更新最新信号状态
            self.latest_signals[signal.token_address] = signal_dict
            
            # 进行信号聚合
            aggregated_signal = self._aggregate_signal(signal)
            
            if aggregated_signal:
                # 更新聚合信号
                self.aggregated_signals[signal.token_address] = aggregated_signal.to_dict()
                
                # 调用回调函数
                for callback in self.signal_callbacks:
                    try:
                        callback(aggregated_signal)
                    except Exception as e:
                        logger.error(f"调用信号回调失败: {e}")
                
                logger.info(f"SimpleSignalAggregator: 聚合信号 {signal.symbol} {aggregated_signal.signal_type} (来源: {signal.source})")
            
            # 保存到文件
            self._save_signals_to_file()
            
            # 清理历史数据
            self._cleanup_history()
            
        except Exception as e:
            logger.error(f"SimpleSignalAggregator: 发送信号失败: {e}")
    
    def _aggregate_signal(self, new_signal: StrategySignal) -> Optional[StrategySignal]:
        """聚合策略信号"""
        try:
            # 获取同一代币的历史信号（最近5分钟）
            recent_signals = self._get_recent_signals(
                new_signal.token_address,
                window_seconds=300
            )
            
            if not recent_signals:
                return new_signal
            
            # 信号优先级：卖出 > 买入 > 持有 > 观察
            signal_priority = {'sell': 4, 'buy': 3, 'hold': 2, 'wait': 1}
            
            # 考虑来源权重：holdings > trending
            source_weight = {'holdings': 1.2, 'trending': 1.0}
            
            # 计算加权信号强度
            weighted_signals = []
            for signal_dict in recent_signals:
                signal_obj = StrategySignal.from_dict(signal_dict)
                priority = signal_priority.get(signal_obj.signal_type, 1)
                weight = source_weight.get(signal_obj.source, 1.0)
                strength = priority * weight * (signal_obj.confidence or 0.5)
                weighted_signals.append((signal_obj, strength))
            
            # 添加新信号
            new_priority = signal_priority.get(new_signal.signal_type, 1)
            new_weight = source_weight.get(new_signal.source, 1.0)
            new_strength = new_priority * new_weight * (new_signal.confidence or 0.5)
            weighted_signals.append((new_signal, new_strength))
            
            # 选择权重最高的信号
            best_signal, best_strength = max(weighted_signals, key=lambda x: x[1])
            
            # 创建聚合信号
            aggregated_signal = StrategySignal(
                token_address=new_signal.token_address,
                symbol=new_signal.symbol,
                signal_type=best_signal.signal_type,
                price=new_signal.price,  # 使用最新价格
                timestamp=new_signal.timestamp,  # 使用最新时间
                strategy_name=f"聚合策略({len(weighted_signals)}信号)",
                source="aggregated",
                confidence=min(best_strength / 4.0, 1.0),  # 标准化置信度
                metadata={
                    'original_sources': list(set(s[0].source for s in weighted_signals)),
                    'signal_count': len(weighted_signals),
                    'best_strength': best_strength
                }
            )
            
            return aggregated_signal
            
        except Exception as e:
            logger.error(f"SimpleSignalAggregator: 信号聚合失败: {e}")
            return new_signal  # 聚合失败时返回原信号
    
    def _get_recent_signals(self, token_address: str, window_seconds: int) -> List[Dict]:
        """获取指定时间窗口内的信号"""
        current_time = int(time.time())
        cutoff_time = current_time - window_seconds
        
        recent_signals = []
        for signal_dict in self.signal_history:
            if (signal_dict['token_address'] == token_address and 
                signal_dict['timestamp'] >= cutoff_time):
                recent_signals.append(signal_dict)
        
        return recent_signals
    
    def _cleanup_history(self):
        """清理历史数据"""
        try:
            if len(self.signal_history) > self.max_history_size:
                # 保留最新的信号
                self.signal_history = self.signal_history[-self.max_history_size:]
                logger.debug(f"SimpleSignalAggregator: 清理历史信号，保留最新 {self.max_history_size} 条")
        
        except Exception as e:
            logger.error(f"SimpleSignalAggregator: 清理历史数据失败: {e}")
    
    def get_latest_signals(self) -> Dict[str, Dict]:
        """获取所有代币的最新信号"""
        return self.latest_signals.copy()
    
    def get_aggregated_signals(self) -> Dict[str, Dict]:
        """获取所有代币的聚合信号"""
        return self.aggregated_signals.copy()
    
    def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""
        try:
            total_signals = len(self.signal_history)
            unique_tokens = len(set(s['token_address'] for s in self.signal_history))
            
            # 按来源统计
            source_stats = {}
            for signal_dict in self.signal_history:
                source = signal_dict['source']
                source_stats[source] = source_stats.get(source, 0) + 1
            
            # 按信号类型统计
            signal_type_stats = {}
            for signal_dict in self.signal_history:
                signal_type = signal_dict['signal_type']
                signal_type_stats[signal_type] = signal_type_stats.get(signal_type, 0) + 1
            
            return {
                'total_signals': total_signals,
                'unique_tokens': unique_tokens,
                'source_stats': source_stats,
                'signal_type_stats': signal_type_stats,
                'aggregated_count': len(self.aggregated_signals)
            }
        
        except Exception as e:
            logger.error(f"SimpleSignalAggregator: 获取统计信息失败: {e}")
            return {}
    
    def add_callback(self, callback: Callable[[StrategySignal], None]):
        """添加信号回调函数"""
        self.signal_callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[StrategySignal], None]):
        """移除信号回调函数"""
        if callback in self.signal_callbacks:
            self.signal_callbacks.remove(callback)


# 全局信号聚合器实例
_global_aggregator = None


def get_global_aggregator() -> SimpleSignalAggregator:
    """获取全局信号聚合器实例"""
    global _global_aggregator
    if _global_aggregator is None:
        _global_aggregator = SimpleSignalAggregator()
    return _global_aggregator


def shutdown_global_aggregator():
    """关闭全局信号聚合器"""
    global _global_aggregator
    if _global_aggregator:
        try:
            _global_aggregator._save_signals_to_file()
        except Exception as e:
            logger.error(f"关闭聚合器时保存文件失败: {e}")
        _global_aggregator = None


# 向后兼容性 - 保持原始接口
SignalAggregator = SimpleSignalAggregator


# 测试用例
if __name__ == '__main__':
    import random
    import time
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # 创建聚合器
    aggregator = SimpleSignalAggregator()
    
    # 模拟发送信号
    test_signals = [
        StrategySignal("addr1", "BTC", "buy", 50000, int(time.time()), "MACD", "trending", 0.8),
        StrategySignal("addr1", "BTC", "hold", 50100, int(time.time()) + 60, "RSI", "holdings", 0.6),
        StrategySignal("addr2", "ETH", "sell", 3000, int(time.time()), "VWAP", "trending", 0.9),
    ]
    
    for signal in test_signals:
        aggregator.send_signal(signal)
        time.sleep(1)
    
    # 获取统计信息
    stats = aggregator.get_signal_statistics()
    print(f"信号统计: {stats}")
    
    # 获取聚合信号
    aggregated = aggregator.get_aggregated_signals()
    print(f"聚合信号: {aggregated}")
    
    print("测试完成") 