# TrendTrader 打包使用说明

## 快速开始

### 方法一：一键打包（最简单）

```bash
python 一键打包.py
```

这个命令会自动完成：
1. 安装 PyInstaller
2. 打包应用程序
3. 测试打包结果
4. 显示分发说明

### 方法二：分步执行

1. **打包应用程序**
   ```bash
   python quick_build.py
   ```

2. **测试打包结果**
   ```bash
   python test_package.py
   ```

3. **完整打包（包含安装程序）**
   ```bash
   python build_package.py
   ```

## 打包结果

打包完成后，您会在 `dist/` 目录中找到：

- **Windows**: `TrendTrader.exe`
- **macOS**: `TrendTrader.app`  
- **Linux**: `TrendTrader`

## 分发给用户

1. 将 `dist/` 目录中的文件复制给用户
2. 告知用户系统要求：
   - 需要网络连接
   - Windows 10+ / macOS 10.14+ / Ubuntu 18.04+
3. 提供运行说明

## 常见问题

### 打包失败
- 确保所有依赖都已安装：`pip install -r requirements.txt`
- 检查 Python 版本（推荐 3.8+）

### 运行失败
- 检查目标电脑的网络连接
- 确保防病毒软件不会阻止运行
- macOS 用户需要右键点击选择"打开"

## 技术支持

如果遇到问题，请查看：
1. `打包部署指南.md` - 详细的技术文档
2. 应用程序日志文件
3. 错误信息截图

---

**注意**: 打包后的应用程序仍需要网络连接来访问 API 服务。 